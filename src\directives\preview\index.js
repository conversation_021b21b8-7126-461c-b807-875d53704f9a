import PreviewImg from "@/components/Preview/index.vue";
import { useEventListener } from "@vueuse/core";
import { h, render } from "vue";
import { downloadFileBySaveAs } from "@/utils/index";
// interface previewEl extends HTMLElement {
//   srcValue: string;
// }
const handleDestroy = () => {
  // 从 body 上移除组件
  render(null, document.body);
};

/** 图片预览 */
export const preview = {
  // mounted(el, binding) {
  //   console.log('🍪-----el, binding-----', el, binding);
  // },
  mounted(el, binding) {
    // 添加style
    el.style.cursor = "pointer";
    const { value } = binding;
    // console.log("🦄------------------------------>", binding);
    if (value) {
      console.log("🦄------------------------------>", value);
      // el.srcValue = value;
      const { url, type, name } = value;
      const arg = binding.arg ?? "click";
      useEventListener(el, arg, () => {
        // console.log("🍭------------------------------>");
        if (type === "image") {
          const vnode = h(PreviewImg, {
            src: url,
            type: type,
            destroy: handleDestroy
          });
          render(vnode, document.body);
        } else if (type === "download") {
          downloadFileBySaveAs(url, name);
        }
      });
    } else {
      throw new Error(
        '[Directive: copy]: need value! Like v-copy="modelValue"'
      );
    }
  }
};
