// @ts-check
import antfu from "@antfu/eslint-config";

export default antfu(
  {
    unocss: true,
    formatters: true,
    ignores: [
      "**/.*",
      "dist/*",
      "*.d.ts",
      "public/*",
      "src/assets/**",
      "src/**/iconfont/**",
      "src/utils/filePreview/**"
    ]
  },
  {
    rules: {
      "style/no-tabs": "off",
      "style/quote-props": "off",
      "vue/html-indent": "off",
      "style/indent": "off",
      "node/prefer-global/process": "off",
      "node/prefer-global/buffer": "off",
      "unused-imports/no-unused-vars": "off",
      "vue/singleline-html-element-content-newline": "off",
      "no-console": "off",
      "ts/no-unused-expressions": "off",
      "regexp/no-useless-assertions": "off",
      "node/handle-callback-err": "off",
      "import/order": "off",
      "style/semi": "off",
      "no-empty-pattern": "off",
      eqeqeq: "off",
      "ts/no-unsafe-function-type": "off",
      "ts/no-use-before-define": "off",
      "vue/no-unused-refs": "off",
      "regexp/no-unused-capturing-group": "off",
      "array-callback-return": "off",
      "no-async-promise-executor": "off",
      "no-empty": "off",
      "vue/custom-event-name-casing": "off",
      "ts/no-empty-object-type": "off",
      "unicorn/prefer-dom-node-text-content": "off",
      "prefer-const": "off",
      "ts/no-this-alias": "off",
      "no-new": "off",
      "no-throw-literal": "off",
      "sort-imports": "off",
      "no-prototype-builtins": "off",
      "vue/no-template-shadow": "off",
      "vue/attribute-hyphenation": "off",
      "jsdoc/check-param-names": "off",
      "style/quotes": "off",
      "import/newline-after-import": "off",
      "ts/consistent-type-imports": "off",
      "style/member-delimiter-style": "off",
      "@typescript-eslint/no-unused-vars": "off",
      "style/comma-dangle": "off",
      "jsdoc/multiline-blocks": "off",
      "ts/consistent-type-definitions": "off",
      "vue/comma-dangle": "off",
      "vue/component-name-in-template-casing": "off",
      "style/arrow-parens": "off",
      "antfu/if-newline": "off",
      "regexp/prefer-w": "off",
      "import/first": "off",
      "vue/operator-linebreak": "off",
      "format/prettier": "off",
      "prefer-template": "off",
      "no-unneeded-ternary": "off",
      "unocss/order": "off",
      "antfu/top-level-function": "off",
      "style/brace-style": "off",
      "vue/prefer-separate-static-class": "off",
      "vue/v-slot-style": "off",
      "vue/no-useless-v-bind": "off",
      "object-shorthand": "off",
      "style/operator-linebreak": "off",
      "style/spaced-comment": "off",
      "ts/method-signature-style": "off",
      "jsdoc/check-types": "off",
      "dot-notation": "off",
      "style/jsx-one-expression-per-line": "off",
      "jsdoc/check-alignment": "off",
      "no-useless-return": "off",
      "antfu/curly": "off",
      "unicorn/no-instanceof-array": "off",
      "one-var": "off",
      "unicorn/prefer-node-protocol": "off",
      "jsdoc/no-multi-asterisks": "off",
      "unicorn/prefer-number-properties": "off",
      "regexp/no-dupe-characters-character-class": "off",
      "unused-imports/no-unused-imports": "off",
      "unicorn/prefer-includes": "off",
      "prefer-arrow-callback": "off",
      "perfectionist/sort-imports": "off",
      "perfectionist/sort-named-imports": "off",
      "no-unused-vars": "off",
      "no-undef": "off",
      "@typescript-eslint/ban-ts-comment": [
        "off",
        {
          "ts-expect-error": "allow-with-description",
          "ts-ignore": "allow-with-description",
          "ts-nocheck": "allow-with-description",
          "ts-check": "allow-with-description"
        }
      ]
    }
  }
);
