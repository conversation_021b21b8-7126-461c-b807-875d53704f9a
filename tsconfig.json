{"compilerOptions": {"target": "ESNext", "jsx": "preserve", "lib": ["ESNext", "DOM"], "experimentalDecorators": true, "baseUrl": ".", "module": "ESNext", "moduleResolution": "bundler", "paths": {"@/*": ["src/*"], "@build/*": ["build/*"]}, "resolveJsonModule": true, "types": ["node", "vite/client", "element-plus/global", "@pureadmin/table/volar"], "allowJs": false, "strict": false, "strictFunctionTypes": false, "importHelpers": true, "sourceMap": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "skipLibCheck": true}, "include": ["mock/*.ts", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "types/*.d.ts", "vite.config.js", "src/store/modules/app.js", "src/store/utils.js", "src/store/modules/settings.js", "src/store/modules/epTheme.js", "src/store/modules/multiTags.js", "src/store/modules/permission.js", "src/store/modules/user.js", "src/store/index.js", "src/utils/mitt.js", "src/layout/hooks/useNav.js", "src/layout/hooks/useTranslationLang.js", "src/layout/hooks/useBoolean.js", "src/utils/http/index.js", "src/utils/http/base.js", "src/utils/http/tool.js", "src/api/files.js", "src/api/admin.js", "src/api/mock.js", "src/api/list.js", "src/api/ossUpload.js", "src/api/routes.js", "src/api/system.js", "src/api/user.js", "src/utils/progress/index.js", "src/utils/upload/base.js", "src/utils/upload/index.js", "src/utils/getFileType.js", "src/utils/globalPolyfills.js", "src/utils/ImgBlurHash.js", "src/utils/md5.js", "src/utils/message.js", "src/utils/preventDefault.js", "src/utils/print.js", "src/utils/propTypes.js", "src/utils/sso.js", "src/utils/tree.js", "src/components/ReAnimateSelector/src/animate.js", "src/components/ReAnimateSelector/index.js", "src/components/ReAuth/index.js", "src/components/ReAuth/src/auth.jsx", "src/components/ReCountTo/src/normal/index.jsx", "src/components/ReCountTo/src/normal/props.js", "src/components/ReCountTo/index.js", "src/components/ReCountTo/src/rebound/props.js", "src/components/ReCountTo/src/rebound/index.jsx", "src/components/ReCropper/src/index.jsx", "src/components/ReCropper/index.js", "src/components/ReCropper/src/svg/index.js", "src/components/ReDialog/index.js", "src/components/ReCropperPreview/index.js", "src/components/ReFlicker/index.js", "src/components/ReFlicker/index.js", "src/components/ReIcon/index.js", "src/components/ReIcon/data.js", "src/components/ReIcon/src/types.js", "src/components/ReIcon/src/offlineIcon.js", "src/components/ReIcon/src/iconifyIconOnline.js", "src/components/ReIcon/src/iconifyIconOffline.js", "src/components/ReIcon/src/iconfont.js", "src/components/ReIcon/src/iconfont.js", "src/components/ReIcon/src/hooks.js", "src/components/ReImageVerify/src/hooks.js", "src/components/ReImageVerify/index.js", "src/components/RePerms/index.js", "src/components/RePerms/src/perms.jsx", "src/components/RePureTableBar/src/bar.jsx", "src/components/RePureTableBar/index.js", "src/components/ReQrcode/src/index.jsx", "src/components/ReSegmented/src/type.js", "src/components/ReSegmented/src/index.jsx", "src/components/ReQrcode/index.js", "src/components/ReDialog/type.js", "src/store/modules/auth.js", "src/components/ImgBlurHash.js", "src/components/ReSegmented/index.js", "src/components/ReText/index.js", "src/components/ReTypeit/index.js", "src/components/ReTypeit/src/index.jsx", "src/utils/auth/sign.js", "src/utils/auth/index.js", "src/router/index.js", "src/router/utils1.ts", "src/router/staticRouter.js", "src/router/enums.js", "src/router/modules/error.js", "src/router/modules/home.js", "src/router/modules/remaining.js", "src/plugins/i18n.ts", "src/plugins/elementPlus.js", "src/plugins/echarts.js", "src/directives/index.js", "src/directives/ripple/index.js", "src/directives/preview/index.js", "src/directives/perms/index.js", "src/directives/optimize/index.js", "src/directives/longpress/index.js", "src/directives/copy/index.js", "src/directives/auth/index.js", "src/config/sign.js", "src/config/index.js", "src/api/drafts.js"], "exclude": ["dist", "**/*.js", "node_modules"]}