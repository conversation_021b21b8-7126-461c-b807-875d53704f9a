<script setup>
import { ref } from "vue";
const props = defineProps({
  text: {
    type: String,
    default: "这里是该页面的信息填写说明以及填写建议"
  }
});
</script>

<template>
  <div class="instruction-text">
    <el-alert title="填写说明" type="primary" :closable="false">
      <template #default>
        <div>{{ text }}</div>
      </template>
    </el-alert>
  </div>
</template>

<style lang="scss" scoped>
.instruction-text {
  width: 100%;
  height: 80px;
  margin-bottom: 7px;
  font-size: 14px;
  .content {
  }
}
</style>
