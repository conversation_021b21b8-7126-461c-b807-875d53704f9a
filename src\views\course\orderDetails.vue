<script setup>
import {
  ref,
  onMounted,
  reactive,
  onBeforeMount,
  onBeforeUnmount,
  h
} from "vue";
import { useRouter, useRoute } from "vue-router";
import orderDetails from "@/views/institution/components/orderDetailsComponent.vue";
const router = useRouter();
const route = useRoute();
const form = ref({});

const formRef = ref(null);
const richFlag = ref(false);
const adminId = ref(null);
const userInfIdData = ref(null);

onMounted(() => {
  adminId.value = route.query.id;
  console.log("🎉-----route.query.id-----", route.query.id);
  richFlag.value = true;
});
</script>

<template>
  <div class="content">
    <orderDetails v-if="richFlag" :adminId="adminId" />
  </div>
</template>

<style lang="scss" scoped></style>
