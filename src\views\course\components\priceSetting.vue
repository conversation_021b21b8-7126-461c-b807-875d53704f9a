<script setup>
import { onMounted, ref } from "vue";
import { formatTime } from "@/utils/index";
import {
  CoursePeriodIdByFree,
  findSpecificationByCoursePeriodId
} from "@/api/period.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { courseStore } from "@/store/modules/course.js";
import { to } from "@iceywu/utils";
import {
  priceSettingFindByDraftId,
  findSpecificationTableByDraftId,
  findFeeItemByDraftId
} from "@/api/drafts.js";

const props = defineProps({
  draftId: {
    type: String,
    default: ""
  },
  height: {
    type: String,
    default: "100%"
  },
  coursePeriodId: {
    type: String,
    default: ""
  },
  width: {
    type: String,
    default: "100%"
  }
});
const router = useRouter();
const route = useRoute();

onMounted(() => {
  if (props.draftId === "") {
    getTableList();
    getFreeData();
  } else {
    getTableListVt();
    getFreeDataVt();
  }
  // tableData.value = Array.from({ length: 20 }).fill({
  //   completed_at: "0",
  //   pending_review: 1,
  //   createdAt: 172627952334,
  //   name: "2024-09-14习题任务",
  //   termNumber: 1,
  //   courseTypeName: "手工",
  //   state: 1,
  //   organizationName: "2024-09-14习题任务",
  //   freeze: 1
  // });
});
// 表格数据
const tableData = ref([]);
const tableTitle = ref([]);
// 退款和费用数据
const freeInfo = ref({
  freeValue: "",
  refundValue: ""
});

// 新的价格设置数据
const priceSettings = ref({
  coursePrice: "", // 课程费用
  materialPrice: "", // 材料费用
  priceType: "fixed", // 价格类型：fixed(是) 或 negotiable(否)
  priceDescription: "" // 费用说明
});
const params = ref({
  page: 1,
  size: 10,
  sort: "createdAt,desc",
  totalElements: 0
});
// 获取规格列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    coursePeriodId: route.query.periodId
  };
  // console.log("🍧-----paramsData-----", paramsData);
  const [err, result] = await requestTo(
    findSpecificationByCoursePeriodId(paramsData)
  );
  // console.log("🎁-----result--22---", result);
  if (result) {
    tableData.value = result?.content.map(item => {
      let obj = {};
      result?.headers.forEach((header, index) => {
        if (header === "价格") {
          obj[header] = item[index] + " 元";
        } else {
          obj[header] = item[index];
        }
      });
      return obj;
    });
    tableTitle.value = result?.headers.map(it => {
      return {
        label: it,
        prop: it
      };
    });
    // console.log(
    //   "🌳 tableData.value------------------------------>",
    //   tableData.value
    // );
  } else {
    console.log("没有数据");
  }
  getListLoading.value = false;
};
// 草稿箱获取规格列表信息V2
const getTableListVt = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    draftId: props.draftId
  };
  const [err, result] = await requestTo(
    findSpecificationTableByDraftId(paramsData)
  );
  if (result) {
    console.log("🎁-----result-----", result);
    tableData.value = result?.content.map(item => {
      let obj = {};
      result?.headers.forEach((header, index) => {
        if (header === "价格") {
          obj[header] = item[index] + " 元";
        } else {
          obj[header] = item[index];
        }
      });
      return obj;
    });
    tableTitle.value = result?.headers.map(it => {
      return {
        label: it,
        prop: it
      };
    });
  } else {
    console.log("没有数据");
  }
  getListLoading.value = false;
};

// 获取费用说明及退款信息
const getLoading = ref(false);
const getFreeData = async data => {
  if (getLoading.value) {
    return;
  }
  getLoading.value = true;
  let paramsData = {
    coursePeriodId: route.query.periodId
  };
  const [err, result] = await to(CoursePeriodIdByFree(paramsData));
  if (result?.code === 200) {
    freeInfo.value.freeValue = result?.data?.feeDescription;
    freeInfo.value.refundValue = result?.data?.refundPolicy;
    if (result?.data?.feeDescription) {
      courseStore().saveFreeInfo(result?.data?.feeDescription);
    } else {
      courseStore().saveFreeInfo("");
    }
    if (result?.data?.refundPolicy) {
      courseStore().saveRefundInfo(result?.data?.refundPolicy);
    } else {
      courseStore().saveRefundInfo("");
    }

    // 解析价格设置数据
    if (
      result?.data?.coursePeriodFeeItems &&
      Array.isArray(result.data.coursePeriodFeeItems)
    ) {
      result.data.coursePeriodFeeItems.forEach(item => {
        if (item.feeType === "CLASS_HOUR") {
          priceSettings.value.coursePrice =
            item.amount !== undefined && item.amount !== null
              ? item.amount
              : "";
        } else if (item.feeType === "MATERIAL") {
          priceSettings.value.materialPrice =
            item.amount !== undefined && item.amount !== null
              ? item.amount
              : "";
          // 材料费用的mandatory决定priceType
          priceSettings.value.priceType = item.mandatory
            ? "fixed"
            : "negotiable";
        }
      });
    }
  } else {
    // ElMessage.error('获取失败');
    console.log("无数据");
  }
  getLoading.value = false;
};
// 获取费用说明及退款信息V2
const getFreeDataVt = async data => {
  if (getLoading.value) {
    return;
  }
  getLoading.value = true;
  let paramsData = {
    draftId: props.draftId
  };
  const [err, result] = await to(priceSettingFindByDraftId(paramsData));
  if (result?.code === 200) {
    freeInfo.value.freeValue = result?.data?.feeDescription;
    freeInfo.value.refundValue = result?.data?.refundPolicy;
    if (result?.data?.feeDescription) {
      courseStore().saveFreeInfo(result?.data?.feeDescription);
    } else {
      courseStore().saveFreeInfo("");
    }
    if (result?.data?.refundPolicy) {
      courseStore().saveRefundInfo(result?.data?.refundPolicy);
    } else {
      courseStore().saveRefundInfo("");
    }
  } else {
    console.log("无数据");
  }

  // 获取费用项目数据
  await getFeeItemData();
  getLoading.value = false;
};

// 获取费用项目数据
const getFeeItemData = async () => {
  if (!props.draftId) return;

  const [err, result] = await requestTo(
    findFeeItemByDraftId({
      draftId: Number(props.draftId)
    })
  );

  if (result && Array.isArray(result)) {
    result.forEach(item => {
      if (item.feeType === "CLASS_HOUR") {
        priceSettings.value.coursePrice =
          item.amount !== undefined && item.amount !== null ? item.amount : "";
      } else if (item.feeType === "MATERIAL") {
        priceSettings.value.materialPrice =
          item.amount !== undefined && item.amount !== null ? item.amount : "";
        // 材料费用的mandatory决定priceType
        priceSettings.value.priceType = item.mandatory ? "fixed" : "negotiable";
      }
    });
  }
};

const text = ref("");
// 编辑价格
const editePrice = () => {
  if (tableTitle.value && tableTitle.value.length > 0) {
    text.value = "value";
  }
  router.push({
    path: "/course/currentDetails/priceEdite",
    query: {
      periodId: route.query.periodId,
      back: text.value
    }
  });
};
// 费用退款
const editeFree = (val, text) => {
  router.push({
    path: "/course/currentDetails/freeEdite",
    query: {
      type: val,
      periodId: route.query.periodId,
      text: text
    }
  });
};
</script>

<template>
  <div class="containers">
    <!-- 规格表格相关代码已注释 -->
    <!-- <div class="con_table">
      <div v-if="tableData?.length" class="table-content">
        <pure-table
          ref="tableRef"
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          table-layout="auto"
          :loading="loadingTable"
          :size="size"
          :data="tableData"
          :columns="tableTitle"
          :style="{ height: '100%' }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @drag-sort-end="handleSortEnd"
        />
      </div>
      <el-skeleton v-else-if="getListLoading" :rows="5" animated />
      <el-empty v-else description="暂无数据" class="w100% h248px" />

      <el-button
        v-if="false"
        :disabled="getListLoading"
        type="primary"
        class="editeBtn"
        @click="editePrice"
      >
        编辑价格
      </el-button>
    </div> -->

    <!-- 新的价格设置展示 -->
    <div class="price-setting-section">
      <div class="price-description-list">
        <!-- 课程费用 -->
        <div class="description-item">
          <div class="label">课程费用：</div>
          <div class="value">
            {{
              priceSettings.coursePrice !== "" &&
              priceSettings.coursePrice !== null &&
              priceSettings.coursePrice !== undefined
                ? `${priceSettings.coursePrice} 元`
                : "暂无数据"
            }}
          </div>
        </div>

        <!-- 材料费用 -->
        <div class="description-item">
          <div class="label">材料费用：</div>
          <div class="value">
            {{
              priceSettings.materialPrice !== "" &&
              priceSettings.materialPrice !== null &&
              priceSettings.materialPrice !== undefined
                ? `${priceSettings.materialPrice} 元${priceSettings.priceType === "fixed" ? "（家长必选）" : "（家长可选）"}`
                : "暂无数据"
            }}
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="free">
        <div class="title">
          <div class="text">费用说明（包含、不包含）</div>
          <el-button
            v-if="false"
            type="primary"
            @click="editeFree('free', '费用说明（包括、不包括）')"
          >
            编辑费用说明
          </el-button>
        </div>
        <div class="text">
          <el-input
            v-model="freeInfo.freeValue"
            :rows="7"
            type="textarea"
            resize="none"
            disabled
            :placeholder="!freeInfo.freeValue ? '暂无数据' : ''"
          />
        </div>
      </div>
      <div class="refund">
        <div class="title">
          <div class="text">退款政策</div>
          <el-button
            v-if="false"
            type="primary"
            @click="editeFree('refund', '退款政策')"
          >
            编辑退款政策
          </el-button>
        </div>
        <div class="text">
          <el-input
            v-model="freeInfo.refundValue"
            :rows="7"
            type="textarea"
            resize="none"
            disabled
            :placeholder="!freeInfo.refundValue ? '暂无数据' : ''"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  box-sizing: border-box;
  //   width: calc(100% - 48px);
  display: flex;
  flex-direction: column;
  height: v-bind(height);
  background: #fff;
  overflow-y: auto;

  .con_table {
    // width: calc(100% - 25px);
    margin-bottom: 20px;
    // margin-left: 25px;
    display: flex;
    flex: 1;
    min-height: 0;
    // height: 258px;
    .table-content {
      width: 100%;
      // height: 258px; // 移除
      flex: 1;
      min-height: 0;
      display: flex;
      // :deep(.el-table) {
      //   width: 97%;
      // }
      :deep(.pure-table) {
        // width: 100%;
        width: v-bind(width) !important;
        display: flex;
        flex-direction: column;
        :deep(.el-table__body-wrapper) {
          overflow-y: auto; // 确保表格内容超出时可以滚动
        }
      }
    }
    .el-empty {
      flex: 1;
    }

    .editeBtn {
      margin-top: 10px;
      align-self: flex-start;
    }
  }

  .content {
    box-sizing: border-box;
    display: flex;
    padding: 10px 0 10px 0;
    // overflow-y: auto;

    .free {
      width: 50%;
      margin-right: 40px;
    }

    .title {
      margin-bottom: 20px;
      font-size: 14px;
      color: rgb(16 16 16 / 100%);
      display: flex;
      justify-content: space-between;
    }

    .refund {
      width: 50%;
    }

    .text {
      width: 100%;
    }
  }
}
.containers::-webkit-scrollbar-track {
  background: #f1f1f1; /* 滚动条轨道背景 */
}

.containers::-webkit-scrollbar-thumb {
  background: #afafaf; /* 滚动条滑块颜色 */
}

.containers::-webkit-scrollbar-thumb:hover {
  background: #7c7c7c; /* 滚动条滑块悬停时的颜色 */
}

// 新的价格设置样式
.price-setting-section {
  margin-bottom: 20px;
  background: #fff;
  border-radius: 8px;

  .title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 20px;
  }

  .price-description-list {
    .description-item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      padding: 12px 0;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-size: 14px;
        color: #606266;
        font-weight: 500;
        min-width: 160px;
        flex-shrink: 0;
      }

      .value {
        font-size: 14px;
        color: #303133;
        flex: 1;
      }
    }
  }
}
</style>
