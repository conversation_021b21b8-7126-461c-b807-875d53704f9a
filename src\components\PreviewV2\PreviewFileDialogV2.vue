<script setup>
import { getFileType } from "@iceywu/utils";
import { onMounted, ref, computed, h } from "vue";
// Excel Component
import VueOfficeExcel from "@vue-office/excel";
import "@vue-office/excel/lib/index.css";
// Word Component
import VueOfficeDocx from "@vue-office/docx";
import "@vue-office/docx/lib/index.css";
// PDF Component
import VueOfficePdf from "@vue-office/pdf";
// PPTX Component
import VueOfficePptx from "@vue-office/pptx";

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  },
  showViewer: {
    type: Boolean,
    default: true
  }
});
const emit = defineEmits(["update:showViewer"]);
const isShow = defineModel("isShow");
const renderLoading = ref(true);
const iframeDom = src => {
  const sourceSrc = `https://view.officeapps.live.com/op/view.aspx?src=${props.data?.url}`;
  return h("iframe", {
    src: sourceSrc, // 这里要填入 iframe 加载的 URL
    width: "100%",
    height: "100%",
    frameborder: "0",
    allowfullscreen: true // 允许全屏模式
    // sandbox: "allow-same-origin allow-scripts" // 配置沙箱权限
  });
};
// 传入src，判断是否是doc
const isDoc = src => {
  return /\.(doc)$/i.test(src);
};

const showCom = computed(() => {
  const fileType = getFileType(props.data?.url);
  console.log("🐠-----fileType-----", fileType);
  switch (fileType) {
    case "excel":
      return VueOfficeExcel;
    case "document":
      if (isDoc(props.data?.url)) {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        renderLoading.value = false;
        return iframeDom;
      }
      return VueOfficeDocx;

    case "pdf":
      return VueOfficePdf;
    case "ppt":
      return VueOfficePptx;
    default:
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      renderLoading.value = false;
      return h(
        "div",
        { class: "unsupported-file-type" },
        "暂不支持该文件类型的预览"
      );
  }
});

const closeDialog = () => {
  emit("update:showViewer", false);
};
onMounted(() => {});
const renderedHandler = () => {
  renderLoading.value = false;
};
const errorHandler = () => {
  renderLoading.value = false;
};
</script>

<template>
  <el-dialog
    v-model="isShow"
    append-to-body
    width="90%"
    top="5vh"
    class="content"
    body-class="dialog-body"
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <div
      v-loading="renderLoading"
      class="preview-box"
      element-loading-text="加载中..."
    >
      <component
        :is="showCom"
        :src="data?.url"
        @rendered="renderedHandler"
        @error="errorHandler"
      />
    </div>
  </el-dialog>
</template>

<style lang="scss">
.dialog-body {
  height: 80vh;
  overflow: auto;
  padding: 20px 0;
}
</style>

<style lang="scss" scoped>
.dialog-body {
  height: 80vh;
  overflow: auto;
}
.preview-box {
  width: 100%;
  height: 100%;
  overflow: auto;
  box-sizing: border-box;
  position: relative;
}

.unsupported-file-type {
  text-align: center;
  font-size: 16px;
  color: #999;
  padding: 20px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
