<script>
import { ReDialog } from "@/components/ReDialog";
import { ElConfigProvider } from "element-plus";
import en from "element-plus/es/locale/lang/en";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import plusEn from "plus-pro-components/es/locale/lang/en";
import plusZhCn from "plus-pro-components/es/locale/lang/zh-cn";
import { defineComponent, watchEffect } from "vue";
import { useTitle, useFavicon, useLocalStorage } from "@vueuse/core";
import { getPlatformConfig } from "@/api/admin";
import { ImageThumbnail } from "@/utils/imageProxy.js";

export default defineComponent({
  name: "App",
  components: {
    [ElConfigProvider.name]: ElConfigProvider,
    ReDialog
  },
  setup() {
    const platformInfo = useLocalStorage("platformInfo", {
      platformName: "",
      platformIcon: "",
      platformLogo: "",
      platformLogoLight: "",
      platformLogoVertical: "",
      platformSlogan: ""
    });

    // 获取平台信息
    const getPlatformInfo = async () => {
      try {
        const res = await getPlatformConfig();
        // 处理图片URL，存入localStorage前先进行代理
        const processedData = {
          ...res.data,
          platformIcon: ImageThumbnail(res.data?.platformIcon, "32x")
        };
        // 更新 localStorage 中的数据
        platformInfo.value = processedData;
      } catch (error) {
        console.error("获取平台配置失败:", error);
      }
    };

    // 立即执行获取平台信息
    getPlatformInfo();

    // 监听平台信息变化，更新标题和图标
    watchEffect(() => {
      if (platformInfo.value) {
        // 设置网页标题
        useTitle(platformInfo.value.platformName || "机构端");
        // 设置网页图标
        useFavicon(
          platformInfo.value.platformIcon ||
          "data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
        );
      }
    });

    return {
      platformInfo
    };
  },
  computed: {
    currentLocale() {
      return this.$storage.locale?.locale === "zh"
        ? { ...zhCn, ...plusZhCn }
        : { ...en, ...plusEn };
    }
  }
});
</script>

<template>
  <el-config-provider :locale="currentLocale">
    <router-view />
    <ReDialog />
  </el-config-provider>
</template>
