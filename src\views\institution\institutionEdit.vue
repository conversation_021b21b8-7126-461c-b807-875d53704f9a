<script setup>
import {
  organizationFindById,
  organizationUpdate,
  verifyPhone,
  updateAdminPhone,
  institutionFindById
} from "@/api/institution";
import uploadImg from "@/assets/login/upload1.png";
import RichEditor from "@/components/Base/RichEditor.vue";
import FileItem from "@/components/PreviewV2/FileItem.vue";
import { formatTime } from "@/utils/index";
import { decrypt, encryption } from "@/utils/SM4.js";
import {
  uploadFile,
  validateFileType,
  isOverSizeLimit
} from "@/utils/upload/upload";
import { ElMessage } from "element-plus";
import { onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { Hide, View, Plus } from "@element-plus/icons-vue";
import { compareObjects, debounce } from "@iceywu/utils";
import Map from "./components/Map.vue";
import ImgPos from "@/assets/pos.png";

const router = useRouter();
const route = useRoute();
const form = ref({
  files: [],
  alias: "",
  organizationCode: "",
  // qualificationDocuments: [],
  customerServiceHotline: "",
  organizationsOn: "",
  verificationCode: "",
  createdAt: "",
  id: "",
  organizationAdmin: {
    name: "",
    account: "",
    phone: ""
  },
  legalPerson: "",
  principalContact: "",
  establishmentTime: "",
  administrativeDivision: "",
  operatingAddress: "",
  operatingAddressLongitude: "",
  operatingAddressLatitude: "",
  unifiedSocialCreditCode: "",
  schoolPermitNumber: "",
  registeredAddress: "",
  registeredAddressLongitude: "",
  registeredAddressLatitude: "",
  organizationCategory: [],
  trainingCategory: [],
  serviceScope: "",
  oneSentenceIntroduction: "",
  introduction: "",
  customerServiceHotline: "",
  logo: ""
});
const formRef = ref(null);
const richFlag = ref(false);
onMounted(() => {
  getTableList();

  // if (route.query.data) {
  //   const rawData = JSON.parse(route.query.data);
  //   form.value = rawData;
  // }
});
const formFile = ref({
  institutionLicense: [],
  qualificationDocuments: [],
  logo: [],
  video: [],
  environment: []
});
const formData = ref([
  {
    label: "机构名称",
    type: "text",
    prop: "name",
    check: true,
    placeholder: "请输入机构名称"
  },
  {
    label: "机构别名",
    type: "text",
    check: false,
    prop: "alias",
    placeholder: "请输入机构别名"
  },
  {
    label: "机构编号",
    type: "input",
    check: false,
    prop: "code",
    maxlength: 30,
    placeholder: "请输入机构编号"
  },
  {
    label: "客服热线",
    type: "input",
    check: true,
    maxlength: 11,
    prop: "customerServiceHotline",
    placeholder: "请输入客服热线"
  },
  {
    label: "组织机构代码",
    type: "input",
    check: false,
    prop: "organizationCode",
    maxlength: 20,
    placeholder: "请输入组织机构代码"
  },
  {
    label: "法定代表人",
    type: "input",
    check: true,
    maxlength: 50,
    prop: "legalPerson",
    placeholder: "请输入法定代表人"
  },
  {
    label: "负责人联系方式",
    type: "input",
    check: true,
    maxlength: 11,
    prop: "principalContact",
    placeholder: "请输入联系方式"
  },
  {
    label: "成立时间",
    type: "date",
    check: true,
    prop: "establishmentTime",
    placeholder: "请输入成立时间"
  },
  {
    label: "所属行政区域",
    type: "select",
    check: true,
    prop: "administrativeDivision",
    placeholder: "请输入行政区域",
    parentId: 381
  },
  {
    label: "经营地址",
    type: "map",
    check: true,
    prop: "operatingAddress",
    placeholder: "请选择经营地址"
  },
  {
    label: "统一社会信用代码",
    type: "input",
    check: true,
    maxlength: 20,
    prop: "unifiedSocialCreditCode",
    placeholder: "请输入统一社会信用代码"
  },
  {
    label: "办学许可证",
    type: "input",
    check: false,
    maxlength: 30,
    prop: "schoolPermitNumber",
    placeholder: "请输入办学许可证号"
  },
  {
    label: "注册地址",
    type: "map",
    check: true,
    prop: "registeredAddress",
    placeholder: "请输入注围地址"
  },
  {
    label: "机构类别",
    type: "select2",
    check: false,
    prop: "organizationCategory",
    placeholder: "请选择机构类别",
    opt: [],
    parentId: 369
  },
  {
    label: "培训类别",
    type: "select2",
    check: false,
    prop: "trainingCategory",
    placeholder: "请选择培训类别",
    opt: [],
    parentId: 370
  },
  {},
  {
    label: "服务范围",
    type: "textarea",
    check: true,
    prop: "serviceScope",
    placeholder: "请输入服务范围"
  }
]);

// 机构介绍
const institutionIntroduction = ref([
  {
    label: "机构logo",
    type: "upload",
    check: true,
    prop: "logo",
    placeholder: "请上传机构营业执照",
    width: "200px",
    limit: 1,
    text: "支持上传png、jpg、jpeg图片格式，最多上传1张，最佳尺寸:200*200px，单张图片大小不超过10MB"
  },
  {
    label: "机构简介",
    type: "textarea",
    check: true,
    prop: "oneSentenceIntroduction",
    placeholder: "请用一句话对机构进行简介",
    span: 12
    // width: "500px"
  },
  {
    label: "机构介绍",
    type: "editor",
    check: true,
    prop: "introduction",
    placeholder: "请输入机构介绍",
    width: "200px"
  },
  {
    label: "宣传视频",
    type: "upload",
    type2: "video",
    check: false,
    prop: "video",
    width: "200px",
    limit: 3,
    text: "支持上传vido等视频格式，最多上传3个，单个视频大小不超过800MB "
  },
  {
    label: "机构环境",
    type: "upload",
    prop: "environment",
    placeholder: "请上传机构营业执照",
    width: "200px",
    limit: 9,
    text: "支持上传png、jpg、jpeg图片格式，最多上传9张，最佳尺寸:200*200px，单张图片大小不超过10MB"
  }
]);
// 资质信息
const qualifications = ref([
  {
    label: "营业执照",
    type: "upload",
    check: true,
    prop: "institutionLicense",
    limit: 9,
    text: "支持上传图片及pdf文件，图片最佳尺寸：750*1334px，单张图片大小不超过10MB，文件单个大小不超过30MB "
  },
  {
    label: "资质文件",
    type: "upload",
    check: true,
    prop: "qualificationDocuments",
    limit: 9,
    text: "支持上传图片及pdf、doc、docx、ppt、text文件，图片最佳尺寸：750*1334px，单张图片大小不超过10MB，文件单个大小不超过30MB "
  }
]);

const formfootData = ref([
  {
    label: "姓名",
    type: "text",
    prop: "name",
    width: "200px"
  },
  {
    label: "账号",
    type: "text",
    prop: "account",
    placeholder: "",
    width: "200px"
  }
]);
const formfootDataTow = ref([
  {
    label: "手机号",
    type: "input",
    prop: "organizationAdmin.phone",
    check: true,
    isView: true,
    placeholder: "请输入手机号",
    // hasButton: true,
    width: "200px",
    buttonText: "获取验证码"
  }
  // {
  //   label: "验证码",
  //   type: "input",
  //   prop: "code",
  //   placeholder: "请输入验证码",
  //   width: "200px",
  //   // check: true,
  //   hasButton: true,
  //   buttonText: "获取验证码"
  // }
]);

//返回上一页
const reset = () => {
  router.go(-1);
};
// 客服热线校验
const validateServiceHotline = (rule, value, callback) => {
  if (!value) {
    callback(new Error("客服热线不能为空"));
    return;
  }

  const phoneRegex =
    /^((0\d{2,3}-?\d{7,8})|(1[3-9]\d{9})|(400-?\d{3}-?\d{4})|(400\d{7,8}))$/;
  if (!phoneRegex.test(value)) {
    callback(new Error("请输入正确的手机号、座机号或400热线"));
    return;
  }

  callback();
};

// 自定义校验方法
const validateIntroduction = (rule, value, callback) => {
  // 处理空值
  if (!value) {
    return callback(new Error("机构简介不能为空"));
  }

  // 转换HTML实体为普通字符
  let cleanValue = value
    .replace(/&nbsp;/g, " ")
    .replace(/&emsp;/g, " ")
    .replace(/&lt;/g, "<")
    .replace(/&gt;/g, ">")
    .replace(/&amp;/g, "&");

  // 移除HTML标签
  cleanValue = cleanValue.replace(/<[^>]*>/g, "").trim();

  // 处理仅包含空格或换行符的情况
  if (!cleanValue || /^\s*$/.test(cleanValue)) {
    return callback(new Error("机构简介不能为空"));
  }

  callback();
};

// 自定义电话号码校验方法
const validatePhoneNumber = async (rule, value, callback) => {
  console.log("🎁-----value-----", value);
  if (
    value == newData.value.organizationAdmin.phone ||
    value == decrypt(newData.value.organizationAdmin.phoneCt)
  ) {
    // 移除验证码字段（如果存在）
    if (formfootDataTow.value[1]?.label === "验证码") {
      formfootDataTow.value[0].hasButton = false;
      formfootDataTow.value.splice(1, 1);
    }
    callback();
    return;
  }
  const phoneRegex = /^1[3-9]\d{9}$/; // 匹配中国大陆手机号码
  if (!value) {
    callback(new Error("手机号不能为空"));
  } else if (!phoneRegex.test(value)) {
    callback(new Error("请输入有效的手机号码"));
  } else {
    console.log(
      "🍧-----decrypt(form.value.organizationAdmin.phone)-----",
      decrypt(newData.value.organizationAdmin.phoneCt)
    );

    // 注释掉添加验证码字段的逻辑，隐藏验证码UI
    // if (!formfootDataTow.value[1]) {
    //   // 往数组指定位置添加
    //   formfootDataTow.value[0].hasButton = true;
    //   formfootDataTow.value.splice(1, 0, {
    //     label: "验证码",
    //     type: "input",
    //     prop: "code",
    //     span: 1,
    //     placeholder: "请输入验证码",
    //     width: "400px"
    //     // check: true
    //   });
    // }

    if (value == decrypt(newData.value.organizationAdmin.phoneCt)) {
      callback();
    } else {
      try {
        const response = await verifyPhone({
          organizationId: form.value.id,
          phone: encryption(value)
        });

        if (response.code === 70008) {
          callback(new Error("手机号已存在"));
        } else {
          callback();
        }
      } catch (error) {
        console.log("🌈-----error-----", error);
      }
    }

    // callback();
  }
};
// 验证码
const validateCode = async (rule, value, callback) => {
  if (
    form.value.organizationAdmin.phone ===
    newData.value.organizationAdmin.phone ||
    form.value.organizationAdmin.phone ===
    decrypt(newData.value.organizationAdmin.phoneCt)
  )
    return;
  if (!value) {
    callback(new Error("验证码不能为空"));
    return;
  }
  try {
    let { code, msg } = await updateAdminPhone({
      code: form.value.code,
      phone: encryption(form.value.organizationAdmin.phone),
      adminId: newData.value.organizationAdmin.id
    });
    if (code == 30018) {
      ElMessage({
        message: msg,
        type: "error"
      });
    }
    // console.log("🐠-----code-----", code);
  } catch (error) {
    console.log("catch-----error-----", error);
  }
};

// 自定义组织机构校验方法
const validatelegalPerson = (rule, value, callback) => {
  const legalPerson = /^[\u4E00-\u9FA5]+$/;
  if (!value) {
    callback(new Error("法定代表人不能为空"));
  } else if (!legalPerson.test(value)) {
    callback(new Error("法定代表人必须是中文"));
  } else {
    callback();
  }
};
//自定义负责人联系方式校验方法
const validateprincipalContact = (rule, value, callback) => {
  if (!value) {
    callback(new Error("负责人联系方式不能为空"));
  }
  const phoneRegex =
    /^(?:0\d{2,3}-[1-9]\d{6,7}(?:-\d{1,6})?|0\d{2,3}[1-9]\d{6,7}(?:-\d{1,6})?|400-\d{4}-\d{4}|400\d{8}|1[3-9]\d{9})$/;
  if (!phoneRegex.test(value)) {
    callback(new Error("请输入正确的联系方式"));
    return;
  }

  callback();
};
// 自定义统一社会信用代码校验方法
const validateUnifiedSocialCreditCode = (rule, value, callback) => {
  const pattern = /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/;
  if (!value) {
    callback(new Error("统一社会信用代码不能为空"));
  } else if (!pattern.test(value)) {
    callback(new Error("请输入正确的统一社会信用代码"));
  } else {
    callback();
  }
};
// 自定义一句话简介校验方法
const oneSentenceIntroductions = (rule, value, callback) => {
  const cleanValue = value.replace(/\s/g, "");
  if (!cleanValue) {
    callback(new Error("简介不能为空"));
  } else if (value.length > 200) {
    callback(new Error("简介不能超过200个字"));
  } else {
    callback();
  }
};
// 服务范围校验
const serviceScopepop = (rule, value, callback) => {
  const cleanValue = value.replace(/\s/g, "");
  if (!cleanValue) {
    callback(new Error("服务范围不能为空"));
  } else if (value.length > 1000) {
    callback(new Error("服务范围不能超过1000个字"));
  } else {
    callback();
  }
};
// 机构营业执照文件校验
const institutionLicenseFile = (rule, value, callback) => {
  if (formFile.value.institutionLicense.length <= 0) {
    callback(new Error("请上传营业执照"));
  } else {
    callback();
  }
};
// 资质文件校验
const qualificationDocumentsFile = (rule, value, callback) => {
  if (formFile.value.qualificationDocuments.length <= 0) {
    callback(new Error("请上传资质文件"));
  } else {
    callback();
  }
};
// logo文件校验
const logoFile = (rule, value, callback) => {
  if (formFile.value.logo.length <= 0) {
    callback(new Error("请上传logo"));
  } else {
    callback();
  }
};

// 校验规则
const rules = ref({
  customerServiceHotline: [
    { required: true, validator: validateServiceHotline, trigger: "blur" }
  ],
  introduction: [
    { required: true, validator: validateIntroduction, trigger: "blur" }
  ],
  "organizationAdmin.phone": [
    { required: true, validator: validatePhoneNumber, trigger: "blur" }
  ],
  legalPerson: [
    { required: true, validator: validatelegalPerson, trigger: "blur" }
  ],
  principalContact: [
    { required: true, validator: validateprincipalContact, trigger: "blur" }
  ],
  establishmentTime: [
    { required: true, message: "请选择成立时间", trigger: "blur" }
  ],
  unifiedSocialCreditCode: [
    {
      required: true,
      validator: validateUnifiedSocialCreditCode,
      trigger: "blur"
    }
  ],
  oneSentenceIntroduction: [
    {
      required: true,
      validator: oneSentenceIntroductions,
      trigger: "blur"
    }
  ],
  administrativeDivision: [
    { required: true, message: "请选择行政区划", trigger: "blur" }
  ],
  serviceScope: [
    { required: true, validator: serviceScopepop, trigger: "blur" }
  ],
  operatingAddress: [
    { required: true, message: "请选择经营地址", trigger: "blur" }
  ],
  registeredAddress: [
    { required: true, message: "请选择注册地址", trigger: "blur" }
  ],
  institutionLicense: [
    { required: true, validator: institutionLicenseFile, trigger: "blur" }
  ],
  qualificationDocuments: [
    { required: true, validator: qualificationDocumentsFile, trigger: "blur" }
  ],
  logo: [{ required: true, validator: logoFile, trigger: "blur" }]
  // code: [{ required: true, validator: validateCode, trigger: "blur" }]
});
// 提交表单
const submitForm = debounce(
  () => {
    formRef.value.validate(valid => {
      if (valid) {
        console.log("表单数据:", form.value);
        onSubmit();
      } else {
        console.log("表单校验失败");
      }
    });
  },
  1000,
  { immediate: true }
);
const getListLoading = ref(false);
const onSubmit = async () => {
  if (getListLoading.value) return;
  getListLoading.value = true;
  console.log("submit!");
  form.value.files = []; // 清空文件集合
  fileData(); //文件集合处理

  let nowData = {
    id: form.value.id,
    organizationCode: form.value.organizationCode,
    files: form.value.files,
    customerServiceHotline: form.value.customerServiceHotline,
    introduction: form.value.introduction,
    // phone: encryption(form.value.organizationAdmin.phone || ""),
    phone: form.value.organizationAdmin.phone,
    oneSentenceIntroduction: form.value.oneSentenceIntroduction,
    code: form.value.code,
    name: form.value.name,

    administrativeDivision: form.value.administrativeDivision,
    operatingAddress: form.value.operatingAddress,
    operatingAddressLongitude: form.value.operatingAddressLongitude,
    operatingAddressLatitude: form.value.operatingAddressLatitude,
    schoolPermitNumber: form.value.schoolPermitNumber,
    registeredAddress: form.value.registeredAddress,
    registeredAddressLongitude: form.value.registeredAddressLongitude,
    registeredAddressLatitude: form.value.registeredAddressLatitude,
    organizationCategory: form.value.organizationCategory,
    trainingCategory: form.value.trainingCategory,
    serviceScope: form.value.serviceScope,
    unifiedSocialCreditCode: form.value.unifiedSocialCreditCode,
    legalPerson: form.value.legalPerson,
    principalContact: form.value.principalContact,
    establishmentTime: new Date(form.value.establishmentTime).getTime(),
    logo: formFile.value.logo[0]?.fileIdentifier || "",
    code: form.value.code,
    createdAt: form.value.createdAt,
    updatedAt: form.value.updatedAt,
    alias: form.value.alias
  };
  let paramsData = {};
  for (const paramsDataKey in nowData) {
    let isArray = Array.isArray(nowData[paramsDataKey]);
    if (isArray) {
      if (nowData[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = nowData[paramsDataKey];
      }
    } else {
      if (nowData[paramsDataKey]) {
        paramsData[paramsDataKey] = nowData[paramsDataKey];
      }
    }
  }
  console.log("🐠-----oldData.value-----", oldData.value);
  paramsData = compareObjects(oldData.value, paramsData);
  // console.log("🐬-----paramsData-----", paramsData);
  paramsData.id = form.value.id;
  // 判断手机号是否修改;
  // 没修改
  if (
    form.value.organizationAdmin.phone ===
    newData.value.organizationAdmin.phone ||
    form.value.organizationAdmin.phone ===
    decrypt(newData.value.organizationAdmin.phoneCt)
  ) {
    delete paramsData.phone;
  }
  // 已修改
  if (paramsData.phone) {
    paramsData.phone = encryption(paramsData.phone);
  }
  // console.log(">>>>>>>", decrypt("tanfQfbEon0LlF7gYeqz1A=="));
  console.log("🍧-----paramsData-----", paramsData);
  // if (Object.keys(paramsData).length == 0) {
  //   ElMessage({
  //     type: "warning",
  //     message: "未修改任何信息"
  //   });
  //   getListLoading.value = false;
  //   return;
  // }
  const operateLog = {
    operateLogType: "ORGANIZATIONAL_MANAGEMENT",
    operateType: "编辑了",
    operatorTarget: `“${form.value.name}”的机构信息`
  };
  // return;
  try {
    const { code, msg, data } = await organizationUpdate(
      paramsData,
      operateLog
    );
    console.log("🌈-----code, msg, data-----", code, msg, data);
    if (code === 200) {
      ElMessage({
        type: "success",
        message: "编辑成功"
      });
      router.go(-1);
    } else {
      ElMessage({
        type: "error",
        message: msg
      });
    }
  } catch (error) {
    console.log("🌈-----error-----", error);
  }
  getListLoading.value = false;
};
// 获取验证码
const getCaptcha = () => {
  // ElMessage({
  //   message: "验证码已发送",
  //   type: "success"
  // });
};
//文件处理
const fileData = () => {
  //机构营业执照
  let institutionLicense = formFile.value.institutionLicense;
  //资质文件
  let qualificationDocuments = formFile.value.qualificationDocuments;
  // 视频
  let video = formFile.value.video || [];
  // 环境照片
  let environment = formFile.value.environment || [];
  // logo
  let logo = formFile.value.logo || [];
  if (institutionLicense.length > 0) {
    setFilesFn(institutionLicense, "BUSINESS_LICENSE");
  }
  if (qualificationDocuments.length > 0) {
    setFilesFn(qualificationDocuments, "QUALIFICATION_DOCUMENT");
  }
  if (video.length > 0) {
    setFilesFn(video, "PROMOTIONAL_VIDEO");
  }
  if (environment.length > 0) {
    setFilesFn(environment, "ENVIRONMENT");
  }
  if (logo.length > 0) {
    form.value.logo = logo[0].fileIdentifier;
  }
  // 过滤掉无效项，保证 files 结构正确
  form.value.files = (form.value.files || []).filter(
    f => f && typeof f === "object" && f.fileIdentifier
  );
  // 保证 files 至少是 []
  if (!Array.isArray(form.value.files)) {
    form.value.files = [];
  }
};
const setFilesFn = (val, type) => {
  for (let index = 0; index < val.length; index++) {
    const element = val[index].fileIdentifier;
    form.value.files.push({
      fileType: type,
      fileIdentifier: element,
      sortOrder: index + 1
    });
  }
};
// 文件上传
const beforeUpload = async (file, item) => {
  // 判断是否重复
  const isDuplicate = formFile.value[item].some(
    f =>
      (f.name === file.name || f.fileName === file.name) &&
      (f.size === file.size || f.totalSize === file.size)
  );
  console.log(formFile.value[item], file);
  if (isDuplicate) {
    ElMessage.error("不能上传重复的文件");
    return false;
  }
  let fileType = [];
  switch (item) {
    case "video":
      fileType = ["video"];
      break;
    case "institutionLicense":
      fileType = ["image", "pdf"];
      break;
    case "qualificationDocuments":
      fileType = ["image", "pdf", "word", "excel", "text"];
      break;
    default:
      break;
  }
  let strData = validateFileType(file, fileType);
  let typeImage = validateFileType(file, ["image"]);
  let isSize = isOverSizeLimit(
    file,
    item === "video" ? 800 : typeImage.valid === true ? 10 : 30
  );
  if (item === "logo" || item === "environment") {
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png"];
    const fileTypes = file.type;
    if (!allowedTypes.includes(fileTypes)) {
      strData.valid = false;
      ElMessage.error("请上传jpg、jpeg、png格式的图片");
      return false;
    }
  }

  if (strData.valid === true && isSize.valid === true) {
    try {
      await uploadFile(
        file,
        progress => {
          // 构造用于 el-upload 展示的文件对象
          const currentFile = {
            name: file.name,
            uid: file.uid,
            status: progress.status || "uploading",
            percentage: progress.percent || 0
          };

          // 上传成功，补充 url 字段
          if (progress.status === "success" && progress.data?.url) {
            currentFile.url = progress.data.url;

            // 更新 ruleForm.cover，确保无 Proxy
            if (progress.data.fileIdentifier) {
              formFile.value[item] = formFile.value[item].filter(
                item => item.uid !== file.uid
              );
              formFile.value[item].push({
                fileIdentifier: progress.data.fileIdentifier,
                fileType: "PHOTO",
                uid: file.uid,
                status: "success"
              });
            }
          }

          // 失败时移除，成功/上传中则更新
          formFile.value[item] = formFile.value[item].filter(
            f => f.uid !== file.uid
          );
          if (progress.status === "fail") {
            ElMessage.error(progress.errMessage || "上传失败，请重试");
          } else {
            // 用浅拷贝，避免 Proxy
            formFile.value[item] = [
              ...formFile.value[item],
              { ...currentFile }
            ];
          }
        },
        fileType
      );
      // if (code === 200) {
      //   formFile.value[item].push({
      //     ...data,
      //     name: data.name || data.fileName || file.name,
      //     url: data.url || data.fileUrl || "",
      //     uid: data.uploadId || Date.now() + Math.random(),
      //     status: "success" // 关键
      //   });
      // }
    } catch (error) {
      ElMessage({
        message: error.message,
        type: "error"
      });
    }
  } else {
    if (strData.message) {
      ElMessage.error(strData.message);
    }
    if (isSize.message) {
      ElMessage.error(isSize.message);
    }
  }
};
const dialogVisible = ref(false);
const dialogImageUrl = ref("");
const handlePreview = (file, prop) => {
  dialogImageUrl.value = file.url;
  dialogVisible.value = true;
};
const handleExceed = (files, item, limit) => {
  switch (item) {
    case "logo":
      return ElMessage({
        message: "当前限制上传一张图片",
        type: "error"
      });
    case "video":
      return ElMessage({
        message: "当前限制上传三个视频",
        type: "error"
      });
    case "environment":
      return ElMessage({
        message: "当前限制上传九张图片",
        type: "error"
      });
    case "institutionLicense":
    case "qualificationDocuments":
      return ElMessage({
        message: "当前限制上传九个文件",
        type: "error"
      });
    default:
      break;
  }
};
const imageAndDocs = val => {
  const qualification = [
    // 图片类型
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/bmp",
    "image/webp",
    // PDF
    "application/pdf",
    ".pdf",
    // Word文档
    "application/msword",
    ".doc",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ".docx",
    // PowerPoint文档
    "application/vnd.ms-powerpoint",
    ".ppt",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    ".pptx",
    // Excel文档
    "application/vnd.ms-excel",
    ".xls",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    ".xlsx"
  ].join(",");
  const institution = [
    // 图片类型（扩展名+MIME类型）
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/bmp",
    "image/webp",
    // PDF
    "application/pdf",
    ".pdf"
  ].join(",");
  if (val === "institutionLicense") {
    return institution;
  } else {
    return qualification;
  }
};
//删除文件
const uploadKey = ref(Date.now());
const getDeleted = (item, index) => {
  formFile.value[item].splice(index, 1);
  uploadKey.value = Date.now(); // 关键：强制刷新
};
const newData = ref();
const oldData = ref();

const getTableList = async data => {
  // let params = { id: route.query.id };
  try {
    const { code, data, msg } = await organizationFindById();
    // console.log("🎁-----data-----", code, data, msg);
    if (code == 200) {
      form.value = data;
      form.value.files = [];

      formData.value[8].opt = [{ name: data?.administrativeDivision, id: 1 }];
      formData.value[13].opt = [{ name: data?.trainingCategory, id: 1 }];
      formData.value[14].opt = [{ name: data?.organizationCategory, id: 1 }];
      form.value.establishmentTime = formatTime(
        data?.establishmentTime,
        "YYYY-MM-DD"
      );

      // form.value.name = data?.name;
      // form.value.createdAt = data?.createdAt;
      // form.value.id = data?.id;
      // form.value.alias = data?.alias;
      // form.value.organizationCode = data?.organizationCode;
      // form.value.customerServiceHotline = data?.customerServiceHotline;
      // form.value.oneSentenceIntroduction = data?.oneSentenceIntroduction;
      // form.value.introduction = data?.introduction;
      form.value.organizationAdmin.name = data?.organizationAdmin.name;
      form.value.organizationAdmin.account =
        data?.organizationAdmin.account.split("@")[0];
      // form.value.organizationAdmin.phone = decrypt(
      //   data?.organizationAdmin.phoneCt
      // )
      form.value.organizationAdmin.phone = data?.organizationAdmin.phone;

      formFile.value.institutionLicense = [];
      formFile.value.qualificationDocuments = [];
      formFile.value.video = [];
      formFile.value.environment = [];
      formFile.value.logo = [];
      formFile.value.logo = data?.logo
        ? [{ url: data?.logo, name: "logo.png" }]
        : [];
      if (data?.fileDTOS) {
        data.fileDTOS.forEach(item => {
          if (item.fileType == "BUSINESS_LICENSE") {
            formFile.value.institutionLicense.push(item.uploadFile);
          } else if (item.fileType == "QUALIFICATION_DOCUMENT") {
            formFile.value.qualificationDocuments.push(item.uploadFile);
          } else if (item.fileType == "PROMOTIONAL_VIDEO") {
            formFile.value.video.push(item.uploadFile);
          } else if (item.fileType == "ENVIRONMENT") {
            formFile.value.environment.push(item.uploadFile);
          }
        });
      }
      newData.value = JSON.parse(JSON.stringify(data));

      //构造对比对象
      // form.value.files = []; // 清空文件集合
      fileData();
      oldData.value = {
        id: data.id,
        organizationCode: data.organizationCode,
        files: form.value.files,
        customerServiceHotline: data.customerServiceHotline,
        introduction: data.introduction,
        phone: encryption(data.organizationAdmin.phone),
        oneSentenceIntroduction: data.oneSentenceIntroduction,
        name: data.name,
        administrativeDivision: data?.administrativeDivision,
        operatingAddress: data?.operatingAddress,
        operatingAddressLongitude: data?.operatingAddressLongitude,
        operatingAddressLatitude: data?.operatingAddressLatitude,
        schoolPermitNumber: data?.schoolPermitNumber,
        registeredAddress: data?.registeredAddress,
        registeredAddressLongitude: data?.registeredAddressLongitude,
        registeredAddressLatitude: data?.registeredAddressLatitude,
        organizationCategory: data?.organizationCategory,
        trainingCategory: data?.trainingCategory,
        serviceScope: data?.serviceScope,
        unifiedSocialCreditCode: data?.unifiedSocialCreditCode,
        legalPerson: data?.legalPerson,
        principalContact: data?.principalContact,
        establishmentTime: new Date(data.establishmentTime).getTime(),
        logo: formFile.value.logo[0]?.url || "",
        code: data.code,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt,
        alias: data.alias,
        account: data.organizationAdmin.account.split("@")[0],
        adminName: data.organizationAdmin.name
      };
      richFlag.value = true;
    }
  } catch (error) {
    console.log("catch-----error-----", error);
  }
};
const isView = ref(true);
const isViewFn = () => {
  isView.value = !isView.value;
  if (isView.value) {
    form.value.organizationAdmin.phone = newData.value.organizationAdmin.phone;
  } else {
    form.value.organizationAdmin.phone = decrypt(
      newData.value.organizationAdmin.phoneCt
    );
    console.log(
      '🍪-----decrypt("MLbBtmdJJuTIUPUOwMbDvg==")-----',
      decrypt("MLbBtmdJJuTIUPUOwMbDvg==")
    );
  }
};
// 地图
const dialogTableVisible = ref(false);
const locationPostion = ref([116.3912757, 39.906217]);
const selectedLocationMap = ref({}); // 存储不同地址类型的选择位置
const teacherTimeInfo = {
  location_range: 1000
};
const handleMapConfirm = data => {
  console.log("🎉-----handleMapConfirm-----", data);
  if (addressStr.value === "经营地址") {
    form.value.operatingAddress = data.address;
    form.value.operatingAddressLatitude = data.point.lat;
    form.value.operatingAddressLongitude = data.point.lng;
    // 保存经营地址的选择位置
    selectedLocationMap.value.operatingAddress = data;
  } else if (addressStr.value === "注册地址") {
    form.value.registeredAddress = data.address;
    form.value.registeredAddressLatitude = data.point.lat;
    form.value.registeredAddressLongitude = data.point.lng;
    // 保存注册地址的选择位置
    selectedLocationMap.value.registeredAddress = data;
  }
};
// 打开地图
let addressStr = ref("");
const mapOpen = val => {
  val === "operatingAddress"
    ? (addressStr.value = "经营地址")
    : (addressStr.value = "注册地址");
  dialogTableVisible.value = true;
};
// 页面下拉列表查询
const selectOptions = async val => {
  console.log("🎁-----val-----", val);
  try {
    let { code, data } = await institutionFindById({ parentId: val.parentId });
    if (code === 200) {
      formData.value.map(item => {
        if (item.prop === val.prop) {
          item.opt = data;
          if (
            val.prop === "organizationCategory" ||
            val.prop === "trainingCategory"
          ) {
            item.opt = (Array.isArray(data) ? data : []).map(opt => ({
              value: opt.name,
              label: opt.name
            }));
          }
        }
      });
    }
    console.log("🎁-----data-----", formData.value);
  } catch (error) {
    console.log("🦄-----error-----", error);
    ElMessage({
      message: "获取失败",
      type: "error"
    });
  }
};
// 多选下拉列表
const selectOptions2 = (val, prop, item) => {
  form.value[prop] = val;
};
// 下拉选择器
const onInputSelect = async (value, prop, options) => {
  // const selectedOption = options.find(item => item.id === value);
  // // form[prop] = selectedOption?.name;
  // formData.value[10].opt = [
  //   { name: selectedOption?.name, id: selectedOption?.id }
  // ];
  // // console.log("🐠-----selectedOption-----", selectedOption);
  const selectedOption = options.find(item => item.name === value);
  form.value[prop] = selectedOption?.name || value; // 保证是 name
};
const handleRemove = (file, item) => {
  const idx = formFile.value[item].findIndex(
    i => i.url === file.url && i.uid === file.uid
  );
  if (idx !== -1) {
    formFile.value[item].splice(idx, 1);
  }
};
const disabledAfterToday = date => {
  return date.getTime() > Date.now();
};
</script>

<template>
  <div class="main">
    <el-scrollbar class="scrollbar">
      <div class="header">
        <div class="title_left">
          <p>创建时间</p>
          <p>{{ formatTime(form.createdAt, "YYYY-MM-DD HH:mm:ss") }}</p>
        </div>
        <div class="title_rigth">
          <p>机构ID</p>
          <p>{{ form.id }}</p>
        </div>
      </div>

      <el-form
        ref="formRef"
        :scroll-to-error="true"
        :model="form"
        label-width="auto"
        :rules="rules"
      >
        <div>
          <div class="title-text">基本信息</div>
          <el-descriptions title="" :column="2" border class="my-descriptions">
            <el-descriptions-item
              v-for="(item, index) in formData"
              :key="index"
              label-align="right"
              label-class-name="my-label"
              :span="item.span || 1"
            >
              <template #label>
                <span v-if="item.check" class="star">*</span>{{ item.label }}
              </template>
              <el-form-item
                :prop="item.prop"
                :inline-message="item.check"
                style="margin-bottom: 0"
                :show-message="true"
                error-placement="right"
              >
                <!-- text -->
                <template v-if="item.type === 'text'">
                  <div style="color: #a8a8a8">
                    {{
                      item.prop === "createdAt"
                        ? formatTime(form?.createdAt, "YYYY-MM-DD")
                        : item.prop === "id"
                          ? adminId
                          : form[item.prop] || "--"
                    }}
                  </div>
                </template>
                <!-- textarea输入 -->
                <template v-if="item.type === 'textarea'">
                  <el-input
                    v-model="form[item.prop]"
                    :rows="5"
                    type="textarea"
                    :maxlength="1000"
                    show-word-limit
                    :placeholder="item.placeholder"
                  />
                </template>
                <!-- 下拉框(单选) -->
                <template v-if="item.type === 'select'">
                  <el-select
                    v-model="form[item.prop]"
                    :placeholder="item.placeholder"
                    @click="selectOptions(item)"
                    @change="value => onInputSelect(value, item.prop, item.opt)"
                  >
                    <el-option
                      v-for="items in item.opt"
                      :key="items.id"
                      :label="items.name"
                      :value="items.name"
                    />
                  </el-select>
                </template>
                <!-- 下拉框（多选） -->
                <template v-if="item.type === 'select2'">
                  <el-select-v2
                    v-model="form[item.prop]"
                    :options="item.opt"
                    :placeholder="item.placeholder"
                    multiple
                    @focus="selectOptions(item)"
                    @change="
                      value => selectOptions2(value, item.prop, form[item.prop])
                    "
                  />
                </template>
                <!-- input输入 -->
                <template v-if="item.type === 'input'">
                  <el-input
                    v-model.trim="form[item.prop]"
                    :placeholder="item.placeholder"
                    :style="{ width: item.width }"
                    :maxlength="item.maxlength || 50"
                  />
                  <!-- 获取验证码 -->
                  <div class="Vacode">
                    <el-button v-if="item.hasButton" @click="getCaptcha(item)">
                      {{ item.buttonText }}
                    </el-button>
                  </div>
                </template>

                <!-- 日期选择 -->
                <template v-if="item.type === 'date'">
                  <el-date-picker
                    v-model="form[item.prop]"
                    type="date"
                    placeholder="请选择成立时间"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                    :disabled-date="disabledAfterToday"
                  />
                </template>
                <!-- 地图 -->
                <template v-if="item.type === 'map'">
                  <div class="selsect-pos">
                    <div class="cover" @click="mapOpen(item.prop)" />
                    <el-input
                      v-model="form[item.prop]"
                      class="input-part"
                      placeholder="请选择地址"
                      readonly
                    >
                      <template #suffix>
                        <div class="pos-icon">
                          <img :src="ImgPos" class="wfull h-full" alt="">
                        </div>
                      </template>
                    </el-input>
                  </div>
                </template>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>

          <div class="title-text">机构介绍</div>
          <el-descriptions :column="1" border :label-width="'200px'">
            <el-descriptions-item
              v-for="(item, index) in institutionIntroduction"
              :key="index"
              label-align="right"
              label-class-name="my-label"
              :span="item.span || 1"
            >
              <template #label>
                <span v-if="item.check" class="star">*</span>{{ item.label }}
              </template>
              <template #default>
                <div style="min-width: 335px">
                  <el-form-item
                    :prop="item.prop"
                    :inline-message="item.check"
                    style="margin-bottom: 0"
                    :show-message="true"
                    error-placement="right"
                  >
                    <!-- input输入 -->
                    <!-- <template v-if="item.type === 'input'">
                      <el-input
                        v-model.trim="form[item.prop]"
                        :placeholder="item.placeholder"
                        :style="{ width: item.width }"
                      />
                    </template> -->

                    <!-- textarea输入 -->
                    <template v-if="item.type === 'textarea'">
                      <el-input
                        v-model="form[item.prop]"
                        :rows="3"
                        type="textarea"
                        :maxlength="200"
                        show-word-limit
                        :placeholder="item.placeholder"
                      />
                    </template>

                    <!-- 富文本 -->
                    <template v-else-if="item.type === 'editor'">
                      <div style="width: 100%; border: 1px solid #e2e2e2">
                        <RichEditor v-model="form[item.prop]" height="350px" />
                      </div>
                    </template>

                    <!-- 示例：上传组件 -->
                    <template v-else-if="item.type === 'upload'">
                      <el-upload
                        :key="
                          item.prop === 'video'
                            ? uploadKey + item.prop
                            : item.prop
                        "
                        action="#"
                        :show-file-list="item.prop === 'video' ? false : true"
                        :file-list="formFile[item.prop]"
                        :http-request="() => {}"
                        :limit="item.limit"
                        :on-exceed="
                          file => handleExceed(file, item.prop, item.limit)
                        "
                        :accept="item.prop === 'video' ? 'video/*' : 'image/*'"
                        :list-type="item.type2 ? '' : 'picture-card'"
                        :before-upload="file => beforeUpload(file, item.prop)"
                        :on-remove="file => handleRemove(file, item.prop)"
                        :on-preview="file => handlePreview(file, item.prop)"
                        :class="{
                          hideUploadBtn:
                            formFile[item.prop].length >= item.limit
                        }"
                      >
                        <template v-if="item.type2 === 'video'">
                          <img :src="uploadImg" alt="">
                        </template>
                        <template v-else>
                          <el-icon><Plus /></el-icon>
                        </template>
                      </el-upload>

                      <template v-if="item.type2">
                        <template
                          v-for="(item2, index2) in formFile[item.prop]"
                          :key="index2"
                        >
                          <FileItem
                            isNeedDelte
                            :data="item2"
                            :index="index2"
                            :isTypeVideo="true"
                            style="width: 50%; min-width: 130px"
                            @delete="getDeleted(item.prop, index2)"
                          />
                        </template>
                      </template>

                      <div class="upload_text">{{ item.text }}</div>
                    </template>
                  </el-form-item>
                </div>
              </template>
            </el-descriptions-item>
          </el-descriptions>

          <div class="title-text">资质信息</div>
          <el-descriptions :column="1" border :label-width="'200px'">
            <el-descriptions-item
              v-for="(item, index) in qualifications"
              :key="index"
              label-align="right"
              label-class-name="my-label"
            >
              <template #label>
                <span v-if="item.check" class="star">*</span>{{ item.label }}
              </template>
              <template #default>
                <div style="min-width: 335px">
                  <el-form-item
                    :prop="item.prop"
                    :inline-message="item.check"
                    style="margin-bottom: 0"
                    :show-message="true"
                    error-placement="right"
                  >
                    <template v-if="item.type === 'upload'">
                      <el-upload
                        :key="uploadKey + item.prop"
                        action="#"
                        :show-file-list="false"
                        :file-list="formFile[item.prop]"
                        class="upload-demo"
                        :accept="imageAndDocs(item.prop)"
                        :http-request="() => {}"
                        :on-exceed="file => handleExceed(file, item.prop)"
                        :before-upload="file => beforeUpload(file, item.prop)"
                      >
                        <img :src="uploadImg" alt="">
                      </el-upload>

                      <template
                        v-for="(item2, index2) in formFile[item.prop]"
                        :key="index2"
                      >
                        <FileItem
                          isNeedDelte
                          :data="item2"
                          :index="index2"
                          style="width: 50%; min-width: 130px"
                          @delete="getDeleted(item.prop, index2)"
                        />
                      </template>
                      <div class="upload_text">{{ item.text }}</div>
                    </template>
                  </el-form-item>
                </div>
              </template>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="account_management">管理员账号</div>
        <el-descriptions title="" :column="1" border :label-width="'200px'">
          <el-descriptions-item
            v-for="(item, index) in formfootData"
            :key="index"
            label-align="center"
            label-class-name="my-label"
          >
            <template #label>
              <span v-if="item.check" class="star">*</span>{{ item.label }}
            </template>
            <el-form-item
              :prop="item.prop"
              :inline-message="item.check"
              style="margin-bottom: 0"
              :show-message="true"
              error-placement="right"
            >
              <!-- text -->
              <template v-if="item.type === 'text'">
                <div style="color: #a8a8a8">
                  {{ form.organizationAdmin[item.prop] }}
                </div>
              </template>
              <!-- input输入 -->
              <template v-if="item.type === 'input'">
                <el-input
                  v-model.trim="form.organizationAdmin[item.prop]"
                  :placeholder="item.placeholder"
                  :style="{ width: item.width }"
                />
                <!-- 获取验证码 -->
                <div class="Vacode">
                  <el-button v-if="item.hasButton" @click="getCaptcha(item)">
                    {{ item.buttonText }}
                  </el-button>
                </div>
              </template>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions title="" :column="2" border :label-width="'200px'">
          <el-descriptions-item
            v-for="(item, index) in formfootDataTow"
            :key="index"
            label-align="center"
            label-class-name="my-label"
          >
            <template #label>
              <span v-if="item.check" class="star">*</span>{{ item.label }}
            </template>
            <el-form-item
              :prop="item.prop"
              :inline-message="item.check"
              style="margin-bottom: 0"
              :show-message="true"
              error-placement="right"
            >
              <!-- input输入 -->
              <div v-if="item.type === 'input' && richFlag" class="input_box">
                <el-input
                  v-if="item.prop === 'organizationAdmin.phone'"
                  v-model.trim="form.organizationAdmin.phone"
                  :placeholder="item.placeholder"
                  :style="{ width: item.width }"
                  :disabled="form.organizationAdmin.phone ? isView : false"
                >
                  <template v-if="form?.organizationAdmin?.phone" #suffix>
                    <el-icon
                      v-if="isView"
                      style="cursor: pointer"
                      @click="isViewFn"
                    >
                      <Hide />
                    </el-icon>
                    <el-icon v-else style="cursor: pointer" @click="isViewFn">
                      <View />
                    </el-icon>
                  </template>
                </el-input>
                <el-input
                  v-else
                  v-model.trim="form[item.prop]"
                  :placeholder="item.placeholder"
                  :style="{ width: item.width }"
                />
                <!-- 获取验证码 -->
                <div class="Vacode">
                  <el-button v-if="item.hasButton" @click="getCaptcha(item)">
                    {{ item.buttonText }}
                  </el-button>
                </div>
              </div>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>
    </el-scrollbar>
    <div class="footer">
      <el-button @click="reset">取消</el-button>
      <el-button type="primary" :loading="getListLoading" @click="submitForm">
        保存
      </el-button>
    </div>
    <!-- 地图 -->
    <Map
      v-if="dialogTableVisible"
      v-model="dialogTableVisible"
      :center="locationPostion"
      :selected-location="
        addressStr === '经营地址'
          ? selectedLocationMap.operatingAddress
          : selectedLocationMap.registeredAddress
      "
      :checkInResult="teacherTimeInfo"
      :addressStr="addressStr"
      @confirm="handleMapConfirm"
    />
    <el-dialog v-model="dialogVisible">
      <img class="w-full" :src="dialogImageUrl" alt="Preview Image">
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.main {
  // padding: 20px;
  background: #fff;
}
.scrollbar {
  // padding-top: 20px;
  padding: 20px;
  // height: calc(100vh - 201px);
  height: calc(100vh - 171px);
  background-color: #fff;
}
.header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 20px;
  padding: 0 10px;

  .title_left {
    display: flex;

    :nth-child(2) {
      margin-left: 40px;
    }
  }

  .title_rigth {
    display: flex;

    :nth-child(2) {
      margin-left: 40px;
    }
  }
}

.account_management {
  color: #0e1832;
  font-family: Source Han Sans CN;
  font-size: 16px;
  display: flex;
  align-items: center;
  margin: 20px 0 20px 10px;
  font-weight: 500;

  &::after {
    content: "";
    flex: 1;
    height: 1px;
    background: #e4e7ed;
    margin-left: 16px;
  }
}

.footer {
  display: flex;
  justify-content: flex-end;
  padding: 20px;
  box-sizing: border-box;
  color: #fff;

  .footer_left {
    padding: 8px 18px;
    cursor: pointer;
    background: red;
    border-radius: 8px;
  }

  .footer_right {
    padding: 8px 18px;
    cursor: pointer;
    background: #4095e5;
    border-radius: 8px;
  }
}

:deep(.my-label) {
  background: #fff !important;
  min-width: 150px !important;
}

:deep(
  .el-descriptions__body
    .el-descriptions__table.is-bordered
    .el-descriptions__cell
) {
  border: 0px !important;
}

.star {
  margin-right: 3px;
  color: red;
}

.input_box {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
.upload-demo {
  display: flex;
  align-items: center;
  margin-right: 10px;
}
.fileOther {
  // margin-top: 26px;
  margin-left: 20px;
  display: flex;
  align-items: center;
  line-height: 22px;
  width: 420px;
  // margin-bottom: 56px;
  .title {
    color: #0e1832;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    white-space: nowrap;
  }
  .link {
    width: 20px;
    height: 20px;
    margin: 0 10px;
    cursor: pointer;
  }
  .fileName {
    color: #3876fd;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    cursor: pointer;
    // width: 350px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .linkDetele {
    width: 20px;
    height: 20px;
    margin-left: 10px;
    cursor: pointer;
  }
}
:deep(.el-form-item__content) {
  flex-direction: column;
  align-items: normal;
}
:deep(.el-form-item__content) {
  display: inline-block;
  width: 300px;
  .upload_text {
    display: inline-block;
    font-size: 12px;
    position: relative;
    top: 5px;
    color: #8c939d;
  }
}
.title-text {
  color: #0e1832;
  font-family: Source Han Sans CN;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  margin: 20px 0 20px 10px;

  &::after {
    content: "";
    flex: 1;
    height: 1px;
    background: #e4e7ed;
    margin-left: 16px;
  }
}
.upload_text {
  display: inline-block;
  font-size: 12px;
  position: relative;
  top: 5px;
  color: #8c939d;
}
:deep(
  .el-descriptions__body
    .el-descriptions__table.is-bordered
    .el-descriptions__cell
) {
  border: 0px !important;
}
.selsect-pos {
  position: relative;
  width: 100%;
  // height: 60px;
  // background: red;
  .cover {
    cursor: pointer;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    // background: red;
    width: 100%;
    height: 100%;
  }

  .pos-icon {
    width: 24px;
    height: 24px;
  }
}
:deep(.hideUploadBtn .el-upload--picture-card) {
  display: none;
}
:deep(.el-form-item__error) {
  position: absolute;
  left: 0;
  top: 100%;
  white-space: nowrap;
  z-index: 10;
}
:deep(.el-form-item__content) {
  position: relative;
  min-height: 40px; // 仍建议设置
}
</style>
