import { getAsyncTask as getAsyncTask<PERSON><PERSON>, getAsyncTaskVET } from "@/api/common";
import { getAsyncTask as getAsyncTaskFunc } from "@iceywu/utils";

export const getAsyncTask = async id => {
  const rules = [
    {
      keys: "code",
      val: 200
    },
    {
      keys: ["data", "complete"],
      val: true
    }
  ];
  const params = { id };
  const { task } = getAsyncTaskFunc(getAsyncTaskApi, { rules, params });
  // console.warn("🐳-----task-----", task);
  return task;
};

//异步导入 AI工具接口
export const vETgetAsyncTask = async id => {
  const rules = [
    {
      keys: "code",
      val: 200
    },
    {
      keys: ["data", "complete"],
      val: true
    }
  ];
  const params = { id };
  const { task, stop } = getAsyncTaskFunc(getAsyncTaskVET, { rules, params });
  // console.warn("🐳-----task-----", task);
  return { task, stop };
};
// 新建实践点、领队讲师师资删除数据白名单路由
export const whitePath = [
  "/course/courseDetails",
  "/course/courseDetails/currentDetails",
  "/course/currentDetails/groupOrder",
  "/course/courseManage/index",
  "/course/drafts",
  "/welcome",
  "/institution/institutionManage",
  "/institution/baseManage",
  "/institution/logManage",
  "/course/orderManage",
  "/course/financeManage",
  "/account/teamManage",
  "/account/teacherManage",
  "/account/accountManage",
  "/account/roleManage"
];
