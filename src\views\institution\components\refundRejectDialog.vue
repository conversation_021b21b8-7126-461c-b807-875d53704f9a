<script setup>
import { ref, defineEmits, computed } from "vue";
import { ElMessage } from "element-plus";

const props = defineProps({
  title: {
    type: String,
    default: "退款驳回"
  },
  dialogFormVisible: {
    type: Boolean,
    default: false
  },
  textLeftBtn: {
    type: String,
    default: "取消"
  },
  textRightBtn: {
    type: String,
    default: "确认驳回"
  }
});

const emit = defineEmits(["reset", "update:dialogFormVisible", "confirm"]);

const reason = ref("");
const getListLoading = ref(false);
const selectedReasons = ref([]); // 用于跟踪已选择的理由

// 退款驳回理由列表
const reasonList = ref([
  { id: 0, name: "课程过半不予退款" },
  { id: 1, name: "课程结束不予退款" },
  { id: 2, name: "超出合同约定退款期限" },
  { id: 3, name: "不符合合同退款条件" }
]);

// 使用 computed 属性代理 prop
const localVisible = computed({
  get() {
    return props.dialogFormVisible;
  },
  set(value) {
    emit("update:dialogFormVisible", value);
  }
});

// 选择理由事件
const selectReasonEvt = (item, index) => {
  const isSelected = selectedReasons.value.some(
    selected => selected.id === item.id
  );

  if (isSelected) {
    // 如果已选择，则移除
    const index = selectedReasons.value.findIndex(
      selected => selected.id === item.id
    );
    selectedReasons.value.splice(index, 1);
  } else {
    // 如果未选择，则添加
    selectedReasons.value.push(item);
  }

  // 更新文本框内容为所有已选择理由的连接
  reason.value = selectedReasons.value
    .map(reasonItem => reasonItem.name)
    .join("，");
};

// 处理理由输入
const handleReasonInput = value => {
  reason.value = value;
};

// 确认按钮点击事件
const btnOKClick = async () => {
  if (!reason.value.trim()) {
    ElMessage.warning("请选择或输入驳回理由");
    return;
  }

  if (getListLoading.value) {
    return;
  }

  getListLoading.value = true;

  try {
    // 触发确认事件，传递驳回理由
    emit("confirm", reason.value);
  } catch (error) {
    console.error("驳回操作失败：", error);
  } finally {
    getListLoading.value = false;
  }
};

// 取消按钮点击事件
const cancel = () => {
  selectedReasons.value = [];
  reason.value = "";
  emit("reset");
};

// 弹窗关闭事件
const handleClose = () => {
  selectedReasons.value = [];
  reason.value = "";
};
</script>

<template>
  <el-dialog
    v-model="localVisible"
    :title="title"
    width="515"
    @close="handleClose"
  >
    <div class="content">
      <div class="des-examine">
        <div>
          <el-input
            v-model="reason"
            placeholder="请选择或输入驳回理由"
            type="textarea"
            resize="none"
            maxlength="100"
            show-word-limit
            @input="handleReasonInput"
          />
          <div class="select-btn">
            <div
              v-for="(item, index) in reasonList"
              :key="index"
              :class="
                selectedReasons.some(selected => selected.id === item.id)
                  ? 'active-item'
                  : 'select-item'
              "
              @click="selectReasonEvt(item, index)"
            >
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">
          {{ textLeftBtn }}
        </el-button>
        <el-button :loading="getListLoading" type="primary" @click="btnOKClick">
          {{ textRightBtn }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.des-examine {
  width: 96%;
  height: 200px;
  margin: 0 auto;

  :deep(.el-textarea__inner) {
    height: 150px;
  }

  .select-btn {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    .select-item {
      padding: 5px;
      border: 1px solid #ccc;
      border-radius: 5px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s;

      &:hover {
        color: #409eff;
        border-color: #dae8f6;
        background-color: #ebf5ff;
      }
    }

    .active-item {
      padding: 5px;
      background-color: #409eff;
      color: #fff;
      border-radius: 5px;
      font-size: 14px;
      cursor: pointer;
      border: 1px solid #409eff;
    }
  }
}
</style>
