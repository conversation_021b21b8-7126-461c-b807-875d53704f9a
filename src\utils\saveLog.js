import { useUserStoreHook } from "@/store/modules/user";
import { isEmpty, removeEmptyValues } from "@iceywu/utils";
import { operateLogSave } from "@/api/institution";
export const saveLog = data => {
  console.log("🌵-----data-----", data);

  const {
    operateLogType,
    detail = "",
    additionalParameter,
    operator,
    operatorTarget,
    operateType
  } = data;
  let detailVal = detail;
  if (isEmpty(detail)) {
    const userName = operator ?? useUserStoreHook()?.username;
    detailVal = `${userName}${operateType}${operatorTarget}`;
  }
  const params = {
    operateLogType,
    detail: detailVal,
    additionalParameter
  };
  console.log("🐳-----params-----", params);
  operateLogSave(removeEmptyValues(params));
};

export const saveLoginLog = async username => {
  const logParams = {
    operateLogType: "ACCOUNT_MANAGEMENT",
    detail: `${username}登录了系统`,
    operateType: "登录",
    operatorTarget: "机构端"
  };
  try {
    await operateLogSave(logParams);
  } catch (err) {
    console.error("登录日志记录失败", err);
  }
};
