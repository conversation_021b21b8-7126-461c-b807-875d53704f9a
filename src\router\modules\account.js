// import { $t } from "@/plugins/i18n";
import { components } from "@/router/enums.js";
import { acCodesList, reCodesList } from "@/router/accidCode.js";
import AccountIcon from "@/assets/home/<USER>";
import AccountIconActive from "@/assets/home/<USER>";
import { KeepAlive } from "vue";
export default {
  path: "/account",
  redirect: "/account/teamManage",
  meta: {
    icon: "ri:information-line",
    title: "账号",
    imgIcon: AccountIcon,
    imgIconActive: AccountIconActive,
    rank: components,
    idCode: acCodesList.baseCode
  },
  children: [
    {
      path: "/account/team",
      name: "team",
      redirect: "/account/teamManage",
      // component: () => import("@/views/account/teamManage.vue"),
      meta: {
        title: "领队管理"
        // idCode: acCodesList.teamManage
      },
      children: [
        {
          path: "/account/teamManage",
          name: "TeamManageIndex",
          component: () => import("@/views/account/teamManage.vue"),
          meta: {
            title: "领队管理",
            keepAlive: true,
            idCode: acCodesList.teamManage
          }
        },
        {
          path: "/account/teamCreate",
          name: "teamCreate",
          component: () => import("@/views/course/accountCreate.vue"),
          meta: {
            title: "新建领队",
            idCode: reCodesList.periodLeaderCreate,
            showLink: false
          }
        },
        {
          path: "/account/teacherDetails",
          name: "TeacherDetails",
          component: () => import("@/views/account/teacherDetails.vue"),
          meta: {
            title: "详情",
            keepAlive: true,
            idCode: reCodesList.teacherDetails,
            showLink: false
          },
          children: [
            {
              path: "/account/qualificationsFile",
              name: "qualificationsFile",
              component: () => import("@/views/account/qualificationsFile.vue"),
              meta: {
                title: "资质文件",
                idCode: reCodesList.qualificationsFile,
                showLink: false
              }
            },
            {
              path: "/account/editInformation",
              name: "editInformation",
              component: () => import("@/views/account/editInformation.vue"),
              meta: {
                title: "编辑信息",
                idCode: reCodesList.editInformation,
                showLink: false
              }
            },
            {
              path: "/course/currentPeriodDetails",
              name: "currentPeriodDetails",
              component: () => import("@/views/course/currentDetails.vue"),
              // redirect: "/course/courseDetails/currentDetails/tableList",
              meta: {
                title: "课程详情",
                idCode: reCodesList.relatedCourse,
                keepAlive: true
              },
              children: [
                {
                  path: "/course/currentDetails/evaluateDetail",
                  name: "evaluateDetail",
                  component: () =>
                    import("@/views/course/components/evaluateDetail.vue"),
                  meta: {
                    title: "用户评价详情",
                    idCode: reCodesList.evaluateDetail
                  }
                },
                {
                  path: "/course/currentDetails/workDetail",
                  name: "workDetail",
                  component: () =>
                    import("@/views/course/components/workDetail.vue"),
                  meta: {
                    title: "实践感悟详情",
                    idCode: reCodesList.workDetail
                  }
                }
              ]
            }
          ]
        }
      ]
    },
    {
      path: "/account/teacher",
      name: "teacher",
      redirect: "/account/teacherManage",
      // component: () => import("@/views/account/teacherManage.vue"),
      meta: {
        title: "讲师管理"
        // idCode: acCodesList.teacherManage
      },
      children: [
        {
          path: "/account/teacherManage",
          name: "TeacherManage",
          component: () => import("@/views/account/teacherManage.vue"),
          meta: {
            title: "讲师管理",
            keepAlive: true,
            idCode: acCodesList.teacherManage
          }
        },
        {
          path: "/account/teacherCreate",
          name: "teacherCreate",
          component: () => import("@/views/course/accountCreate.vue"),
          meta: {
            title: "新建讲师",
            idCode: reCodesList.periodLeaderCreate,
            showLink: false
          }
        },
        {
          path: "/account/lectureDetails",
          name: "LectureDetails",
          component: () => import("@/views/account/teacherDetails.vue"),
          meta: {
            title: "详情",
            // keepAlive: true,
            idCode: reCodesList.teacherDetails,
            showLink: false
          },
          children: [
            {
              path: "/account/lectureQualificationsFile",
              name: "lectureQualificationsFile",
              component: () => import("@/views/account/qualificationsFile.vue"),
              meta: {
                title: "资质文件",
                idCode: reCodesList.qualificationsFile,
                showLink: false
              }
            },
            {
              path: "/account/lectureEditInformation",
              name: "lectureEditInformation",
              component: () => import("@/views/account/editInformation.vue"),
              meta: {
                title: "编辑信息",
                idCode: reCodesList.editInformation,
                showLink: false
              }
            },
            {
              path: "/course/currentPeriodDetails",
              name: "lecturerCurrentPeriodDetails",
              component: () => import("@/views/course/currentDetails.vue"),
              meta: {
                title: "课程详情",
                idCode: reCodesList.relatedCourse,
                keepAlive: true
              },
              children: [
                {
                  path: "/course/currentDetails/evaluateDetail",
                  name: "lecturerEvaluateDetail",
                  component: () =>
                    import("@/views/course/components/evaluateDetail.vue"),
                  meta: {
                    title: "用户评价详情",
                    idCode: reCodesList.evaluateDetail
                  }
                },
                {
                  path: "/course/currentDetails/workDetail",
                  name: "lecturerWorkDetail",
                  component: () =>
                    import("@/views/course/components/workDetail.vue"),
                  meta: {
                    title: "实践感悟详情",
                    idCode: reCodesList.workDetail
                  }
                }
              ]
            }
          ]
        }
      ]
    },
    {
      path: "/account/account",
      name: "account",
      redirect: "/account/accountManage",
      // component: () => import("@/views/account/accountManage.vue"),
      meta: {
        title: "账号管理",
        idCode: acCodesList.accountManage
      },
      children: [
        {
          path: "/account/accountManage",
          name: "AccountManage",
          component: () => import("@/views/account/accountManage.vue"),
          meta: {
            title: "账号管理",
            keepAlive: true,
            idCode: acCodesList.accountManage
          }
        },
        {
          path: "/account/accountCreate",
          name: "accountCreate",
          component: () => import("@/views/account/accountCreate.vue"),
          meta: {
            title: "新建账号",
            idCode: reCodesList.accountCreate,
            showLink: false
          }
        },
        {
          path: "/account/accountEdit",
          name: "accountEdit",
          component: () => import("@/views/account/accountCreate.vue"),
          meta: {
            title: "编辑账号",
            idCode: reCodesList.accountEdit,
            showLink: false
          }
        },
        {
          path: "/account/accountDetails",
          name: "accountDetails",
          component: () => import("@/views/account/accountDetails.vue"),
          meta: {
            title: "账号详情",
            idCode: reCodesList.accountDetails,
            showLink: false
          }
        }
      ]
    },
    {
      path: "/account/roleManage",
      name: "roleManage",
      component: () => import("@/views/account/roleManage.vue"),
      meta: {
        title: "角色管理",
        idCode: acCodesList.roleManage
      }
    }
    // {
    //   path: "/account/rightsManage",
    //   name: "rightsManage",
    //   component: () => import("@/views/account/rightsManage.vue"),
    //   meta: {
    //     title: "权限管理",
    //     idCode: acCodesList.rightsManage
    //   }
    // }
  ]
};
