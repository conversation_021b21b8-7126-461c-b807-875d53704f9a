import { Edit } from "@element-plus/icons-vue";
import { fileDeleteById } from "@/api/files";
import ImgBlurHash from "@/components/ImgBlurHash";
import { message } from "@/utils/message";
import dayjs from "dayjs";
import { onMounted, reactive, ref } from "vue";
import { uploadFile } from "@/utils/upload/upload.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { lowerFirst } from "lodash";

export function useRole() {
  const columns = ref([]);

  const loadingTable = ref(false);

  const dataList = ref([]);

  return {
    columns
  };
}
