import apiServer from "@/utils/http/base";

// 判断代理服务是否可用
function checkImageProxyServer() {
  return !!apiServer.imageProxy;
}

//  图片缩略
export function ImageThumbnail(url, optionVal = "150x") {
  if (!checkImageProxyServer() || !url) {
    return url;
  }
  const imageProxyServer = apiServer.imageProxy;
  if (optionVal) {
    const thumbnailUrl = `${imageProxyServer}/${optionVal}/${url}`;
    return thumbnailUrl;
  } else {
    return url;
  }
}
