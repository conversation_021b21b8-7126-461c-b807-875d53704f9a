<script setup>
import { ref, onMounted, onActivated } from "vue";
import { useRouter, useRoute } from "vue-router";
import { complexDelete, findAllNotPage } from "@/api/institution.js";
import { ElMessage, ElMessageBox } from "element-plus";
import { requestTo } from "@/utils/http/tool";
import { formatTime } from "@/utils/index";
import { template } from "lodash";
defineOptions({
  name: "BaseManage"
});
const router = useRouter();
const route = useRoute();
onActivated(() => {
  getTableList();
});
onMounted(() => {
  getTableList();
});
// 表格数据
const tableData = ref([]);
const params = ref({
  page: 1,
  size: 10,
  sort: "createdAt,desc",
  totalElements: 0
});
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async item => {
  let paramsData = {
    // id: 1,
    // page: params.value.page - 1,
    // size: params.value.size,
    // sort: params.value.sort
  };
  console.log("🍧-----paramsData-----", paramsData);
  // return;
  const { code, data } = await findAllNotPage(paramsData);
  console.log("🎁-----data-----", data);
  if (code === 200) {
    tableData.value = data;
    console.log("🍪-----tableData.value-----", tableData.value);
    // params.value.totalElements = data.totalElements;
  } else {
    // ElMessage.error(err);
  }
  getListLoading.value = false;
};

//分页
const handleCurrentChange = e => {
  params.value.page = e;
};
// 实践点详情
const getInfoid = item => {
  router.push({
    path: "/institution/baseDetails",
    query: { id: item.id }
  });
};
const Freeze = async row => {
  ElMessageBox.confirm("你确定要删除该实践点吗?", "确认删除", {
    confirmButtonText: "确认删除",
    cancelButtonText: "取消",
    type: ""
  })
    .then(() => {
      isChargebackApi(row);
    })
    .catch(() => {
      // ElMessage({
      //   type: "info",
      //   message: "已取消"
      // });
    });
};
const isChargebackApi = async (row, bool) => {
  const params = {
    id: row.id
  };
  const operateLog = {
    operateLogType: "COMPLEX_MANAGEMENT",
    operateType: "删除了",
    operatorTarget: `“${row.name}”的实践点信息`
  };
  const { msg, code } = await complexDelete(params, operateLog);
  if (code === 200) {
    ElMessage({
      type: "success",
      message: "删除成功"
    });
    getTableList();
  } else {
    ElMessage({
      type: "error",
      message: msg || "删除失败"
    });
  }
};

//跳转到新建实践点
const newBase = () => {
  router.push({
    path: "/institution/baseAdd",
    query: { title: "jsMang", id: 1 }
  });
};
</script>

<template>
  <div class="containers">
    <div class="con_top">
      <el-button class="titles" type="primary" @click="newBase">
        新建实践点
      </el-button>
      <!-- <el-button type="primary">创建课程</el-button> -->
    </div>
    <el-scrollbar class="scrollbar">
      <div class="con_table">
        <el-table
          :data="tableData"
          table-layout="fixed"
          :header-cell-style="{ backgroundColor: '#fafafa', color: '#565353' }"
          highlight-current-row
          max-height="calc(100vh - 280px)"
          :row-style="{ height: '50px' }"
        >
          <!-- :row-style="{ height: '70px' }" -->
          <el-table-column type="index" width="100" label="编号" />
          <el-table-column prop="name" label="实践点名称">
            <template #default="scope">
              <el-text>
                {{ scope.row.name || "--" }}
              </el-text>
            </template>
          </el-table-column>
          <el-table-column prop="state" label="类型" align="left">
            <template #default="scope">
              <div v-if="scope.row.complexType === 'MEETING_ROOM'">会议室</div>
              <div v-else-if="scope.row.complexType === 'CLASSROOM'">教室</div>
              <div v-else-if="scope.row.complexType === 'LOBBY'">大厅</div>
              <div v-else-if="scope.row.complexType === 'SQUARE'">广场</div>
              <div v-else-if="scope.row.complexType === 'PARK'">公园</div>
              <div v-else-if="scope.row.complexType === 'ATTRACTION'">景点</div>
              <div v-else-if="scope.row.complexType === 'OUTDOORS'">户外</div>
              <div v-else-if="scope.row.complexType === 'OTHER'">其他</div>
              <div v-else>--</div>
            </template>
          </el-table-column>
          <el-table-column prop="state" label="地址" align="left">
            <template #default="scope">
              <div>
                {{ scope.row.detailedAddress || "--" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" label="创建时间">
            <template #default="scope">
              <div>
                {{
                  formatTime(scope.row?.createdAt, "YYYY-MM-DD HH:mm:ss") ||
                  "暂无"
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="address"
            fixed="right"
            label="操作"
            align="left"
          >
            <template #default="scope">
              <div class="operate">
                <el-button type="primary" link @click="getInfoid(scope.row)">
                  详情
                </el-button>
                <el-button
                  type="primary"
                  style="color: red"
                  link
                  @click="Freeze(scope.row)"
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="con_pagination">
        <!-- 分页 -->
        <!-- <el-pagination
        v-model:current-page="params.page"
        v-model:page-size="params.size"
        class="pagination"
        background
        layout="total, prev, pager, next, jumper"
        :total="params.totalElements"
        @current-change="handleCurrentChange"
      /> -->
      </div>
    </el-scrollbar>
  </div>
</template>

<style lang="scss" scoped>
.scrollbar {
  // padding-top: 20px;
  padding: 20px;
  // height: calc(100vh - 181px);
  height: calc(100vh - 151px);

  background-color: #fff;
}
.containers {
  box-sizing: border-box;
  // width: calc(100% - 48px);
  // height: 100%;
  // padding: 24px;
  background: #fff;

  .con_top {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    height: fit-content;
    padding: 20px 20px 0 20px;

    // .titles {
    //   padding: 6px 14px;
    //   font-size: 14px;
    //   // font-weight: bold;
    //   color: #fff;
    //   cursor: pointer;
    //   background: #4095e5;
    //   border-radius: 8px;
    // }
  }

  .con_search {
    display: flex;
    align-items: center;
    width: 100%;
    height: fit-content;
    margin-bottom: 24px;
  }

  .con_table {
    // width: calc(100% - 25px);
    margin-bottom: 24px;
    // margin-left: 25px;

    .btnse {
      color: #409eff;
    }
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }
}
</style>
