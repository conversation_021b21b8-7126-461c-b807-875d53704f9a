import { encryption } from "@/utils/SM4.js";
import { webUrl } from "@/utils/http/base";

//  ai工具
export function aiNewPage(type) {
  let url = "";
  let userId = encryption(
    JSON.parse(localStorage.getItem("userInfoData"))?.id.toString()
  );
  if (webUrl.aiServer && type && userId) {
    url = `${webUrl.aiServer}?sig=${userId}&type=${type}`;
  } else if (webUrl.aiServer && userId) {
    url = `${webUrl.aiServer}?sig=${userId}`;
  } else if (webUrl.aiServer && type) {
    url = `${webUrl.aiServer}?type=${type}`;
  } else {
    url = `${webUrl.aiServer}`;
  }
  window.open(url, "_blank");
}
