import dayjs from "dayjs";
import { ElLoading } from "element-plus";
import { saveAs } from "file-saver";

import "dayjs/locale/zh-cn";
import relativeTime from "dayjs/plugin/relativeTime";
dayjs.extend(relativeTime); // 相对时间
dayjs.locale("zh-cn"); // 使用本地化语言

// formatTime(item.createdAt, 'YYYY-MM-DD HH:mm')
export const formatTime = (
  time,
  format = "YYYY-MM-DD HH:mm:ss",
  isISO = true
) => {
  if (!time) return "";
  isISO && (time = new Date(time).getTime());
  if (time.toString().length < 13) {
    time = time * 1000;
  }
  return dayjs(time).format(format);
};
export function downloadFileBySaveAs(url, fileName) {
  const loadingInstance = ElLoading.service({
    lock: true,
    text: "下载中...",
    background: "rgba(0, 0, 0, 0.7)"
  });
  fetch(url)
    .then(res => res.blob())
    .then(blob => {
      saveAs(blob, fileName);
      loadingInstance.close();
    })
    .catch(error => {
      loadingInstance.close();
    });
}
export const getFileType = (url = "") => {
  const flieArr = url.split(".");
  const suffix = flieArr[flieArr.length - 1];
  let type;
  if (suffix) {
    type = suffix.toLowerCase();
  } else {
    return "other";
  }
  if (["png", "jpg", "jpeg", "bmp", "gif", "webp"].includes(type)) {
    return "image";
  } else if (
    ["mp4", "m2v", "mkv", "rmvb", "wmv", "avi", "flv", "mov", "m4v"].includes(
      type
    )
  ) {
    return "video";
  } else if (["mp3", "wav", "wmv"].includes(type)) {
    return "radio";
  } else if (["doc", "docx"].includes(type)) {
    return "doc";
  } else if (["xls", "xlsx"].includes(type)) {
    return "xls";
  } else if (type == "pdf") {
    return "pdf";
  } else {
    return "other";
  }
};
