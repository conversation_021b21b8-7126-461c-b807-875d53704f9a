<script setup>
import { onMounted, ref } from "vue";
import { formatTime } from "@/utils/index";
import { orderDetail, confirmRefundSub } from "@/api/period.js";
import { confirmRefund } from "@/api/orderManagement.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { ORDER_ENUM } from "@/utils/enum.js";
import { to } from "@iceywu/utils";
import { useRouter, useRoute } from "vue-router";

const router = useRouter();
const route = useRoute();
const refName = ref("digndd");
const formData = ref([
  {
    label: "订单号",
    type: "text",
    prop: "ordersId",
    // check: true,
    placeholder: "",
    width: "200px",
    value: "--"
  },
  {
    label: "子订单号",
    type: "text",
    prop: "id",
    // check: true,
    placeholder: "",
    width: "200px",
    value: "--"
  },
  {
    label: "团购发起人ID",
    type: "text",
    prop: "promoterId",
    // check: true,
    placeholder: "",
    width: "200px",
    value: "--"
  },
  {
    label: "团购发起人",
    type: "text",
    prop: "promoter",
    // check: true,
    placeholder: "",
    width: "200px",
    value: "--"
  },
  {
    label: "创建时间",
    type: "text",
    // check: true,
    prop: "createdAt",
    placeholder: "",
    width: "200px",
    value: "--"
  },
  {
    label: "订单状态",
    type: "text",
    prop: "orderStatus",
    // check: true,
    placeholder: "",
    width: "200px",
    value: "--"
  },
  {
    label: "付款时间",
    type: "text",
    prop: "payTime",
    // check: true,
    placeholder: "",
    width: "200px",
    value: "--"
  },
  {
    // label: "完成时间",
    label: "完成时间",
    type: "text",
    prop: "finishTime",
    // check: true,
    placeholder: "",
    width: "200px",
    value: "--"
  },
  {
    label: "课程名",
    type: "text",
    prop: "courseName",
    // check: true,
    placeholder: "",
    width: "200px",
    value: "--"
  },
  {
    label: "课程期号",
    type: "text",
    prop: "termNumber",
    // check: true,
    placeholder: "",
    width: "200px",
    value: "--"
  },
  {
    label: "机构",
    type: "text",
    prop: "organizationName	",
    // check: true,
    placeholder: "",
    width: "200px",
    value: "--"
  },
  {
    label: "机构客服热线",
    type: "text",
    prop: "organizationServicePhone",
    // check: true,
    isEye: true,
    isView: true,
    placeholder: "",
    width: "200px",
    value: "--"
  },
  {
    label: "机构管理员",
    type: "text",
    prop: "organizationManager",
    // check: true,
    placeholder: "",
    width: "200px",
    value: "--"
  },
  {
    label: "管理员电话",
    type: "text",
    prop: "organizationManagerPhone",
    // check: true,
    isEye: true,
    isView: true,
    placeholder: "",
    width: "200px",
    value: "--"
  },
  {
    label: "购买人",
    type: "text",
    prop: "buyer",
    // check: true,
    placeholder: "",
    width: "200px",
    value: "--"
  },
  {
    label: "购买人电话",
    type: "text",
    prop: "buyerPhone",
    // check: true,
    isEye: true,
    isView: true,
    placeholder: "",
    width: "200px",
    value: "--"
  },
  {
    label: "学生",
    type: "text",
    prop: "studentName",
    // check: true,
    placeholder: "",
    width: "200px",
    value: "--"
  },
  {
    label: "购买规格",
    type: "text",
    prop: "specification",
    // check: true,
    placeholder: "",
    width: "200px",
    value: "--"
  },
  {
    label: "购买价格",
    type: "text",
    prop: "price",
    // check: true,
    placeholder: "",
    width: "200px",
    value: "--"
  },
  {
    label: "订单附带文件",
    type: "upload",
    // check: true,
    prop: "orderFile",
    placeholder: "",
    width: "200px",
    value: []
  }
]);
// 获取列表信息
const getListLoading = ref(false);
const getData = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    id: Number(route.query.subOrdersId) || ""
  };
  // console.log("🍧-----paramsData-----", paramsData);
  const [err, result] = await requestTo(orderDetail(paramsData));
  console.log("🎁-----result--33---", result);
  if (result) {
    formData.value[0].value = result?.id;
    formData.value[1].value = result?.ordersId;
    formData.value[2].value = result?.promoterId;
    formData.value[3].value = result?.promoter;
    formData.value[4].value = result?.createdAt
      ? formatTime(result?.createdAt, "YYYY-MM-DD HH:mm:ss")
      : "--";
    formData.value[5].value = ORDER_ENUM[result?.orderStatus].label;
    formData.value[6].value = result?.payTime
      ? formatTime(result?.payTime, "YYYY-MM-DD HH:mm:ss")
      : "--";
    formData.value[7].value = result?.finishTime
      ? formatTime(result?.finishTime, "YYYY-MM-DD HH:mm:ss")
      : "--";
    formData.value[8].value = result?.courseName;
    formData.value[9].value = result?.termNumber;
    formData.value[10].value = result?.organizationName;
    formData.value[11].value = result?.organizationServicePhone;
    formData.value[12].value = result?.organizationManager;
    formData.value[13].value = result?.organizationManagerPhone;
    formData.value[14].value = result?.buyer;
    formData.value[15].value = result?.buyerPhone;
    formData.value[16].value = result?.studentName;
    formData.value[17].value = result?.specification;
    formData.value[18].value = result?.price;
    formData.value[19].value = result?.files;
  }
  if (err) {
    ElMessage.error(err);
  }
  getListLoading.value = false;
};
// 退单(子订单)
const outOrder = () => {
  ElMessageBox.confirm("你确定要退单吗?", "确认退单", {
    confirmButtonText: "确认退单",
    cancelButtonText: "我再想想",
    type: ""
  })
    .then(async () => {
      const params = {
        id: Number(route.query.subOrdersId) || ""
      };
      return;
      const [err, res] = await to(confirmRefundSub(params));
      if (res.code == 200) {
        ElMessage({
          type: "success",
          message: "退单成功"
        });
      } else {
        ElMessage({
          type: "error",
          message: "退单失败"
        });
      }
    })
    .catch(() => {
      // ElMessage({
      //   type: "info",
      //   message: "已取消"
      // });
    });
};
onMounted(() => {
  getData();
});
</script>

<template>
  <div class="related-orders">
    <el-descriptions class="descriptions" title="" :column="2" border>
      <template v-for="(item, index) in formData" :key="index">
        <el-descriptions-item
          v-if="item.label === '订单附带文件'"
          width="120px"
          :span="
            item.label === '订单附带文件'
              ? 2
              : item.label === '购买价格'
                ? 2
                : ''
          "
        >
          <template #label>
            <div class="cell-item">{{ item.label }}</div>
          </template>
          <template v-for="(item2, index2) in item.value" :key="index2">
            <div v-show="item2?.fileName" class="fileOther">
              <!-- <div class="title">附件</div> -->
              <img class="link" src="@/assets/login/link.png" alt="">
              <div class="fileName">{{ item2?.fileName }}</div>
            </div>
          </template>
        </el-descriptions-item>
        <el-descriptions-item
          v-else
          width="120px"
          :span="
            item.label === '订单附带文件'
              ? 2
              : item.label === '购买价格'
                ? 2
                : ''
          "
        >
          <template #label>
            <div class="cell-item">{{ item.label }}</div>
          </template>
          {{ item.value }}
        </el-descriptions-item>
      </template>
    </el-descriptions>
    <div class="buttons">
      <div class="create" @click="outOrder">
        {{ "确认退单" }}
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.related-orders {
  box-sizing: border-box;
  width: 100%;
  height: 520px;
  background-color: #fff;
  padding: 15px 30px;
  .descriptions {
    width: 100%;
    height: 448px;
    overflow-y: auto;
    margin-bottom: 10px;
  }
  .buttons {
    display: flex;
    justify-content: flex-end;
    // width: 95%;
    // margin-top: 28vh;

    .create {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100px;
      height: 36px;
      color: rgb(255 255 255 / 100%);
      cursor: pointer;
      background-color: rgb(64 149 229 / 100%);
      border-radius: 6px;
    }
  }
}
</style>
