<script setup>
import { id } from "element-plus/es/locale/index.mjs";
import { onMounted, ref, defineEmits, computed } from "vue";
import { requestTo } from "@/utils/http/tool";
import { pinyin } from "pinyin-pro";
import { decrypt, encryption } from "@/utils/SM4.js";
import { ElMessage } from "element-plus";
import { useUserStoreHook } from "@/store/modules/user";
import { debounce } from "@iceywu/utils";

const props = defineProps({
  title: {
    type: String
  },
  api: {
    type: String,
    default: ""
  },
  id: {
    type: Number
  },
  name: {
    type: String,
    default: ""
  },
  phone: {
    type: String,
    default: ""
  },
  account: {
    type: String,
    default: ""
  },
  dialogFormVisible: {
    type: Boolean
  },
  logOut: {
    type: Boolean,
    default: true
  },
  operateLogType: {
    type: String,
    default: "COMPLEX_MANAGEMENT"
  }
});
const emit = defineEmits(["reset", "update:dialogFormVisible"]);
onMounted(() => {
  console.log(props.title);
  generatePassword();
});
// const dialogFormVisible = ref(false);
const newPassword = ref("");
const getListLoading = ref(false);
const btnOKClick = debounce(
  async data => {
    if (getListLoading.value) {
      return;
    }
    getListLoading.value = true;
    let paramsData = {
      id: props.id,
      password: encryption(newPassword.value)
    };
    const operateLog = {
      operateLogType: props.operateLogType,
      operateType: `重置了${props.name}的密码`
      // operatorTarget: form.value.name,
    };
    try {
      const { code, data, msg } = await props.api(paramsData, operateLog);
      if (code == 200) {
        ElMessage({
          message: "重置密码成功",
          type: "success"
        });
        /** 退出登录 */
        if (props.logOut) {
          useUserStoreHook().logOut();
        }
      }
    } catch (error) {
      console.error("生成密码失败：", error);
    }
    getListLoading.value = false;
    emit("reset");
  },
  1000,
  { immediate: true }
);
// 生成管理员账号的方法
const generatePassword = () => {
  try {
    // 1. 获取姓名首字母
    const initials = pinyin(props.name, {
      pattern: "first", // 只保留首字母
      toneType: "none", // 不显示声调
      type: "array" // 返回数组格式
    })
      .join("")
      .toLowerCase();

    // 2. 获取手机号后6位
    const phonePart = props.phone.slice(-6);

    // 3. 组合生成密码
    newPassword.value = `${initials}${phonePart}@`;
    console.log("🌈-----newPassword.value-----", newPassword.value);
  } catch (error) {
    console.error("生成密码失败：", error);
    // password.value = '生成失败，请检查输入';
  }
};
const cancel = () => {
  emit("reset");
};
// 使用 computed 属性代理 prop
const localVisible = computed({
  get() {
    return props.dialogFormVisible;
  },
  set(value) {
    emit("update:dialogFormVisible", value); // 通知父组件更新
  }
});
</script>

<template>
  <!-- <div class="popup"> -->
  <el-dialog v-model="localVisible" title="重置密码确认" width="500">
    <div class="content">
      <div class="info">
        <p>重置密码的账号 {{ props.account }}</p>
        <p>密码将被重置为 {{ newPassword }}</p>
        <p>密码生成规则：姓名首字母+手机号后6位+@</p>
      </div>
      <!-- <div class="btns">
        <div class="btn_cancel" @click="cancel">取消</div>
        <div class="btn_cancel" @click="btnOKClick">确认重置</div>
      </div> -->
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="danger" @click="btnOKClick"> 确认重置 </el-button>
      </div>
    </template>
  </el-dialog>
  <!-- </div> -->
  <!-- <div class="popup">
    <div class="title">
      <span class="title_text">{{ title }} </span>
    </div>
    <div class="content">
      <div class="info">
        <p>重置密码的账号 {{ props.account }}</p>
        <p>密码将被重置为 {{ newPassword }}</p>
        <p>密码生成规则：姓名首字母+手机号后6位+@</p>
      </div>
      <div class="btns">
        <div class="btn_cancel" @click="$emit('cancel')">取消</div>
        <div class="btn_cancel" @click="btnOKClick">确认重置</div>
      </div>
    </div>
  </div> -->
</template>

<style lang="scss" scoped>
.info {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  :nth-child(2) {
    margin: 20px 0 20px 0px;
  }
}
</style>
