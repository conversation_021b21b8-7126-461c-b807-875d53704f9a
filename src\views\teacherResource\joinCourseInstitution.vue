<script setup>
import { onMounted, ref, nextTick, onActivated } from "vue";
import { formatTime } from "@/utils/index";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { ledeterAll, isFreeze } from "@/api/leaderLecturer.js";
import { decrypt, encryption } from "@/utils/SM4.js";
import { View, Hide } from "@element-plus/icons-vue";
import { COURSE_PERIOD_ENUM } from "@/utils/enum.js";
import { institutionCoursePeriod } from "@/api/teachers/institutionalFaculty.js";
import { stateOptions } from "@/views/teacherResource/utils/options.js";
defineOptions({
  name: "TeamManageIndex"
});
const router = useRouter();
const route = useRoute();

onActivated(() => {
  getTableList();
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});

onMounted(() => {
  getTableList();
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});

// 计算表格高度
const searchFormHeight = ref(0);
const tableHeight = ref("calc(100vh - 350px)");

const calculateTableHeight = async () => {
  await nextTick();
  const searchForm = document.querySelector(".con_search");
  if (searchForm) {
    searchFormHeight.value = searchForm.offsetHeight;
    // 动态计算表格高度，减去搜索框高度、表头按钮高度、分页器高度和其他间距
    tableHeight.value = `calc(100vh - 230px - ${searchFormHeight.value}px)`;
  }
};

// 搜索表单
const form = ref({
  startTime: "",
  endTime: "",
  name: "",
  complexName: "",
  coursePeriodState: ""
});
// 表格数据
const tableData = ref([
  // {
  //   id: 18,
  //   createdAt: 0,
  //   updatedAt: 0,
  //   name: "1223",
  //   organizationName: "dd ",
  //   courseTypeName: "fff",
  //   termNumber: 0
  // }
]);
const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});
// 获取列表信息
const getTableList = async data => {
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort,
    teacherId: route.query.teacherId
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  // console.log("paramsData =", paramsData);
  // return;
  const [err, result] = await requestTo(institutionCoursePeriod(paramsData));
  if (result) {
    result?.content.forEach(item => {
      item.show_phone = false;
      // item.show_card = false;
    });
    tableData.value = result?.content;
    params.value.totalElements = result.totalElements;
  } else {
    ElMessage.error(err);
  }
};
const eye_phone = id => {
  const item = tableData.value.find(item => item.id === id);
  if (item) {
    item.type_phone = !item.type_phone;
  }
};
// const eye_card = id => {
//   const item = tableData.value.find(item => item.id === id);
//   if (item) {
//     item.show_card = !item.show_card;
//   }
// };
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTableList();
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getTableList();
};
//搜索
const searchData = () => {
  params.value.page = 1;
  getTableList();
};
// 重置
const setData = () => {
  form.value = {};
  params.value.page = 1;
  value1.value = [];
  getTableList();
};
// 选择时间
const timeChange = e => {
  form.value.startTime = e ? new Date(e[0])?.getTime() : "";
  form.value.endTime = e ? new Date(e[1])?.getTime() + (86400000 - 1) : "";
};

const value1 = ref([]);

// 冻结/解冻
const getButtonText = isPub => {
  return isPub === true ? "解冻" : "冻结";
};

const isSubmitting = ref(false);
</script>

<template>
  <div class="containers">
    <!-- <div class="con_top">
      <div class="titles">领队管理</div>
    </div> -->
    <div class="con_search">
      <el-form :model="form" :inline="true">
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="value1"
            type="daterange"
            start-placeholder="请选择开始时间"
            end-placeholder="请选择结束时间"
            style="width: 310px"
            @change="timeChange"
          />
        </el-form-item>
        <el-form-item label="课期名">
          <el-input
            v-model.trim="form.name"
            placeholder="请输入"
            clearable
            style="width: 180px"
          />
        </el-form-item>
        <el-form-item label="实践点">
          <el-input
            v-model.trim="form.complexName"
            placeholder="请输入"
            clearable
            style="width: 180px"
          />
        </el-form-item>
        <el-form-item label="课期状态">
          <el-select
            v-model="form.coursePeriodState"
            placeholder="请选择"
            style="width: 180px"
            clearable
          >
            <el-option
              v-for="item in stateOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label=" ">
          <div class="flex">
            <el-button type="primary" @click="searchData">搜索</el-button>
            <el-button @click="setData">重置</el-button>
            <el-button
              type="primary"
              style="margin-left: 10px"
              @click="
                router.push({
                  path: '/institution/invite/course',
                  query: { type: 'teachers' }
                })
              "
            >
              邀请参课
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div class="con_table">
      <el-table
        :data="tableData"
        :max-height="tableHeight"
        :header-cell-style="{ backgroundColor: '#fafafa', color: '#565353' }"
      >
        <el-table-column prop="name" label="课期名">
          <template #default="scope">
            {{ scope.row.name || "--" }}
          </template>
        </el-table-column>
        <el-table-column prop="termNumber" label="期号" align="left">
          <template #default="scope">
            <div>
              {{ scope.row.termNumber || "--" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column width="200px" prop="openTime" label="上课时间">
          <template #default="scope">
            <div>
              {{ formatTime(scope.row?.openTime, "YYYY-MM-DD HH:mm") || "--" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column width="200px" prop="createdAt" label="创建时间">
          <template #default="scope">
            <div>
              {{ formatTime(scope.row?.createdAt, "YYYY-MM-DD HH:mm") || "--" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="complex" label="实践点" align="left">
          <template #default="scope">
            <div>
              {{ scope.row.complex?.name || "--" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="leaderList" label="领队" align="left">
          <template #default="scope">
            <div>
              {{ scope.row.name || "--" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="lecturerList" label="讲师" align="left">
          <template #default="scope">
            <div>
              {{ scope.row.name || "--" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="专家" align="left">
          <template #default="scope">
            <div>
              {{ scope.row.name || "--" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="coursePeriodState" label="课期状态">
          <template #default="scope">
            <div
              :style="{
                color: scope.row.freeze === true ? 'red' : ''
              }"
            >
              {{
                scope.row?.coursePeriodState
                  ? COURSE_PERIOD_ENUM[scope.row?.coursePeriodState].label
                  : "--"
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="left">
          <template #default="scope">
            <div>
              <el-button
                type="primary"
                link
                @click="
                  router.push({
                    path: '/institution/course/current/detail',
                    query: { id: scope.row.id }
                  })
                "
              >
                详情
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="con_pagination">
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="params.page"
          v-model:page-size="params.size"
          class="pagination"
          background
          :page-sizes="[15, 30, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="params.totalElements"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 88vh;
  overflow: hidden;

  .con_search {
    padding: 20px 20px 2px 20px;
    background-color: #fff;
    margin-bottom: 20px;
    overflow: auto;
  }

  .con_table {
    background: #fff;
    padding: 20px;
    box-sizing: border-box;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .btnse {
      color: #409eff;
      width: 100%;
      display: flex;
      justify-content: flex-end;
      margin-bottom: 20px;
    }

    .el-table {
      flex: 1;
      overflow: auto;
    }
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    margin-top: 20px;
    flex-wrap: wrap;

    :deep(.el-pagination) {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-end;
      row-gap: 10px;

      .el-pagination__sizes,
      .el-pagination__jump {
        margin-bottom: 0;
      }
    }
  }
  .eye_style {
    width: 120px;
    // align-items: center;
    // justify-content: center;
    .eye {
      float: right;
      cursor: pointer;
      :hover {
        color: #409eff;
      }
    }
  }
}
</style>
