<script setup>
import {
  ref,
  onMounted,
  computed,
  watch,
  nextTick,
  onBeforeUnmount
} from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { Hide, View } from "@element-plus/icons-vue";
import { formatTime, downloadFileBySaveAs } from "@/utils/index";
import { decrypt } from "@/utils/SM4.js";
import FileItem from "@/components/PreviewV2/FileItem.vue";
import { teacherDataFindId } from "@/api/teachers/teacherResourcePool.js";
import { findByTeacherId } from "@/api/teachers/invitationManagement.js";
import { getFileFullUrl } from "@/utils/fileTools";
import { removeEmptyValues } from "@iceywu/utils";

// 全局错误处理器
const handleGlobalError = event => {
  const errorMsg = event?.error?.message || event?.message;
  if (
    errorMsg &&
    errorMsg.includes(
      "Cannot read properties of null (reading 'querySelector')"
    )
  ) {
    console.warn("已捕获DOM查询错误，这不会影响功能");
    event.preventDefault(); // 阻止错误冒泡
  }
};

// 安装全局错误处理器
const setupErrorHandling = () => {
  window.addEventListener("error", handleGlobalError);
};

// 移除全局错误处理器
const cleanupErrorHandling = () => {
  window.removeEventListener("error", handleGlobalError);
};

const router = useRouter();
const route = useRoute();
const getListLoading = ref(false);

// 详情数据
const detailData = ref({
  id: "",
  name: "", // 姓名
  teacherNumber: "", // 教师编号
  age: "", // 年龄
  gender: "", // 性别
  phone: "", // 联系方式
  politicalStatus: "", // 政治面貌
  idType: "", //证件类型
  idNumber: "", // 身份证号
  healthCertificate: "", // 健康证明
  noCrimeCertificate: "", // 无犯罪证明
  mentalHealthCertificate: "", // 心理健康证明
  educationLevel: "", // 学历
  major: "", // 所学专业
  employeeStatus: "", // 在职
  teacherYear: "", // 从教时长
  professionalTitle: "", // 职称等级
  teacherCertificateNumber: "", // 教师资格证号码
  graduationCertificate: "", // 毕业证
  teachableCourse: "", // 可任教课程
  avatar: "", // 头像
  goodAt: "", // 擅长课程
  hobbies: "", // 兴趣爱好
  personBio: "", // 个人风采
  files: [] // 文件集合
});

// 文件集合
const fileData = ref({
  avatar: [], // 头像
  healthCertificate: [], // 健康证明
  noCrimeCertificate: [], // 无犯罪证明
  mentalHealthCertificate: [], // 心理健康证明
  graduationCertificate: [], // 毕业证
  teacherCertificate: [], // 教师资格证
  personBio: [] // 个人风采
});

// 性别映射
const genderMap = {
  1: "男",
  2: "女",
  0: "未知"
};

// 职称级别映射
const professionalTitleMap = {
  UNRATED: "未定级",
  JUNIOR: "初级",
  INTERMEDIATE: "中级",
  ASSOCIATE_SENIOR: "副高级",
  SENIOR: "高级"
};

// 基本信息表单项配置
const basicInfoFields = ref([
  {
    label: "教师姓名",
    prop: "name"
  },
  {
    label: "教师编号",
    prop: "teacherNumber"
  },
  {
    label: "年龄",
    prop: "age"
  },
  {
    label: "性别",
    prop: "gender",
    formatter: value => genderMap[value] || "--"
  },
  {
    label: "联系方式",
    prop: "phone"
  },
  {
    label: "政治面貌",
    prop: "politicalStatus"
  },
  {
    label: "证件类型",
    prop: "idType"
  },
  {
    label: "身份证号",
    prop: "idNumber"
  },
  {
    label: "健康证明",
    prop: "healthCertificate",
    type: "file"
  },
  {
    label: "无犯罪证明",
    prop: "noCrimeCertificate",
    type: "file"
  },
  {
    label: "心理健康筛查证明",
    prop: "mentalHealthCertificate",
    type: "file"
  }
]);

// 教学背景表单项配置
const teachingBackgroundFields = ref([
  {
    label: "学历",
    prop: "educationLevel"
  },
  {
    label: "所学专业",
    prop: "major"
  },
  {
    label: "在职",
    prop: "employeeStatus"
  },
  {
    label: "从教时长",
    prop: "teacherYear",
    formatter: value => (value ? `${value}年` : "--")
  },
  {
    label: "职称级别",
    prop: "professionalTitle",
    formatter: value => professionalTitleMap[value] || "--"
  },
  {
    label: "教师资格证号码",
    prop: "teacherCertificateNumber"
  },
  {
    label: "教师资格证",
    prop: "teacherCertificate",
    type: "file"
  },
  {
    label: "毕业证",
    prop: "graduationCertificate",
    type: "file"
  },
  {
    label: "可任教课程",
    prop: "teachableCourse"
  }
]);

// 个人风采表单项配置
const personalInfoFields = ref([
  {
    label: "个人头像",
    prop: "avatar",
    type: "image"
  },
  {
    label: "擅长课程",
    prop: "goodAt",
    type: "text"
  },
  {
    label: "兴趣爱好",
    prop: "hobbies",
    type: "text"
  },
  {
    label: "个人风采",
    prop: "personBio",
    type: "images"
  }
]);

// 页面分区数据
const sectionsList = computed(() => [
  {
    title: "基本信息",
    column: 2,
    fields: basicInfoFields.value
  },
  {
    title: "教学背景",
    column: 2,
    fields: teachingBackgroundFields.value
  },
  {
    title: "个人风采",
    column: 1,
    fields: personalInfoFields.value
  }
]);

// 判断是否显示审核信息
const showAuditInfo = computed(() => {
  return !!route.query.reviewId;
});

// 处理文件数据
const processFileData = data => {
  // 清空所有文件数组
  Object.keys(fileData.value).forEach(key => {
    fileData.value[key] = [];
  });

  // 处理头像
  if (data.avatar && data.avatar.uploadFile) {
    fileData.value.avatar = [
      {
        url: data.avatar.uploadFile.url,
        name: data.avatar.uploadFile.fileName || "头像",
        fileName: data.avatar.uploadFile.fileName || "头像",
        fileIdentifier: data.avatar.uploadFile.fileIdentifier,
        status: "success"
      }
    ];
  }

  // 处理其他文件
  if (data.files && Array.isArray(data.files)) {
    data.files.forEach(item => {
      // 获取uploadFile中的文件信息
      const fileInfo = item.uploadFile || {};

      // 确保文件对象有必要的属性
      const fileObj = {
        ...fileInfo,
        url: fileInfo.url || "",
        name: fileInfo.fileName || "未命名文件",
        fileName: fileInfo.fileName || "未命名文件",
        fileIdentifier: fileInfo.fileIdentifier || "",
        uid: fileInfo.uploadId || Date.now() + Math.random(),
        status: "success"
      };

      switch (item.fileType) {
        case "MEDICAL_CERT":
          fileData.value.healthCertificate.push(fileObj);
          break;
        case "NO_CRIME_CERT":
          fileData.value.noCrimeCertificate.push(fileObj);
          break;
        case "MENTAL_HEALTH_CERT":
          fileData.value.mentalHealthCertificate.push(fileObj);
          break;
        case "GRADUATION_CERT":
          fileData.value.graduationCertificate.push(fileObj);
          break;
        case "TEACHER_CERT":
          fileData.value.teacherCertificate.push(fileObj);
          break;
        case "PERSON_BIO":
          fileData.value.personBio.push(fileObj);
          break;
      }
    });
  }
};

// 下载文件
const downloadFile = async file => {
  if (!file || !file.url) {
    ElMessage.warning("暂无文件下载");
    return;
  }

  try {
    const downloadUrl = await getFileFullUrl(file.url);
    const fileName = file.fileName || file.name || "未命名文件";
    downloadFileBySaveAs(downloadUrl, fileName);
  } catch (error) {
    console.error("文件下载失败:", error);
    ElMessage.error("文件下载失败");
  }
};
// 处理可能的DOM操作相关错误
const safelyRunDomOperations = async () => {
  try {
    // 延迟执行以确保DOM完全加载
    await new Promise(resolve => setTimeout(resolve, 100));
    await nextTick();
    // DOM已准备好，可以安全执行DOM操作
  } catch (error) {
    console.error("DOM操作错误:", error);
  }
};

// 根据id查询，使用getTeacherDetail接口获取教师详情
const getData = async () => {
  const params =
    route.query.type === "invite"
      ? { teacherId: route.query.id }
      : { id: route.query.id };
  if (route.query.id) {
    try {
      getListLoading.value = true;
      const api =
        route.query.type === "invite" ? findByTeacherId : teacherDataFindId;
      const { code, data } = await api(params);
      if (code === 200 && data) {
        // 填充详情数据
        detailData.value = {
          ...detailData.value,
          ...data
        };

        // 处理手机号和身份证号数据 - 使用加密数据解密后显示
        if (data.phoneCt) {
          detailData.value.phone = decrypt(data.phoneCt);
        }

        if (data.idNumberCt) {
          detailData.value.idNumber = decrypt(data.idNumberCt);
        }

        // 处理文件数据
        processFileData(data);
      } else {
        ElMessage.error("获取教师详情失败");
      }
    } catch (error) {
      console.error("获取数据失败:", error);
      ElMessage.error(error.message || "获取数据失败");
    } finally {
      getListLoading.value = false;
    }
  }
};
onMounted(async () => {
  setupErrorHandling();
  getData();
  await safelyRunDomOperations();
});

onBeforeUnmount(() => {
  cleanupErrorHandling();
});
</script>

<template>
  <div class="page-layout">
    <div class="content-container">
      <el-scrollbar class="content-area">
        <!-- 信息部分循环 -->
        <template
          v-for="(section, sectionIndex) in sectionsList"
          :key="sectionIndex"
        >
          <div class="title-text">{{ section.title }}</div>
          <el-descriptions
            title=""
            :column="section.column || 2"
            border
            :label-width="'200px'"
            class="details-container"
          >
            <el-descriptions-item
              v-for="(item, index) in section.fields"
              :key="index"
              label-align="right"
              :label-class-name="
                item.type === 'file' ||
                item.type === 'image' ||
                item.type === 'images'
                  ? 'my-label upload-label'
                  : 'my-label'
              "
              :span="item.span || 1"
              class="description-item"
            >
              <template #label>{{ item.label }}</template>

              <!-- 普通文本显示 -->
              <template v-if="!item.type || item.type === 'text'">
                <div class="detail-text">
                  {{
                    item.formatter
                      ? item.formatter(detailData[item.prop])
                      : detailData[item.prop] || "--"
                  }}
                </div>
              </template>

              <!-- 图片显示(头像) -->
              <template v-else-if="item.type === 'image'">
                <div
                  v-if="fileData[item.prop]?.length > 0"
                  class="image-preview"
                >
                  <el-image
                    :src="fileData[item.prop][0].url"
                    :preview-src-list="[fileData[item.prop][0].url]"
                    fit="cover"
                    class="avatar-image"
                  />
                </div>
                <div v-else class="detail-text">--</div>
              </template>

              <!-- 多图片显示(个人风采) -->
              <template v-else-if="item.type === 'images'">
                <div
                  v-if="fileData[item.prop]?.length > 0"
                  class="images-preview"
                >
                  <div
                    v-for="(image, imgIndex) in fileData[item.prop]"
                    :key="imgIndex"
                    class="image-item"
                  >
                    <el-image
                      :src="image.url"
                      :preview-src-list="
                        fileData[item.prop].map(img => img.url)
                      "
                      fit="contain"
                      class="preview-image"
                    />
                  </div>
                </div>
                <div v-else class="detail-text">--</div>
              </template>

              <!-- 文件显示 -->
              <template v-else-if="item.type === 'file'">
                <div
                  v-if="fileData[item.prop]?.length > 0"
                  class="file-display"
                >
                  <template
                    v-for="(file, fileIndex) in fileData[item.prop]"
                    :key="fileIndex"
                  >
                    <FileItem
                      :data="file"
                      :index="fileIndex"
                      style="width: 100%; min-width: 130px"
                    />
                  </template>
                </div>
                <div v-else class="detail-text">--</div>
              </template>
            </el-descriptions-item>
            <!-- 添加占位符，确保最后一行的单个项目只占一半宽度 -->
            <el-descriptions-item
              v-if="section.column === 2 && section.fields.length % 2 !== 0"
              class-name="placeholder-cell"
              label-class-name="placeholder-cell"
            />
          </el-descriptions>
        </template>
      </el-scrollbar>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.page-layout {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 88vh;
  min-height: calc(100vh - 150px); /* 适应页面高度，减去顶部导航等高度 */
}

.audit-info {
  background: #fff;
  padding: 20px;
  flex-shrink: 0;
}

.content-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  background: #fff;
  padding: 20px;
  overflow: hidden;
}

.content-area {
  flex: 1;
  overflow: auto;
}

.audit-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

@media (max-width: 768px) {
  .audit-info-grid {
    grid-template-columns: 1fr;
  }
}

.audit-info .info-item {
  font-size: 14px;
  line-height: 2;
  color: #606266;
}

.audit-info .status-text {
  color: #409eff;
}

.audit-info .status-text.rejected {
  color: rgb(245, 108, 108);
}

.audit-info .status-text.cancel {
  color: inherit;
}

.footer {
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
  font-size: 14px;
}

/* 内容容器样式 */
.content-container {
  background: #fff;
  padding: 20px;
}

/* 调整滚动条高度，考虑到审核信息可能存在 */
.scrollbar {
  height: v-bind(
    'showAuditInfo ? "calc(100vh - 240px)" : "calc(100vh - 190px)"'
  );
}

.title-text {
  color: #0e1832;
  font-family: Source Han Sans CN;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  margin: 20px 0 20px 10px;

  &::after {
    content: "";
    flex: 1;
    height: 1px;
    background: #e4e7ed;
    margin-left: 16px;
  }
}

.footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  font-size: 14px;

  .footer_right {
    display: flex;
    gap: 10px; /* 按钮之间的间距 */
  }
}

/* 确保描述项容器布局正确 */
:deep(.details-container) {
  width: 100%;

  .el-descriptions__table {
    width: 100%;
    table-layout: fixed;
  }

  .el-descriptions__cell {
    width: 50%;
  }

  .el-descriptions__label {
    width: 200px;
  }

  .el-descriptions__content {
    width: calc(100% - 200px);
  }
}

:deep(.report-btn) {
  height: 30px !important;
}
:deep(.report-input) {
  height: 32px !important;
}

/* 确保两列布局平均分配空间 */
:deep(.el-descriptions__body) {
  .el-descriptions__table {
    width: 100%;

    .el-descriptions__row {
      display: table-row;

      .el-descriptions__cell {
        display: table-cell;
        width: 50%;
      }
    }
  }
}

:deep(.my-label) {
  background: #fff !important;
  min-width: 150px !important;
}

:deep(.placeholder-cell) {
  opacity: 0;
}

:deep(
  .el-descriptions__body
    .el-descriptions__table.is-bordered
    .el-descriptions__cell
) {
  border: 0px !important;
}

:deep(.el-descriptions-item__label) {
  vertical-align: middle !important;
  padding-top: 0 !important;
  width: 200px;
}

:deep(.el-descriptions__cell) {
  padding: 12px 0;
}

/* 详情文本样式 */
.detail-text {
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  padding: 4px 0;
}

/* 文件显示样式 */
.file-display {
  width: 100%;

  :deep(.file-item) {
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

/* 可点击的文件名样式 */
.clickable {
  cursor: pointer;
  color: #409eff;
  text-decoration: none;
  transition: color 0.3s;
}

.clickable:hover {
  color: #66b1ff;
  text-decoration: underline;
}

/* 头像图片样式 */
.image-preview {
  display: flex;
  align-items: center;

  .avatar-image {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    border: 1px solid #ebeef5;
  }
}

/* 多图片预览样式 */
.images-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;

  .image-item {
    width: 150px;
    height: 150px;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;

    .preview-image {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
}

/* 添加审核信息相关样式 */
.audit-info {
  background: #fff;
  padding: 20px;
  flex-shrink: 0;
}

.audit-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

@media (max-width: 768px) {
  .audit-info-grid {
    grid-template-columns: 1fr;
  }
}

.audit-info .info-item {
  font-size: 14px;
  line-height: 2;
  color: #606266;
}

.audit-info .status-text {
  color: #409eff;
}

.audit-info .status-text.rejected {
  color: rgb(245, 108, 108);
}

.audit-info .status-text.cancel {
  color: inherit;
}
</style>
