import { http } from "@/utils/http";

/*  师资库相关接口  */
// 分页查询
export const teacherDataFindAll = params => {
  return http.request(
    "get",
    "/organization/teacherDatabase/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 详情
export const teacherDataFindId = params => {
  return http.request(
    "get",
    "/organization/teacherDatabase/findById",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 专家评价查询
export const teacherDatabaseCommentsAll = params => {
  return http.request(
    "get",
    "/organization/teacherDatabaseComments/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 邀请常驻
export const teachersIsResident = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/teacherDatabase/inviteResident",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "TEACHER_DATABASE",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "邀请常驻"
      }
    }
  );
};
// 结束常驻
export const teachersEndResident = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/teacherDatabase/endResident",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "TEACHER_DATABASE",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "结束常驻"
      }
    }
  );
};
// 邀请参课
export const teachersInviteClass = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/teacherDatabase/inviteClass",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "TEACHER_DATABASE",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "邀请参与课程"
      }
    }
  );
};
// 字典查询学历和专业
export const findModelChildrenByParentId = params => {
  return http.request(
    "get",
    "/common/dict/findModelChildrenByParentId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 参与课程
export const joinFindAllCoursePeriod = params => {
  return http.request(
    "get",
    "/organization/teacherDatabaseCoursePeriod/findAllCoursePeriodByTeacherId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
