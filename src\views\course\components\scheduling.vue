<script setup>
import { onMounted, ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { findByCoursePeriodId } from "@/api/period.js";
import { requestTo } from "@/utils/http/tool";
import dayjs from "dayjs";
import { courseStore } from "@/store/modules/course.js";
import { ItineraryFindByDraftId } from "@/api/drafts.js";

const props = defineProps({
  draftId: {
    type: String,
    default: ""
  }
});
const useCourseStore = courseStore();
const router = useRouter();
const route = useRoute();

const refName = ref("行程安排");
const tripList = ref([]);
// 编辑行程
const editeTrip = () => {
  router.push({
    path: "/course/currentDetails/tripEdite",
    query: { periodId: route.query.periodId }
  });
};
const tableData = ref([]);
// 课期行程列表查询
const findCoursePeriodList = async type => {
  const params = {
    coursePeriodId: route.query.periodId
  };
  let [err, res] = await requestTo(findByCoursePeriodId(params));
  if (res) {
    console.log("🌳-----res-----", res);
    tripList.value = res;
  } else {
    console.log("🌵-----err----11-", err);
  }
};

// 草稿箱 课期行程列表查询V2
const findVt = async type => {
  const params = {
    draftId: props.draftId
  };
  let [err, res] = await requestTo(ItineraryFindByDraftId(params));
  if (res) {
    // console.log("🌳-----res-----", res);
    tripList.value = res;
  } else {
    // console.log("🌵-----err-----", err);
  }
};
onMounted(() => {
  if (props.draftId === "") {
    findCoursePeriodList();
  } else {
    findVt();
  }
});
</script>

<template>
  <div class="scheduling">
    <div v-if="tripList?.length" class="content overflow-y-auto">
      <el-timeline style="max-width: 600px">
        <el-timeline-item
          v-for="item in tripList"
          :key="item.id"
          :timestamp="item.timestamp"
        >
          <div v-if="props.draftId !== ''" class="timeLine_title">
            <span class="m-w">{{ item.title }}</span>
            <span class="m-w">{{ `第 ${item.gapDays} 天` }}</span>
            <span class="m-w">{{ item.time }}</span>
          </div>
          <div v-else class="timeLine_title">
            <span class="m-w">{{ item.title }}</span>
            <span class="m-w">{{
              `${dayjs(item.startTime).format("YYYY-MM-DD")} ` +
              `(${dayjs(item.startTime).format("dddd")})`
            }}</span>
            <span class="m-w">{{
              dayjs(item.startTime).format("HH:mm:ss")
            }}</span>
          </div>
          <div class="item_content">
            <div v-html="item.content" />
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>

    <el-empty v-else description="暂无数据" />
    <div class="btn_box">
      <el-button
        v-if="false"
        type="primary"
        class="editeBtn"
        @click="editeTrip"
      >
        编辑行程
      </el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.scheduling {
  height: 100%;
  width: 100%;
  position: relative;

  .content {
    width: 100%;
    height: 91%;
    overflow-y: auto;
    padding: 10px 0 10px 0;
    box-sizing: border-box;
    // scrollbar-width: none;
    // -ms-overflow-style: none;
    // background-color: rgba(255, 0, 0, 0.418);
    .timeLine_title {
      width: 100%;
      display: flex;
      align-items: center;
      gap: 20px;
    }
    .item_content {
      margin-top: 20px;
      width: 100%;
      padding-left: 30px;

      // 添加表格样式
      :deep(table) {
        border-collapse: collapse;
        // width: 100%;
        // margin: 10px 0;

        td,
        th {
          border: 1px solid #a7a6a6;
          // padding: 8px;
          min-width: 50px;
          text-align: center;
        }
      }
    }
  }

  // .content::-webkit-scrollbar {
  //   // display: none;
  //   // width: 8px;
  // }

  .content::-webkit-scrollbar-track {
    background: #f1f1f1; /* 滚动条轨道背景 */
  }

  .content::-webkit-scrollbar-thumb {
    background: #afafaf; /* 滚动条滑块颜色 */
  }

  .content::-webkit-scrollbar-thumb:hover {
    background: #7c7c7c; /* 滚动条滑块悬停时的颜色 */
  }
  .btn_box {
    position: absolute;
    // left: 20px;
    bottom: 0;
  }
  .m-w {
    min-width: fit-content;
    font-size: 16px;
    font-weight: bold;
  }
}
</style>
