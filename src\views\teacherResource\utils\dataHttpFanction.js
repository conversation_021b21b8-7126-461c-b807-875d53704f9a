import {
  teacherDataFindAll,
  teachersIsResident,
  findModelChildrenByParentId
} from "@/api/teachers/teacherResourcePool.js";
import { to } from "@iceywu/utils";
const processData = nodes => {
  if (!Array.isArray(nodes)) {
    console.warn("Expected an array but got:", nodes);
    return [];
  }
  return nodes.map(node => {
    const { leaf, ...newNode } = node;
    if (
      newNode.children &&
      Array.isArray(newNode.children) &&
      newNode.children.length > 0
    ) {
      newNode.children = processData(newNode.children);
    }
    return newNode;
  });
};
export const getFindModelChildren = async id => {
  const [err, res] = await to(findModelChildrenByParentId({ parentId: id }));
  if (res.code === 200) {
    const resData = processData(res.data);
    // console.log("🌳resData------------------------------>", resData);
    return resData || [];
  } else {
    console.log(res?.err);
    return [];
  }
};
