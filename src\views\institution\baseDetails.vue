<script setup>
import { ref, onMounted, reactive, onBeforeMount, onBeforeUnmount } from "vue";
import { useRouter, useRoute } from "vue-router";
import RichEditor from "@/components/Base/RichEditor.vue";
import { complexFindById, findArea } from "@/api/institution";
import { ElMessage } from "element-plus";
import { formatTime } from "@/utils/index";
import { Hide, View } from "@element-plus/icons-vue";
import { decrypt, encryption } from "@/utils/SM4.js";
import { template } from "lodash";
import FileItem from "@/components/PreviewV2/FileItem.vue";

const router = useRouter();
const route = useRoute();
const form = ref({});

const formRef = ref(null);
const richFlag = ref(false);
const adminId = ref(null);
onMounted(() => {
  adminId.value = route.query.id;
  getData();
  richFlag.value = true;
});

const formType = {
  MEETING_ROOM: "会议室",
  CLASSROOM: "教室",
  LOBBY: "大厅",
  SQUARE: "广场",
  PARK: "公园",
  ATTRACTION: "景点",
  OUTDOORS: "户外",
  OTHER: "其他"
};

const formFile = ref({
  organizationName: [],
  video: []
});

const formData = ref([
  {
    label: "实践点名称",
    type: "text",
    prop: "name",
    check: true,
    placeholder: "请输入机构名称",
    width: "200px"
  },
  {
    label: "机构",
    type: "text",
    // check: true,
    prop: "organizationName",
    placeholder: "请输入机构别名",
    width: "200px"
  },
  {
    label: "创建时间",
    type: "text",
    // check: true,
    prop: "createdAt",
    placeholder: "请输入机构别名",
    width: "200px",
    rowspan: 2
  },
  {
    label: "归属地信息",
    type: "text",
    // check: true,
    prop: "address",
    placeholder: "请输入机构别名",
    width: "200px",
    rowspan: 2
  },
  {
    label: "详细地址",
    type: "text",
    // check: true,
    prop: "detailedAddress",
    placeholder: "请输入机构别名",
    width: "200px",
    rowspan: 2
  },
  {
    label: "实践点联系人",
    type: "text",
    // check: true,
    prop: "emergencyPeople",
    placeholder: "请输入客服热线",
    width: "200px"
  },
  {
    label: "实践点联系电话",
    type: "text",
    // check: true,
    prop: "emergencyPhone",
    placeholder: "请输入客服热线",
    width: "200px"
  },
  {
    label: "类型",
    type: "textString",
    prop: "complexType",
    placeholder: "请输入机构别名",
    width: "200px",
    rowspan: 2
  },
  {
    label: "标签",
    type: "textString",
    prop: "tags",
    placeholder: "请输入机构别名",
    width: "200px",
    rowspan: 2
  },
  {
    label: "一句话简介",
    type: "text",
    prop: "oneSentenceIntroduction",
    placeholder: "请输入机构别名",
    width: "200px",
    rowspan: 2
  },
  {
    label: "实践点介绍",
    type: "editor",
    // check: true,
    prop: "introduction",
    placeholder: "",
    width: "200px",
    rowspan: 2
  },
  {
    label: "照片",
    type: "images",
    type2: "images",
    prop: "organizationName",
    placeholder: "请输入机构别名",
    width: "200px",
    rowspan: 2
  },
  {
    label: "视频",
    type: "video",
    type2: "video",
    prop: "video",
    placeholder: "请输入机构别名",
    width: "200px",
    rowspan: 2
  }
]);
const newData = ref();

// 根据id查询
const getData = async () => {
  let params = { id: route.query.id };
  try {
    const { code, data, msg } = await complexFindById(params);
    // console.log("🎁-----data-----", code, data, msg);
    if (code == 200 && data) {
      form.value = data;
      newData.value = JSON.parse(JSON.stringify(data));
      // 文件集合回显
      if (data?.files) {
        data.files.forEach(item => {
          // console.log("🐳-----item-----", item);
          if (item.fileType === "PHOTO") {
            formFile.value.organizationName.push(item.uploadFile);
          } else if (item.fileType === "PROMOTIONAL_VIDEO") {
            formFile.value.video.push(item.uploadFile);
          }
        });
      }
      // 查询归属地
      await initAddressEcho(data);
    }
  } catch (error) {
    console.log("catch-----error-----", error);
  }
};
// 编辑
const edit = () => {
  router.push({
    path: "/institution/baseEdit",
    query: { id: adminId.value }
  });
};
//返回上一页
const cancel = () => {
  router.go(-1);
};
const isView = ref(true);
const isViewFn = () => {
  isView.value = !isView.value;
  if (isView.value) {
    form.value.emergencyPhone = newData.value.emergencyPhone;
  } else {
    form.value.emergencyPhone = decrypt(newData.value.emergencyPhoneCt);
  }
};
// 数组字符串处理
const findTypeItem = obj => {
  for (const i in formType) {
    if (obj === i) {
      return formType[i];
    }
  }
};
// 获取图片URL列表用于预览
const getImageUrlList = prop => {
  if (!formFile.value[prop] || formFile.value[prop].length === 0) {
    return [];
  }
  return formFile.value[prop].map(item => item.url);
};

const initAddressEcho = async data => {
  console.log("🎉-----data-----", data);
  if (data?.district && data.city && data.province) {
    const addressArray = [];

    try {
      // 设置地址数组
      if (data.city !== "直辖市") {
        form.value.address = [
          data.province,
          data.city,
          data.district,
          data.street
        ];
      } else {
        form.value.address = [data.province, data.district, data.street];
      }
      const list = await getCascaderLabelsByIds(form.value.address, findArea);
      form.value.address = list.join("/");
    } catch (error) {
      console.error("获取地址ID失败:", error);
    }
  }
};

// 通用的函数
const getCascaderLabelsByIds = async (
  idArray,
  apiFunction,
  parentIdField = "parentId"
) => {
  if (!idArray || !Array.isArray(idArray) || idArray.length === 0) {
    return [];
  }

  const labels = [];

  try {
    for (let i = 0; i < idArray.length; i++) {
      const id = idArray[i];
      let parentId = 0;

      if (i > 0) {
        parentId = idArray[i - 1];
      }

      const { code, data } = await apiFunction({ [parentIdField]: parentId });

      if (code === 200 && data && Array.isArray(data)) {
        const item = data.find(
          area => area.id === id || String(area.id) === String(id)
        );
        if (item) {
          labels.push(item.name);
        } else {
          labels.push("未知");
        }
      } else {
        labels.push("未知");
      }
    }

    return labels;
  } catch (error) {
    console.error("获取级联选择器标签失败:", error);
    return [];
  }
};
</script>

<template>
  <div class="main">
    <el-scrollbar class="scrollbar">
      <div>
        <!-- <el-form ref="formRef" :model="form" :rules="rules"> -->
        <el-descriptions title="" :column="2" border :label-width="'200px'">
          <el-descriptions-item
            v-for="(item, index) in formData"
            :key="index"
            label-align="center"
            label-class-name="my-label"
            :span="item.rowspan || 1"
          >
            <template #label>
              {{ item.label }}
            </template>
            <el-form-item
              :prop="item.prop"
              :inline-message="item.check"
              style="margin-bottom: 0"
              :show-message="true"
              error-placement="right"
            >
              <!-- text -->
              <template v-if="item.type === 'text'">
                <div
                  style=""
                  :class="{
                    cell_item: item.prop === 'emergencyPhone' && form[item.prop]
                  }"
                >
                  {{
                    item.prop === "createdAt"
                      ? formatTime(form[item.prop], "YYYY-MM-DD HH:mm:ss")
                      : form[item.prop] || "--"
                  }}
                  <span
                    v-if="item.prop === 'emergencyPhone' && form[item.prop]"
                    class="icon"
                  >
                    <el-icon
                      v-if="isView"
                      style="cursor: pointer"
                      @click="isViewFn"
                    >
                      <Hide />
                    </el-icon>
                    <el-icon v-else style="cursor: pointer" @click="isViewFn">
                      <View />
                    </el-icon>
                  </span>
                </div>
              </template>
              <!-- 富文本 -->
              <template v-else-if="item.type === 'editor'">
                <div style="width: 100%">
                  <RichEditor
                    v-model="form[item.prop]"
                    height="200px"
                    :isOpen="false"
                    :readOnly="true"
                  />
                </div>
              </template>
              <!-- 需要处理字符串 -->
              <template v-else-if="item.type === 'textString'">
                <template v-if="form[item.prop]">
                  <div v-if="item.prop === 'tags'">
                    {{ form[item.prop].join(", ") }}
                  </div>
                  <div v-if="item.prop === 'complexType'">
                    {{ findTypeItem(form[item.prop]) }}
                  </div>
                </template>
                <span v-else>--</span>
              </template>
              <template v-else-if="item.type2 === 'video'">
                <template
                  v-for="(item2, index2) in formFile[item.prop]"
                  :key="index2"
                >
                  <div v-if="item2?.fileName" class="fileOther">
                    <FileItem
                      :data="item2"
                      :index="index2"
                      style="width: 600px; min-width: 130px"
                      @delete="getDeleted(item2, index2)"
                    />
                  </div>
                </template>
                <div v-show="formFile[item.prop]?.length === 0">{{ "--" }}</div>
              </template>
              <template v-else-if="item.type2 === 'images'">
                <text v-if="formFile[item.prop].length === 0">--</text>
                <template v-else>
                  <el-image
                    v-for="(item2, index2) in formFile[item.prop]"
                    :key="index2"
                    :src="item2.url"
                    fit="scale-down"
                    :preview-src-list="getImageUrlList(item.prop)"
                    :initial-index="index2"
                    class="img-pic"
                  />
                </template>
              </template>
              <!-- <template v-else-if="item.type === 'address'">
                <div v-if=""></div>
                <div></div>
              </template> -->
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-scrollbar>

    <div class="account_management">
      <el-button @click="cancel">返回</el-button>
      <el-button type="primary" @click="edit">编辑信息</el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.scrollbar {
  // padding-top: 20px;
  // padding: 20px;
  // padding-bottom: 0px;

  height: calc(100vh - 190px);
  background-color: #fff;
}
.main {
  padding: 20px;
  background: #fff;
  .cell_item {
    display: flex;
    min-width: 110px;
    /* margin-left: 20px; */
    justify-content: space-around;
    align-items: center;
    .icon {
      margin-left: 10px;
    }
  }
}

.header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 20px;

  .title_left {
    display: flex;

    :nth-child(2) {
      margin-left: 40px;
    }
  }

  .title_rigth {
    display: flex;

    :nth-child(2) {
      margin-left: 40px;
    }
  }
}

.account_management {
  margin: 20px 0 0 0px;
  display: flex;
  justify-content: flex-end;
  :nth-child(2) {
    margin-left: 20px;
  }
}

:deep(.my-label) {
  background: #e1f5ff !important;
}

.img-pic {
  width: 120px;
  height: 120px;
  margin: 10px;
  // object-fit: cover;
}
.fileOther {
  // margin-top: 26px;
  margin-left: 20px;
  display: flex;
  align-items: center;
  line-height: 22px;
  width: 420px;
  // margin-bottom: 56px;
  .title {
    color: #0e1832;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    white-space: nowrap;
  }
  .link {
    width: 20px;
    height: 20px;
    margin: 0 10px;
    cursor: pointer;
  }
  .fileName {
    color: #3876fd;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    cursor: pointer;
    // width: 350px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
