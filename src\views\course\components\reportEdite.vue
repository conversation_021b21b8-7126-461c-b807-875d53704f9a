<script setup>
import { onMounted, ref } from "vue";
import { formatTime } from "@/utils/index";
import { createOrUpdatecourseReport } from "@/api/period.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { to } from "@iceywu/utils";
import { courseStore } from "@/store/modules/course.js";
import DescriptionList from "./descriptionList.vue";
const router = useRouter();
const route = useRoute();
const contentData = ref({
  evaluateValue: "",
  resultValue: ""
});

onMounted(() => {
  if (courseStore().evaluate) {
    contentData.value.evaluateValue = courseStore().evaluate;
  }
  if (courseStore().result) {
    contentData.value.resultValue = courseStore().result;
  }
});
const periodName = ref("");
const periodNameEvt = val => {
  periodName.value = val;
};
const submitLoading = ref(false);
// 保存报告
const saveEvt = async () => {
  if (submitLoading.value) return;
  submitLoading.value = true;
  let paramsData = {
    coursePeriodId: Number(route.query.periodId)
  };
  if (contentData.value.evaluateValue) {
    paramsData.performanceEvaluation = contentData.value.evaluateValue;
  }
  if (contentData.value.resultValue) {
    paramsData.achievementSummary = contentData.value.resultValue;
  }
  const operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `编辑了“${periodName.value}”课期中的课程报告`
  };
  // console.log("🌵paramsData------------------------------>", paramsData);
  const [err, result] = await to(
    createOrUpdatecourseReport(paramsData, operateLog)
  );
  // console.log("🎉result------------------------------>", result);
  if (result?.code === 200) {
    ElMessage({
      message: "保存成功",
      type: "success"
    });
    router.replace({
      path: "/course/courseDetails/currentDetails",
      query: {
        infoShow: "课程报告",
        periodId: route.query.periodId,
        courseId: route.query.courseId
      }
    });
  } else {
    ElMessage({
      message: `保存失败,${result?.msg}`,
      type: "error"
    });
  }

  if (err) {
    console.log("err", err);
  }
  submitLoading.value = false;
};
</script>

<template>
  <div>
    <DescriptionList
      :periodId="Number(route.query.periodId)"
      @name="periodNameEvt"
    />
    <div class="containers">
      <div class="content">
        <div class="buttons">
          <!-- <div
          class="cancel"
          @click="
            router.replace({
              path: '/course/courseDetails/currentDetails',
              query: { infoShow: '课程报告', periodId: route.query.periodId }
            })
          "
        >
          取消
        </div>
        <div class="create" @click="saveEvt">
          {{ "保存报告" }}
        </div> -->
          <el-button
            @click="
              router.replace({
                path: '/course/courseDetails/currentDetails',
                query: { infoShow: '课程报告', periodId: route.query.periodId }
              })
            "
          >
            取消
          </el-button>
          <el-button type="primary" :loading="submitLoading" @click="saveEvt">
            {{ "保存报告" }}
          </el-button>
        </div>
        <div class="free">
          <div class="title">学生整体表现评价</div>
          <div class="text">
            <el-input
              v-model.trim="contentData.evaluateValue"
              :rows="6"
              type="textarea"
              resize="none"
              :placeholder="
                contentData.evaluateValue
                  ? contentData.evaluateValue
                  : '请输入学生整体表现评价'
              "
            />
          </div>
        </div>
        <div class="refund">
          <div class="title">教学成果总结</div>
          <div class="text">
            <el-input
              v-model.trim="contentData.resultValue"
              :rows="6"
              type="textarea"
              resize="none"
              :placeholder="
                contentData.resultValue
                  ? contentData.resultValue
                  : '请输入教学成果总结'
              "
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  box-sizing: border-box;
  //   width: calc(100% - 48px);
  // height: 100%;
  padding: 20px;
  height: 650px;
  background: #fff;

  .content {
    // display: flex;
    box-sizing: border-box;
    padding: 0 0 30px 0;
    .buttons {
      display: flex;
      justify-content: flex-end;
      // width: 95%;
      // margin-top: 28vh;
      .cancel {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100px;
        height: 36px;
        color: rgb(255 255 255 / 100%);
        cursor: pointer;
        background-color: rgb(230 152 58 / 100%);
        border-radius: 6px;
        margin-right: 48px;
      }

      .create {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100px;
        height: 36px;
        color: rgb(255 255 255 / 100%);
        cursor: pointer;
        background-color: rgb(64 149 229 / 100%);
        border-radius: 6px;
      }
    }

    .free {
      width: 100%;
      margin-bottom: 30px;
      margin-top: 30px;
    }

    .title {
      margin-bottom: 16px;
      font-size: 14px;
      color: rgb(16 16 16 / 100%);
    }

    .refund {
      width: 100%;
    }

    .text {
      width: 100%;
    }
  }
}
</style>
