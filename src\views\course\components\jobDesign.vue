<script setup>
import { onMounted, ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { requestTo } from "@/utils/http/tool";
import { ElMessage } from "element-plus";
import { findAssignmentDesign } from "@/api/period.js";
import { courseStore } from "@/store/modules/course.js";
import ImgList from "@/components/Base/list1.vue";
import { ImageThumbnail } from "@/utils/imageProxy.js";
import { draftAssignmentDesignFindByDraftId } from "@/api/drafts.js";

const props = defineProps({
  draftId: {
    type: String,
    default: ""
  }
});

const useCourseStore = courseStore();
const router = useRouter();
const route = useRoute();
const evaluateValue = ref("");
const fileListAPi = ref([]);
const showPreview = ref(false);

const srcList = ref([]);
const srcId = ref(0);
// 图片预览
const handleClick = id => {
  showPreview.value = true;
  srcId.value = id;
};
// 查询实践感悟
const getDetail = async () => {
  const params = {
    coursePeriodId: Number(route.query.periodId)
  };

  let [err, res] = await requestTo(findAssignmentDesign(params));
  if (res) {
    evaluateValue.value = res?.content;
    if (res.files?.length) {
      res.files.map(item => {
        fileListAPi.value.push(item?.uploadFile);
        srcList.value.push(item?.uploadFile?.url);
      });
    }
    console.log("🐳-----res-----", res);
  } else {
    console.log("🐬-----err-----", err);
  }
};
// 草稿箱 查询实践感悟V2
const getDetailVt = async () => {
  const params = {
    draftId: Number(props.draftId)
  };

  let [err, res] = await requestTo(draftAssignmentDesignFindByDraftId(params));
  // console.log('🍪-----err, res-----', err, res);
  if (res) {
    evaluateValue.value = res?.content;
    if (res.files?.length) {
      res.files.map(item => {
        fileListAPi.value.push(item?.uploadFile);
        srcList.value.push(item?.uploadFile?.url);
      });
    }
  } else {
    // console.log("🐬-----err-----", err);
  }
};

// 前往编辑实践感悟
const editeEvt = () => {
  router.push({
    path: "/course/currentDetails/homeWorkEdited",
    query: {
      infoShow: "实践感悟",
      periodId: route.query.periodId,
      courseId: route.query.courseId
    }
  });
};
onMounted(() => {
  if (props.draftId === "") {
    getDetail();
  } else {
    getDetailVt();
  }
});
</script>

<template>
  <div class="scheduling">
    <div class="content">
      <el-input
        v-model="evaluateValue"
        :rows="6"
        type="textarea"
        resize="none"
        :disabled="true"
      />
    </div>
    <div class="work-img">
      <div class="title">实践感悟配图</div>
      <ImgList
        v-if="fileListAPi?.length"
        :imgList="fileListAPi"
        :srcList="srcList"
        :itemCount="fileListAPi?.length"
      />
      <!-- <div v-if="fileListAPi?.length" class="banner">
        <el-carousel
          :interval="4000"
          type="card"
          height="200px"
          :autoplay="false"
        >
          <el-carousel-item v-for="(item, index) in fileListAPi" :key="item">
            <img :src="item.url" class="h-full" @click="handleClick(index)" />
          </el-carousel-item>
        </el-carousel>
        <el-image-viewer
          v-if="showPreview"
          :url-list="srcList"
          show-progress
          :initial-index="srcId"
          @close="showPreview = false"
        />
      </div> -->
      <el-empty v-else style="height: 200px" description="暂无配图" />
      <el-button v-if="false" type="primary" class="editeBtn" @click="editeEvt">
        编辑实践感悟
      </el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.scheduling {
  position: relative;
  height: 100%;
  .editeBtn {
    position: absolute;
    bottom: 0;
    left: 0;
  }
  .work-img {
    .title {
      margin-bottom: 6px;
      font-size: 14px;
      color: rgb(16 16 16 / 100%);
    }
  }

  :deep(.el-carousel__item) {
    margin: 0;
    line-height: 200px;
    text-align: center;
  }
}
</style>
