import { defineStore } from "pinia";
import { ref, computed } from "vue";
import { getObjVal } from "@iceywu/utils";
export const useTeachersStore = defineStore(
  "teachers",
  () => {
    // 专家信息
    const teachersInfo = ref([]);
    // 存储专家信息
    const saveteachersInfo = data => {
      teachersInfo.value = data;
    };

    return {
      teachersInfo,
      saveteachersInfo
    };
  },
  {
    persist: {
      key: "teachersInfo"
    }
  }
);
