<script setup>
import { ref, onMounted } from "vue";
onMounted(() => {});
const Examplevideo = ref(
  "https://s.gostaredu.boran-tech.com/gostaredu-cdn/video/institution-instructions-20250409.mp4"
);
</script>

<template>
  <video
    class="videostyle"
    :src="Examplevideo"

     autoplay controls muted playsinline
  />
</template>

<style lang="scss" scoped>
.videostyle {
  width: 100%;
  height: 800px;
}
</style>
