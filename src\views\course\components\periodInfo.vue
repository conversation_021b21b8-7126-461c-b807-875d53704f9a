<script setup>
import { ref } from "vue";
const props = defineProps({
  tableHeader: {
    type: Array,
    default: () => []
  },
  url: {
    type: String,
    default: ""
  },
  srcList: {
    type: Array,
    default: () => []
  }
});
</script>

<template>
  <div class="info">
    <div v-if="tableHeader?.length" class="curse-table">
      <el-descriptions class="descriptions" title="" :column="4" border>
        <template v-for="(item, index) in tableHeader" :key="index">
          <el-descriptions-item width="120px" label-align="center">
            <template #label>
              <div class="cell-item">{{ item.label }}</div>
            </template>
            {{ item.value }}
          </el-descriptions-item>
        </template>
      </el-descriptions>
      <div class="img">
        <!-- <img src="@/assets/user.jpg" alt="" /> -->
        <el-image
          :src="url"
          :zoom-rate="1.2"
          :max-scale="7"
          :min-scale="0.2"
          :preview-src-list="srcList"
          show-progress
          :initial-index="0"
          fit="cover"
          class="img-pic"
          :hide-on-click-modal="true"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.info {
  background-color: #fff;
  box-sizing: border-box;
  padding: 20px 20px;
  margin-bottom: 20px;
  .con_top {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
  }
  .curse-table {
    display: flex;
    justify-content: space-between;
    width: 100%;
    .descriptions {
      width: 90%;
    }
    .img {
      width: 145px;
      //   min-height: 120px;
      height: 120px;
      margin-left: 20px;

      .img-pic {
        width: 145px;
        height: 120px;
        // object-fit: cover;
      }
    }
  }
}
:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}
</style>
