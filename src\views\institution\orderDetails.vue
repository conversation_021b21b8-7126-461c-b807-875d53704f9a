<script setup>
import { ref, onMounted, computed, onBeforeMount, onBeforeUnmount } from "vue";
import { useRouter, useRoute } from "vue-router";
import RichEditor from "@/components/Base/RichEditor.vue";
import {
  getOrderDetails,
  findByOrdersId,
  confirmRefund
} from "@/api/orderManagement";
import { ElMessage, ElMessageBox } from "element-plus";
import { formatTime } from "@/utils/index";
import { Hide, View } from "@element-plus/icons-vue";
import { decrypt, encryption } from "@/utils/SM4.js";
import uploadImg from "@/assets/login/upload1.png";
import { uploadFile } from "@/utils/upload/upload";
import orderDetails from "@/views/institution/components/orderDetailsComponent.vue";

const router = useRouter();
const route = useRoute();
const form = ref({
  subOrdersDetails: []
});

const richFlag = ref(false);
const subOrdersId = ref(null);
const ordersData = ref([]);
const ordersDataJson = ref("");
onMounted(() => {
  // console.log("🍪-----route.query.data-----", route.query.data);

  // if (route.query.data) {
  //   ordersData.value = JSON.parse(route.query.data);
  //   ordersDataJson.value = route.query.data;
  // }
  // if (sessionStorage.getItem("orderDetailsDataV2")) {
  //   ordersDataJson.value = JSON.parse(
  //     sessionStorage.getItem("orderDetailsDataV2")
  //   );
  //   // sessionStorage.removeItem("orderDetailsDataV2");
  // }
  // console.log("🌵-----ordersDataJson.value-----", ordersDataJson.value);
  // subOrdersId.value = ordersDataJson.value.ordersId;
  subOrdersId.value = route.query.ordersId;
  // getData();
  richFlag.value = true;
});
</script>

<template>
  <div class="main">
    <orderDetails
      v-if="richFlag"
      :adminId="subOrdersId"
      :ordersDataJson="ordersDataJson"
      :styleTyle="true"
    />
  </div>
</template>

<style lang="scss" scoped>
// :deep(.el-descriptions__cell) {
//   width: 34%;
// }
.main {
  padding: 20px;
  background: #fff;
}
</style>
