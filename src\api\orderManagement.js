import { http } from "@/utils/http";

/*  订单管理  */
// 分页查询
export const ordersFindAll = params => {
  return http.request(
    "get",
    "/organization/orders/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 查询子订单详情
export const getOrderDetails = params => {
  return http.request(
    "get",
    "/organization/orders/getOrderDetails",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 根据订单id查询退款记录
export const findByOrdersId = params => {
  return http.request(
    "get",
    "/organization/refundApply/findByOrdersId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 根据子订单id查询退款记录
export const findBySubOrdersId = params => {
  return http.request(
    "get",
    "/organization/refundRecord/findBySubOrdersId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 根据订单id查询退款列表
export const refundFindByOrdersId = params => {
  return http.request(
    "get",
    "/organization/refund/findByOrdersId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 主订单退款
export const confirmRefund = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/orders/confirmRefund",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ORDER_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "退单了主订单"
      }
    }
  );
};
// 子订单退款
export const confirmRefundSub = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/orders/confirmRefundSub",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ORDER_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "退单了子订单"
      }
    }
  );
};
// 子订单退款驳回
export const refundRejectedSub = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/orders/refundRejectedSub",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ORDER_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "驳回了子订单"
      }
    }
  );
};
// 主订单退款驳回
export const refundRejected = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/orders/refundRejected",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ORDER_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "驳回了子订单"
      }
    }
  );
};
// 上传订单文件
export const ordersUploadFile = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/orders/uploadFile",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ORDER_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "上传了订单文件"
      }
    }
  );
};
// 机构端子订单退单
export const refundSub = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/orders/refundSub",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ORDER_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "退单了子订单"
      }
    }
  );
};
// 机构端主订单退单
export const refund = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/orders/refund",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ORDER_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "退单了主订单"
      }
    }
  );
};
