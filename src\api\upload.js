import { http } from "@/utils/http";

// 文件检查
export const fileCheck = data => {
  return http.request("post", "/common/file/check", { data });
};
// 上传文件
export const fileUpload = data => {
  return http.request("post", "/common/file/upload", { data });
};

/** 上传预览文件 */
export const uploadPreviewFile = data => {
  return http.request("post", "/common/file/filePreview", { data });
};
/** 获取预览文件地址 */
export const getFileFullPath = params => {
  return http.request("get", "/common/file/fullPath", { params });
};

/** 上传预览文件V2 */
export const uploadPreviewFileV2 = data => {
  return http.request("post", "/common/file/v2/filePreview", { data });
};

/** 附件预览 */
// `${'https://api.ebag-test.readboy.com/wps'}${'/v1/student/office/url'}`,
export const officeUrl = params => {
  return http.request(
    "get",
    "/v1/student/office/url",
    { params },
    {
      serverName: "filePreviewServer",
      isNeedFullRes: false, // 是否需要返回完整的响应对象
      isNeedToken: false, // 是否需要token
      isNeedEncrypt: false
    }
  );
};
