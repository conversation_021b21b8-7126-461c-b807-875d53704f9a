<script setup>
import { ref, onMounted, computed, watchEffect, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { requestTo } from "@/utils/http/tool";
import uploadImg from "@/assets/login/upload1.png";
import {
  ledeterList,
  editteaInfo,
  getPhonecode,
  verifyPhone,
  fileLeaderList,
  fileLeatureList
} from "@/api/leaderLecturer.js";
import { formatTime } from "@/utils/index";
import {
  uploadFile,
  validateFileType,
  isOverSizeLimit
} from "@/utils/upload/upload";
import { decrypt, encryption } from "@/utils/SM4.js";
import { compareObjects, debounce } from "@iceywu/utils";
import FileItem from "@/components/PreviewV2/FileItem.vue";
import { organizationFindById, verifyUsername } from "@/api/institution";
import { Hide, View, Loading, Warning } from "@element-plus/icons-vue";
import WxQrCode from "@/components/WxQrCode/index.vue";
import { getBindCode, unbindWxCode } from "@/api/user.js";
import qrcode from "qrcode";

const router = useRouter();
const route = useRoute();
const url = ref("https:/www.baidu.com/");
const qrCodeData = ref("");
const richFlag = ref(false);
const dataLoaded = ref(false); // 数据加载状态标识

// 微信二维码组件
const wxQrCodeRef = ref(null);
const unbindWxLoading = ref(false);
// 微信回调处理状态
const isWxCallbackProcessed = ref(false);

// 解绑微信对话框显示状态
const showUnbindDialog = ref(false);

// 处理微信回调
const handleWxCallback = async (code, state) => {
  // 检查是否已经处理过此次绑定请求
  const processedKey = `wx_bind_processed_${code}_${state}`;
  if (sessionStorage.getItem(processedKey)) {
    console.log("已处理过此绑定请求，不再重复处理");
    isWxCallbackProcessed.value = true;
    return;
  }

  console.log(code, "code");
  console.log(state, "state");
  try {
    const storedState = sessionStorage.getItem("wx_login_state");

    console.log("判断state状态");
    // 状态码 是否一致
    if (state !== storedState) {
      ElMessage.error("微信状态不一致，请重新扫码");
      isWxCallbackProcessed.value = true; // 标记为已处理
      return;
    }

    // 确保数据已经加载
    if (!dataLoaded.value || !form.value || form.value.name === undefined) {
      console.log("数据还未加载完成，等待数据加载...");
      await getacctable();
    }

    console.log(form.value?.isBindWx, "是否绑定");

    // 状态码一致，处理微信绑定
    if (form.value?.isBindWx) {
      // 如果当前已绑定微信，这里可以处理解绑逻辑（如果需要的话）
      ElMessage.info("当前账号已绑定微信");
      isWxCallbackProcessed.value = true; // 标记为已处理
    } else {
      console.log("去绑定");
      // 如果当前未绑定微信，处理绑定逻辑
      await bindCode(code, processedKey);
    }
  } catch (error) {
    console.error("微信回调处理失败:", error);
    ElMessage.error("微信绑定处理失败，请重试");
    isWxCallbackProcessed.value = true; // 标记为已处理
  }
};

// 解绑微信
const handleChangeWx = debounce(
  async () => {
    if (unbindWxLoading.value) return;
    unbindWxLoading.value = true;
    try {
      const params = {
        userId: route.query.id,
        userType: "ORGANIZATION_ADMIN"
      };

      const res = await unbindWxCode(params, {
        operateLogType: "ACCOUNT_MANAGEMENT",
        operateType: `对账号“${form.value.account}@${organ.value}”完成微信解绑操作`
      });

      if (res.code === 200) {
        ElMessage.success("微信解绑成功");

        // 关闭对话框
        showUnbindDialog.value = false;

        await getacctable();
      }
    } catch (error) {
      ElMessage.error("微信解绑失败，请重试");
    } finally {
      unbindWxLoading.value = false;
    }
  },
  1000,
  { immediate: true }
);

// 绑定账号
const bindCode = async (code, processedKey) => {
  console.log(code, "code");
  const params = {
    code: code,
    userId: route.query.id,
    userType: "ORGANIZATION_ADMIN"
  };

  try {
    const res = await getBindCode(params, {
      operateLogType: "ACCOUNT_MANAGEMENT",
      operateType: `对账号“${form.value.account}@${organ.value}”完成微信绑定操作`
    });
    if (res.code === 200) {
      ElMessage.success("绑定成功");

      // 记录已处理状态到会话存储
      sessionStorage.setItem(processedKey, "true");

      // 重新获取用户信息
      await getacctable();

      // 延迟重置二维码组件状态
      setTimeout(() => {
        if (wxQrCodeRef.value) {
          wxQrCodeRef.value.resetInit();
        }
      }, 100);

      // 标记为已处理
      isWxCallbackProcessed.value = true;
    } else {
      ElMessage.warning(res.msg);
      isWxCallbackProcessed.value = true; // 标记为已处理
    }
  } catch (err) {
    isWxCallbackProcessed.value = true; // 标记为已处理
  }
};

// 清理路由参数
const resetToInitialState = () => {
  // 清理路由查询参数，保持当前路径
  // router.replace({
  //   path: route.path,
  //   query: {
  //     // 保留必要的查询参数
  //     id: route.query.id,
  //     title: route.query.title
  //     // 移除微信登录相关的参数
  //     // code, state, path 将被清除
  //   }
  // });
};

// 监听路由参数变化
watch(
  () => route.query,
  async newQuery => {
    const { code, state } = newQuery;
    console.log(code, "code");
    console.log(state, "state");

    if (code && state && !isWxCallbackProcessed.value) {
      // 等待数据加载完成后再处理微信回调
      if (!dataLoaded.value) {
        console.log("等待数据加载完成...");
        // 等待数据加载完成
        const checkDataLoaded = () => {
          return new Promise(resolve => {
            const timer = setInterval(() => {
              if (dataLoaded.value) {
                clearInterval(timer);
                resolve(true);
              }
            }, 100);
            // 最多等待5秒
            setTimeout(() => {
              clearInterval(timer);
              resolve(false);
            }, 3000);
          });
        };
        await checkDataLoaded();
      }
      await handleWxCallback(code, state);
    }
  },
  { immediate: true }
);

onMounted(async () => {
  try {
    getTableList(); //获取机构别名
    await getacctable(); // 确保用户信息加载完成
    getDifferentFile();
    qrcode.toDataURL(url.value, (err, url) => {
      if (err) {
        console.error(err);
      } else {
        qrCodeData.value = url;
      }
    });
  } catch (error) {
    console.error("初始化失败:", error);
  }
});
// 表头
const tableHeader = ref([
  { label: "机构", value: "", key: "organizationName" },
  { label: "", value: "", width: "107px", key: "id" },
  { label: "创建时间", value: "", width: "107px", key: "createdAt" }
]);
watchEffect(() => {
  const title = route.query.title;
  // console.log("🍭-----title-----", title);
  tableHeader.value[1].label = title === "ldMang" ? "领队ID" : "讲师ID";
});
// 表单
const form = ref({
  files: [],
  name: "", //姓名
  account: "", //账号
  phone: "", //手机号码
  email: "", //邮箱
  idNumber: "", //身份号
  // upload: [], //资质文件
  isBindWx: false
});
const formFile = ref({
  institutionLicense: [],
  qualificationDocuments: []
});

// 根据不同身份获取上传文件
const getDifferentFile = async () => {
  const params = {
    organizationAdminId: route.query?.id
  };
  let requestApi =
    route.query.title === "ldMang"
      ? fileLeaderList(params)
      : fileLeatureList(params);
  const { code, data, msg } = await requestApi;
  if (code == 200) {
    console.log("🐠-----data---/////////////--", data);
    formFile.value.institutionLicense = [];
    formFile.value.qualificationDocuments = [];
    if (data?.fileDTOS) {
      data.fileDTOS.forEach(item => {
        if (item.fileType == "BUSINESS_LICENSE") {
          formFile.value.institutionLicense.push(item.uploadFile);
        } else if (item.fileType == "QUALIFICATION_DOCUMENT") {
          formFile.value.qualificationDocuments.push(item.uploadFile);
        }
      });
    }
  }
};

// 提交
const formRef = ref(null);
const formData = ref([
  {
    label: "姓名",
    type: "input",
    prop: "name",
    check: true,
    placeholder: "请输入姓名",
    width: "400px",
    maxLength: 10
  },
  {
    label: "账号",
    type: "input",
    prop: "account",
    check: true,
    placeholder: "请输入账号",
    width: "400px",
    maxLength: 20
  },
  {
    label: "手机号",
    type: "input",
    prop: "phone",
    check: true,
    isView: true,
    placeholder: "请输入手机号",
    width: "400px",
    maxLength: 11
  },
  {
    label: "邮箱",
    type: "input",
    prop: "email",
    placeholder: "请输入邮箱",
    width: "400px",
    maxLength: 30
  },
  {
    label: "身份证号",
    type: "input",
    prop: "idNumber",
    isView: true,
    placeholder: "请输入身份证号",
    width: "400px",
    maxLength: 18
  },
  {
    label: "微信绑定",
    type: "img",
    prop: "isBindWx",
    url: "",
    width: "400px",
    height: "120px"
  },
  {
    label: "资质文件",
    type: "upload",
    prop: "qualificationDocuments",
    width: "1200px",
    text: "支持上传图片及pdf、doc、docx、ppt、pptx、xls、xlsx文件，图片最佳尺寸：750*1334px，单张图片大小不超过10MB，文件单个大小不超过30MB "
  }
]);
//文件处理
const fileData = () => {
  //机构营业执照
  let institutionLicense = formFile.value.institutionLicense;
  //资质文件
  let qualificationDocuments = formFile.value.qualificationDocuments;
  if (institutionLicense.length > 0) {
    setFilesFn(institutionLicense, "BUSINESS_LICENSE");
  }
  if (qualificationDocuments.length > 0) {
    setFilesFn(qualificationDocuments, "QUALIFICATION_DOCUMENT");
  }
};
const setFilesFn = (val, type) => {
  for (let index = 0; index < val.length; index++) {
    const element = val[index].fileIdentifier;
    console.log("🌵-----element-----", element);
    form.value.files.push({
      fileType: type,
      fileIdentifier: element,
      sortOrder: index + 1
    });
  }
};
// 自定义电话号码校验方法
const validatePhoneNumber = async (rule, value, callback) => {
  if (value == oldData.value.phone || value == decrypt(oldData.value.phoneCt)) {
    if (formData.value[3].label === "验证码") {
      formData.value.splice(3, 1);
    }
    callback();
    return;
  }
  const phoneRegex = /^1[3-9]\d{9}$/; // 匹配中国大陆手机号码
  if (!value) {
    callback(new Error("手机号不能为空"));
  } else if (!phoneRegex.test(value)) {
    callback(new Error("请输入有效的手机号码"));
  } else {
    if (formData.value[3].label !== "验证码") {
      // 往数组指定位置添加验证码字段
      formData.value.splice(3, 0, {
        label: "验证码",
        type: "input",
        prop: "code",
        span: 1,
        placeholder: "请输入验证码",
        width: "400px",
        check: true
      });

      // 如果form中没有code字段，初始化它
      if (!form.value.code) {
        form.value.code = "";
      }
    }

    if (value == decrypt(oldData.value.phoneCt)) {
      callback();
    } else {
      const params = {
        phone: encryption(value),
        organizationId: getiphorgId.value
      };
      try {
        const response = await verifyPhone(params);
        if (response.code === 70008) {
          callback(new Error("手机号已存在"));
        } else {
          callback();
        }
      } catch (error) {
        console.log("🌈-----error-----", error);
      }
    }
  }
};
// 自定义账号校验方法
const validateAccount = async (rule, value, callback) => {
  if (value === oldData.value.account) {
    callback();
    return;
  }
  if (!value) {
    callback(new Error("账号不能为空"));
  } else {
    try {
      const response = await verifyUsername({ username: value });
      console.log("🌈-----response-----", response);
      if (response.code === 10016) {
        callback(new Error("账号已存在"));
      } else {
        callback();
      }
    } catch (error) {
      console.log("🌈-----error-----", error);
    }
    // callback();
  }
};
//身份证输入筛选
const filterChineseInput = (value, prop) => {
  const filteredValue = value.replace(/[^\dX]/gi, "");
  form.value[prop] = filteredValue.toUpperCase();
};
//身份证号校验
const validateIdNumber = (rule, value, callback) => {
  if (!value) {
    callback();
    return;
  }
  if (value === oldData.value.idNumber) {
    callback();
  } else if (value.length !== 18) {
    callback(new Error("请输入有效的身份证号"));
  } else {
    callback();
  }
};
// 校验规则
const rules = ref({
  name: [
    { required: true, message: "姓名不能为空", trigger: "blur" },
    { min: 2, max: 10, message: "姓名长度应在2-10个字符之间", trigger: "blur" }
  ],
  // account: [{ required: true, message: "账号不能为空", trigger: "blur" }],
  account: [{ required: true, validator: validateAccount, trigger: "blur" }],
  phone: [{ required: true, validator: validatePhoneNumber, trigger: "blur" }],
  idNumber: [{ required: false, validator: validateIdNumber, trigger: "blur" }],
  email: [
    {
      validator: (rule, value, callBack) => {
        if (!value) {
          callBack();
          return;
        }
        // 邮箱正则表达式
        const pattern = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/i;

        // 测试邮箱是否匹配模式
        if (!pattern.test(value)) {
          callBack("邮箱格式错误");
          return;
        }
        callBack();
      },
      trigger: "blur"
    }
  ]
  // codenumb: [{ required: true, message: "验证码不能为空", trigger: "blur" }]
});
// 提交表单/编辑
const oldData = ref({});
const submitForm = debounce(
  () => {
    formRef.value.validate(async (s, b) => {
      if (s) {
        form.value.files = []; // 清空文件集合
        fileData(); //文件集合处理
        // 新增
        const addParam = {
          id: form.value?.id,
          name: form.value?.name,
          account: `${form.value?.account}@${organ.value}`,
          phone: encryption(form.value?.phone || ""),
          files: form.value.files,
          code: form.value?.code,
          leaderLecturerType:
            route.query?.title === "jsMang" ? "LECTURER" : "LEADER"
        };

        // 特殊处理邮箱和身份证号
        // 如果邮箱有值，则必须提交
        if (form.value.email && form.value.email.trim() !== "") {
          addParam.email = form.value.email;
        }

        // 处理身份证号 - 根据当前状态判断如何处理
        if (form.value.idNumber && form.value.idNumber.trim() !== "") {
          // 判断身份证号是否已经是掩码状态
          const idNumberIndex = formData.value.findIndex(
            item => item.prop === "idNumber"
          );

          if (idNumberIndex !== -1 && formData.value[idNumberIndex].isView) {
            // 如果是掩码状态，使用原始的加密数据
            if (oldData.value.idNumberCt) {
              addParam.idNumber = oldData.value.idNumberCt;
            } else {
              addParam.idNumber = encryption(form.value.idNumber);
            }
          } else if (form.value.idNumber === oldData.value.idNumberDecrypted) {
            // 如果显示的是解密形式且值未变，使用原始的加密数据
            if (oldData.value.idNumberCt) {
              addParam.idNumber = oldData.value.idNumberCt;
            }
          } else {
            // 如果是新输入的数据，进行加密
            addParam.idNumber = encryption(form.value.idNumber);
          }
        }

        let paramsData = {};
        for (const paramsDataKey in form.value) {
          let isArray = Array.isArray(addParam[paramsDataKey]);
          if (isArray) {
            if (addParam[paramsDataKey].length > 0) {
              paramsData[paramsDataKey] = addParam[paramsDataKey];
            }
          } else {
            if (addParam[paramsDataKey]) {
              paramsData[paramsDataKey] = addParam[paramsDataKey];
            }
          }
        }

        let params = compareObjects(copyData.value, paramsData);
        // 判断手机号是否修改;
        // 没修改
        if (
          form.value.phone === oldData.value.phone ||
          form.value.phone === decrypt(oldData.value.phoneCt)
        ) {
          delete params.phone;
        }
        // 已修改
        if (paramsData.phone) {
          paramsData.phone = encryption(paramsData.phone);
        }

        // 特殊处理邮箱和身份证号
        // 如果邮箱有值，则必须提交
        if (form.value.email && form.value.email.trim() !== "") {
          params.email = form.value.email;
        }

        // 处理身份证号 - 使用addParam中处理好的值
        if (addParam.idNumber) {
          params.idNumber = addParam.idNumber;
        }

        params.id = form.value?.id;
        params.leaderLecturerType =
          route.query?.title === "jsMang" ? "LECTURER" : "LEADER";

        const operateLog = {
          operateLogType:
            route.query?.title === "jsMang"
              ? "LECTURER_MANAGEMENT"
              : "LEADER_MANAGEMENT",
          operateType: `编辑了${form.value?.name || "用户"}的账号`
        };
        const { code, data, msg } = await editteaInfo(params, operateLog);
        if (code === 200) {
          ElMessage({
            message: "编辑成功",
            type: "success"
          });
          const title = route.query.title === "jsMang" ? "jsMang" : "ldMang";
          router.push({
            path:
              title === "ldMang"
                ? "/account/teacherDetails"
                : "/account/lectureDetails",
            query: { id: route.query?.id, title }
          });
        } else {
          ElMessage({
            message: msg,
            type: "error"
          });
        }
      } else {
        console.log("表单校验失败");
      }
    });
  },
  1000,
  { immediate: true }
);
const copyData = ref({});
const organ = ref("");
// 账号详情回显
const getacctable = async () => {
  const [err, result] = await requestTo(ledeterList({ id: route.query?.id }));
  console.log("🎉-----result-----", result);
  if (result) {
    organ.value = result?.organizationAlias;
    tableHeader.value = tableHeader.value.map(item => ({
      ...item,
      value:
        item.key === "createdAt"
          ? formatTime(result[item.key], "YYYY-MM-DD HH:mm")
          : (result[item.key] ?? "--")
    }));
    form.value = {
      id: result?.id,
      name: result?.name,
      account: result?.account.split("@")[0],
      phone: result?.phone,
      email: result?.email,
      idNumber: result?.idNumber,
      upload: result?.upload ?? form.value.upload,
      isBindWx: result?.isBindWx
    };

    richFlag.value = true;
    dataLoaded.value = true; // 标记数据已加载
    oldData.value = {
      isAdmin: false,
      name: result?.name,
      account: result?.account,
      email: result?.email,
      phone: result?.phone,
      phoneCt: result?.phoneCt,
      idNumber: result?.idNumber,
      idNumberCt: result?.idNumberCt,
      idNumberDecrypted: result?.idNumberCt ? decrypt(result?.idNumberCt) : "",
      upload: result?.upload ?? form.value.upload
    };

    copyData.value = {
      name: result?.name,
      account: result?.account,
      phone: encryption(result?.phone || ""),
      email: result?.email,
      idNumber: result?.idNumberCt || encryption(result?.idNumber || ""),
      leaderLecturerType:
        route.query?.title === "jsMang" ? "LECTURER" : "LEADER"
    };
  }
};
// 获取验证码
const getCaptcha = async phoneCode => {
  const phoneRegex = /^1[3-9]\d{9}$/; // 匹配中国大陆手机号码
  if (!phoneCode) {
    ElMessage({ message: "手机号不能为空" });
    return;
  }
  if (!phoneRegex.test(phoneCode)) {
    ElMessage({ message: "电话号码格式不正确", type: "warning" });
    return;
  }
  const params = {
    phone: encryption(phoneCode),
    codeType: "VERIFICATION_CODE"
  };
  const { code, msg } = await getPhonecode(params);
  if (code == 200) {
    ElMessage({
      message: "验证码已发送",
      type: "success"
    });
  }
};
// 取消
const cancelForm = () => {
  const title = route.query.title === "jsMang" ? "jsMang" : "ldMang";
  router.push({
    path:
      title === "ldMang"
        ? "/account/teacherDetails"
        : "/account/lectureDetails",
    query: { id: route.query?.id, title }
  });
};
// 文件上传之前
const fileList = ref(null);
const beforeUpload = async (file, item) => {
  console.log("🦄-----item-----", item);
  // console.log("💗beforeUpload---------->", file);
  let fileType = ["image", "pdf", "word", "excel", "text", "ppt"];
  let isType = validateFileType(file, ["image"]);
  let isSizeNum = isType.valid;
  let isSize = isOverSizeLimit(file, isSizeNum ? 10 : 20);

  if (isSize.valid) {
    try {
      let { code, data } = await uploadFile(file, () => {}, fileType);
      if (code === 200) {
        // console.log("🌳-----data--///---", data);
        // form.value[item].push(data);
        formFile.value[item].push(data);
        // console.log("🎁----- form.fileData-----", form.value[item]);
      }
    } catch (error) {
      console.log("🌈-----error-----", error);
      ElMessage({
        message: error.message,
        type: "error"
      });
    }
  } else {
    ElMessage.error(isSize.message);
  }
};
//删除文件
// const handleClickDetele = (item, index) => {
//   console.log("🌈-----item, index-----", item, index);
//   // return
//   form.value[item].splice(index, 1);
//   // maxUpload.value.uploadFile = 0;
// };
//删除文件
const getDeleted = (item, index) => {
  // form.value[item].splice(index, 1);
  formFile.value[item].splice(index, 1);
};
// const isView = ref(true);
const isViewFn = (val, index) => {
  formData.value[index].isView = !formData.value[index].isView;
  if (val === "idNumber") {
    if (formData.value[index].isView) {
      // 切换为显示掩码数据
      form.value[val] = oldData.value[val];
    } else {
      // 切换为显示解密数据
      form.value[val] =
        oldData.value.idNumberDecrypted || decrypt(oldData.value.idNumberCt);
    }
  } else if (val === "phone") {
    if (formData.value[index].isView) {
      // 切换为显示掩码数据
      form.value[val] = oldData.value[val];
    } else {
      // 切换为显示解密数据
      form.value[val] = decrypt(oldData.value.phoneCt);
    }
  }
};
const getiphorgId = ref();
const getTableList = async () => {
  const { code, data, msg } = await organizationFindById();
  getiphorgId.value = data?.id;
};

// 检测是否有微信回调参数且未处理
const hasWxCallbackParams = computed(() => {
  return !!(
    route.query.code &&
    route.query.state &&
    !isWxCallbackProcessed.value
  );
});

// 构建包含当前 query 参数的重定向路径
const redirectPathWithQuery = computed(() => {
  const currentPath = route.path;
  const queryParams = new URLSearchParams();

  // 将当前的 query 参数添加到 URLSearchParams 中，排除微信回调参数
  Object.keys(route.query).forEach(key => {
    // 排除微信回调相关的参数
    if (key !== "code" && key !== "state") {
      if (route.query[key] !== null && route.query[key] !== undefined) {
        queryParams.append(key, route.query[key]);
      }
    }
  });

  // 如果有 query 参数，则构建完整路径
  const queryString = queryParams.toString();
  return queryString ? `${currentPath}?${queryString}` : currentPath;
});
</script>

<template>
  <div class="containers">
    <div class="table_top">
      <el-descriptions
        class="margin-top"
        title=""
        :column="2"
        border
        style="width: 100%"
        :label-width="'200px'"
      >
        <template v-for="(item, index) in tableHeader" :key="index">
          <el-descriptions-item :label="item.label" label-align="center">
            {{ item.value }}
          </el-descriptions-item>
        </template>
      </el-descriptions>
    </div>
    <div class="table_content">
      <el-form ref="formRef" :model="form" :rules="rules" style="flex-grow: 1">
        <el-descriptions v-if="richFlag" title="" :column="2" border>
          <el-descriptions-item
            v-for="(item, index) in formData"
            :key="index"
            label-width="180"
            label-align="center"
            label-class-name="my-label"
            :span="
              item.prop === 'phone'
                ? formData.some(i => i.prop === 'code')
                  ? 1
                  : 2
                : item.prop === 'code'
                  ? 1
                  : 2
            "
          >
            <template #label>
              <span v-if="item.check" class="star">*</span>{{ item.label }}
            </template>
            <el-form-item
              :prop="item.prop"
              :inline-message="true"
              style="margin-bottom: 0"
              :show-message="true"
              error-placement="right"
            >
              <!-- input输入 -->
              <template v-if="item.type === 'input'">
                <el-input
                  v-model="form[item.prop]"
                  :placeholder="item.placeholder"
                  :style="{ width: item.width }"
                  :disabled="
                    (item.prop === 'phone' || item.prop === 'idNumber') &&
                    item.isView &&
                    oldData[item.prop]?.length > 0
                  "
                  :maxlength="item.maxLength"
                  :show-word-limit="item.maxLength"
                  @input="
                    item.prop === 'idNumber'
                      ? filterChineseInput($event, item.prop)
                      : ''
                  "
                >
                  <template
                    v-if="
                      (item.prop === 'phone' || item.prop === 'idNumber') &&
                      form[item.prop]?.length > 0
                    "
                    #suffix
                  >
                    <el-icon
                      v-if="item.isView"
                      style="cursor: pointer"
                      @click="isViewFn(item.prop, index)"
                    >
                      <Hide />
                    </el-icon>
                    <el-icon
                      v-else
                      style="cursor: pointer"
                      @click="isViewFn(item.prop, index)"
                    >
                      <View />
                    </el-icon>
                  </template>
                </el-input>
                <div v-if="item.prop === 'account'" class="organ">
                  @{{ organ }}
                </div>
                <!-- 获取验证码 -->
                <div v-if="item.prop === 'code'" class="Vacode">
                  <el-button
                    v-countdown="{
                      value: 60,
                      callback: () => getCaptcha(form.phone),
                      countdownText: 's后重新获取',
                      loadingText: '发送中...'
                    }"
                  >
                    获取验证码
                  </el-button>
                </div>
              </template>

              <!-- 二维码展示 -->
              <template v-else-if="item.type === 'img'">
                <span v-if="!form.isBindWx" class="isQR">
                  <!-- 当前{{ form.isBindWx ? "已绑定" : "未绑定" }} -->
                  当前未绑定
                </span>
                <div v-else>
                  <span>已绑定,
                    <el-link
                      type="primary"
                      underline="hover"
                      @click="showUnbindDialog = true"
                    >
                      解绑
                    </el-link>
                  </span>
                </div>
                <div v-if="!form?.isBindWx" class="codeQR">
                  <!-- 只有在没有微信回调参数时才显示二维码 -->
                  <WxQrCode
                    v-if="!hasWxCallbackParams"
                    ref="wxQrCodeRef"
                    :redirectPath="redirectPathWithQuery"
                  />
                  <!-- 有微信回调参数时显示处理中状态 -->
                  <div v-else class="wx-processing">
                    <el-icon class="is-loading" size="30">
                      <Loading />
                    </el-icon>
                    <p>正在处理微信绑定...</p>
                  </div>
                </div>
              </template>

              <!-- 示例：上传组件 -->
              <template v-else-if="item.type === 'upload'">
                <el-upload
                  action="#"
                  :show-file-list="false"
                  class="upload-demo"
                  :http-request="() => {}"
                  accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,"
                  :before-upload="file => beforeUpload(file, item.prop)"
                >
                  <img :src="uploadImg" alt="">
                </el-upload>
                <template
                  v-for="(item2, index2) in formFile[item.prop]"
                  :key="index2"
                >
                  <FileItem
                    isNeedDelte
                    :data="item2"
                    :index="index2"
                    style="width: 100%; min-width: 130px"
                    @delete="getDeleted(item.prop, index2)"
                  />
                </template>
                <div class="upload_text">{{ item.text }}</div>
              </template>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>
      <div class="table_bottom">
        <el-button type="default" @click="cancelForm"> 取消 </el-button>
        <el-button type="primary" @click="submitForm"> 保存 </el-button>
      </div>
    </div>

    <!-- 解绑微信确认对话框 -->
    <el-dialog
      v-model="showUnbindDialog"
      title="解绑微信"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="dialog-content">
        <span>确定要解绑当前微信账号吗？</span>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showUnbindDialog = false">取消</el-button>
          <el-button
            type="primary"
            :loading="unbindWxLoading"
            @click="handleChangeWx"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-dialog) {
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  margin: 0px !important;
}

.containers {
  display: flex;
  flex-direction: column;
  // box-sizing: border-box;
  width: calc(100% - 48px);
  height: 100%;
  background: #f0f2f5;

  .table_top {
    box-sizing: border-box;
    padding: 20px;
    background-color: #fff;
  }

  .table_content {
    min-height: 73vh;
    display: flex;
    flex-direction: column;
    white-space: nowrap;
    box-sizing: border-box;
    padding: 20px;
    margin-top: 20px;
    background-color: rgb(255 255 255);

    .star {
      margin-right: 3px;
      color: red;
    }
    .organ {
      margin-left: 20px;
    }
    .el-descriptions-item {
      display: flex;
      align-items: center;
    }

    .Vacode {
      margin-left: 20px;
    }

    .isQR {
      margin-right: 240px;
    }

    .codeQR {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px 0;
    }

    .wx-processing {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      color: #606266;

      .is-loading {
        animation: rotating 2s linear infinite;
        margin-bottom: 10px;
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }

    @keyframes rotating {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }
  }

  .table_bottom {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
.upload-demo {
  display: flex;
  align-items: center;
  width: 100%;
}
.fileOther {
  // margin-top: 26px;
  margin-left: 20px;
  display: flex;
  align-items: center;
  line-height: 22px;
  // width: 420px;
  // margin-bottom: 56px;
  .title {
    color: #0e1832;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    white-space: nowrap;
  }
  .link {
    width: 20px;
    height: 20px;
    margin: 0 10px;
    cursor: pointer;
  }
  .fileName {
    color: #3876fd;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    cursor: pointer;
    // width: 350px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .linkDetele {
    width: 20px;
    height: 20px;
    margin-left: 10px;
    cursor: pointer;
  }
}
:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
  //   width: 240px;
}

:deep(.my-label) {
  background: #e1f5ff !important;
}

:deep(.my-content) {
  background: var(--el-color-danger-light-9);
}
// :deep(.el-form-item__content) {
//   flex-direction: column;
//   align-items: normal;
// }
.upload_text {
  font-size: 12px;
  position: relative;
  top: 5px;
  color: #8c939d;
}
.el-link {
  line-height: 1.2;
  margin-bottom: 2px;
}
</style>
