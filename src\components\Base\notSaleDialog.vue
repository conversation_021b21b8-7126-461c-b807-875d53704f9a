<script setup>
import { id } from "element-plus/es/locale/index.mjs";
import { onMounted, ref, defineEmits, computed, nextTick } from "vue";
import { requestTo } from "@/utils/http/tool";
import { pinyin } from "pinyin-pro";
import { decrypt, encryption } from "@/utils/SM4.js";
import { ElMessage } from "element-plus";
import { useUserStoreHook } from "@/store/modules/user";
import { useRouter } from "vue-router";
import baseUrl, { codeInfo } from "@/utils/http/base.js";
import { isEmpty } from "@iceywu/utils";
const props = defineProps({
  title: {
    type: String
  },
  api: {
    type: String,
    default: ""
  },
  id: {
    type: Number
  },
  name: {
    type: String,
    default: ""
  },
  phone: {
    type: String,
    default: ""
  },
  account: {
    type: String,
    default: ""
  },
  dialogFormVisible: {
    type: Boolean
  },
  logOut: {
    type: Boolean,
    default: true
  },
  operateLogType: {
    type: String,
    default: "COMPLEX_MANAGEMENT"
  },
  textLeftBtn: {
    type: String,
    default: "取消"
  },
  textRightBtn: {
    type: String,
    default: "确认"
  },
  operateType: {
    type: String,
    default: "重置了密码"
  },
  showContent: {
    type: String,
    default: "resetPassword"
  },
  marginLeft: {
    type: String,
    default: ""
  }
});
const emit = defineEmits(["reset", "update:dialogFormVisible", "updateData"]);
const router = useRouter();
const reason = ref("");
onMounted(() => {});
// const dialogFormVisible = ref(false);
const newPassword = ref("");
const getListLoading = ref(false);
const btnOKClick = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    id: props.id,
    password: encryption(newPassword.value)
  };

  if (props.showContent === "notSale") {
    if (isEmpty(customReason.value)) {
      ElMessage.error("请填写下架申请理由");
      getListLoading.value = false;
      return;
    }
    paramsData = {
      id: props.id,
      reason: customReason.value
    };
  }
  const operateLog = {
    operateLogType: props.operateLogType,
    operateType: props.operateType || "重置了密码"
  };
  // console.log("🐳paramsData------------------------------>", paramsData);
  // return
  try {
    const { code, data, msg } = await props.api(paramsData, operateLog);
    if (code === 200) {
      if (props.showContent === "notSale") {
        ElMessage({
          message: "课期下架申请提交，请等待平台审核",
          type: "success"
        });
        selectedReasons.value = [];
        customReason.value = "";
        emit("updateData");
      }
    } else {
      if (props.showContent === "notSale") {
        ElMessage.error(`下架申请提交失败，${msg}`);
      }
      getListLoading.value = false;
    }
  } catch (error) {
    if (props.showContent === "notSale") {
      ElMessage.error(`下架申请提交失败，${msg}`);
    }
  }
  getListLoading.value = false;
  emit("reset");
};
const cancel = () => {
  selectedReasons.value = [];
  customReason.value = "";
  emit("reset");
};
// 使用 computed 属性代理 prop
const localVisible = computed({
  get() {
    return props.dialogFormVisible;
  },
  set(value) {
    emit("update:dialogFormVisible", value); // 通知父组件更新
  }
});
const selectedReasons = ref([]); // 用于跟踪已选择的理由
const reasonList = ref([
  "课程内容更新",
  "教师变动",
  "机构运营调整",
  "安全问题",
  "参与人数不足"
]);
const customReason = ref("");
const selectReasonEvt = (item, event) => {
  // 阻止事件冒泡，防止输入框失焦
  event.preventDefault();
  event.stopPropagation();
  const index = selectedReasons.value.indexOf(item);
  if (index > -1) {
    // 取消选择
    selectedReasons.value.splice(index, 1);
    // 从输入框中移除该理由
    const reasonText = item + "，";
    const regex = new RegExp(
      reasonText.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"),
      "g"
    );
    customReason.value = customReason.value.replace(regex, "").trim();
  } else {
    // 选择理由
    selectedReasons.value.push(item);
    // 添加到输入框，后面加上中文逗号
    if (customReason.value) {
      customReason.value += item + "，";
    } else {
      customReason.value = item + "，";
    }
  }

  // 确保输入框保持焦点
  nextTick(() => {
    const textarea = document.querySelector(".custom-reason textarea");
    if (textarea) {
      textarea.focus();
    }
  });
};
const handleReasonInput = value => {
  customReason.value = value;
  // 检查哪些预设理由在输入框中（包含逗号）
  selectedReasons.value = reasonList.value.filter(reason =>
    value.includes(reason + "，")
  );
};
const handleClose = () => {
  selectedReasons.value = [];
  customReason.value = "";
};
</script>

<template>
  <!-- <div class="popup"> -->
  <el-dialog
    v-model="localVisible"
    :title="title || '重置密码确定'"
    width="570"
    @close="handleClose"
  >
    <div class="content">
      <div v-if="showContent === 'notSale'" class="des-examine">
        <div>
          <el-input
            v-model="customReason"
            placeholder="请选择或输入下架申请理由（必填）"
            type="textarea"
            resize="none"
            maxlength="100"
            show-word-limit
            @input="handleReasonInput"
          />
          <div class="select-btn">
            <div
              v-for="item in reasonList"
              :key="item"
              :class="
                selectedReasons.includes(item) ? 'active-item' : 'select-item'
              "
              @click="event => selectReasonEvt(item, event)"
            >
              {{ item }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button :style="{ 'margin-right': marginLeft }" @click="cancel">
          {{ textLeftBtn }}
        </el-button>
        <el-button
          :loading="getListLoading"
          :type="
            textRightBtn === '确认'
              ? 'primary'
              : textRightBtn === '确认开启'
                ? 'primary'
                : 'danger'
          "
          @click="btnOKClick"
        >
          {{ textRightBtn }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.info {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  :nth-child(2) {
    margin: 20px 0 20px 0px;
  }
}
.describe {
  width: 80%;
  height: 130px;
  // border:1px solid red;
  margin: 0 auto;
  font-size: 14px;
}
.des-examine {
  width: 96%;
  height: 200px;
  margin: 0 auto;
  :deep(.el-textarea__inner) {
    height: 150px;
  }
  .select-btn {
    margin-top: 10px;
    display: flex;
    .select-item {
      padding: 5px;
      border: 1px solid #ccc;
      margin-right: 10px;
      border-radius: 5px;
      cursor: pointer;
      &:hover {
        color: #409eff;
        border-color: #dae8f6;
        background-color: #ebf5ff;
      }
    }
    .active-item {
      padding: 5px;
      //   border: 1px solid #409eff;
      background-color: #409eff;
      color: #fff;
      margin-right: 10px;
      border-radius: 5px;
    }
  }
}
</style>
