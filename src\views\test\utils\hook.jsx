import { fileDeleteById, fileFindAll, fileUpdateById } from "@/api/files";
import ImgBlurHash from "@/components/ImgBlurHash";
import PreCover from "@/components/Preview/cover.vue";
import { addDialog, closeAllDialog } from "@/components/ReDialog";
import { getFileIcon } from "@/utils/getFileType";
import { requestTo } from "@/utils/http/tool";

import { message } from "@/utils/message";

import dayjs from "dayjs";
import { h, onMounted, reactive, ref, toRaw } from "vue";
import editForm from "../components/editForm.vue";

export function useRole() {
  const form = reactive({
    name: "",
    fileMd5: "",
    blurhash: "",
    page: 1,
    size: 10,
    sort: "createdAt,desc"
  });
  const formRef = ref();
  const dataList = ref([]);
  const loading = ref(true);
  const pagination = reactive({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });
  const columns = [
    {
      label: "文件编号",
      prop: "id",
      width: 90
    },
    {
      label: "文件名称",
      prop: "name",
      width: 200
    },

    {
      label: "预览",
      prop: "url",
      cellRenderer: ({ row }) => (
        <div class="rounded-md overflow-hidden relative">
          <div
            class="absolute top-1 right-1  text-xl"
            style={{
              backgroundImage: `url(${getFileIcon(row.url)})`,
              backgroundSize: "contain",
              backgroundRepeat: "no-repeat",
              width: "50px",
              height: "40px"
            }}
          >
          </div>
          <PreCover class=" !h-30" data={row} />
        </div>
      ),
      width: 300
    },
    {
      label: "上传人",
      prop: "remark",
      children: [
        {
          label: "用户名称",
          prop: "user",
          cellRenderer: ({ row }) => <el-tag>{row.user.name}</el-tag>
        },
        {
          label: "头像",
          prop: "user",
          cellRenderer: ({ row }) => (
            <ImgBlurHash
              class="rounded-full"
              v-preview={{
                url: row?.user?.avatar,
                type: "image"
              }}
              src={row.user.avatar}
            />
          )
        }
      ]
    },

    {
      label: "创建时间",
      minWidth: 180,
      prop: "createdAt",
      formatter: ({ createdAt }) =>
        dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "操作",
      fixed: "right",
      width: 240,
      slot: "operation"
    }
  ];

  async function handleDelete(row) {
    const params = {
      id: row.id,
      ossDlelete: true
    };
    const [_, data] = await requestTo(fileDeleteById(params));
    if (data) {
      message(`删除成功`, { type: "success" });
      onSearch();
    } else {
      message(`删除失败`, { type: "error" });
    }
  }

  function handleSizeChange(val) {}

  function handleCurrentChange(val) {
    form.page = val;
    onSearch();
  }

  function handleSelectionChange(val) {}

  async function onSearch() {
    loading.value = true;
    const [err, result] = await requestTo(fileFindAll(toRaw(form)));
    const { data = [], meta = {} } = result || {};
    dataList.value = data;
    pagination.total = meta.totalElements;
    pagination.pageSize = form.size;
    pagination.currentPage = form.page;

    setTimeout(() => {
      loading.value = false;
    }, 500);
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  function openDialog(title = "新增", row) {
    addDialog({
      title: `${title}角色`,
      props: {
        formInline: {
          name: row?.name ?? "",
          fileMd5: row?.fileMd5 ?? "",
          blurhash: row?.blurhash ?? ""
        }
      },
      width: "40%",
      draggable: true,
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(editForm, { ref: formRef, formInline: null }),
      beforeSure: (done, { options }) => {
        const FormRef = formRef.value.getRef();
        const curData = options.props.formInline;
        function chores() {
          message(`您${title}了角色名称为${curData.name}的这条数据`, {
            type: "success"
          });
          done(); // 关闭弹框
          onSearch(); // 刷新表格数据
        }
        FormRef.validate(valid => {
          if (valid) {
            // 表单规则校验通过
            if (title === "新增") {
              // 实际开发先调用新增接口，再进行下面操作
              chores();
            } else {
              // 实际开发先调用编辑接口，再进行下面操作
              chores();
            }
          }
        });
      }
    });
  }

  /** 菜单权限 */
  function handleMenu() {
    message("等菜单管理页面开发后完善");
  }

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    openDialog,
    handleMenu,
    handleDelete,
    handleSizeChange,
    handleCurrentChange,
    handleSelectionChange
  };
}
