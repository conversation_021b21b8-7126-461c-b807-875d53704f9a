import { Edit, Hide, View } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import { onMounted, reactive, ref, onActivated, defineEmits } from "vue";
// import { parentIsFreeze, parentFindAll } from "@/api/parentManage.js";
import { courseFindAll } from "@/api/course.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { decrypt, encryption } from "@/utils/SM4.js";

export function useRole() {
  const columns = [
    {
      label: "课程名",
      prop: "name",
      minWidth: 90,
      formatter: row => {
        return row.name || "--";
      }
    },
    {
      label: "课程类型",
      prop: "courseTypeName",
      width: 200,
      //   cellRenderer: ({ row }) => (
      //     <el-text line-clamp="1">{row.name || "--"}</el-text>
      //   )
      formatter: row => {
        return row.courseTypeName || "--";
      }
    },
    {
      label: "创建时间",
      minWidth: 90,
      prop: "createdAt",
      formatter: row => {
        return row.createdAt
          ? dayjs(row.createdAt).format("YYYY-MM-DD HH:mm:ss")
          : "--";
      }
    },
    {
      label: "总期数",
      minWidth: 90,
      prop: "termNumber",
      formatter: row => {
        return row.termNumber || "--";
      }
    },
    {
      label: "上架期数",
      minWidth: 90,
      prop: "onlineTermNumber",
      formatter: row => {
        return row.onlineTermNumber || "--";
      }
    },
    {
      label: "课程状态",
      minWidth: 90,
      prop: "freeze",
      cellRenderer: ({ row }) => (
        <div style={{ color: row.freeze ? "#f56c6c" : "" }}>
          {row.freeze ? "冻结" : "正常"}
        </div>
      )
    },
    {
      label: "操作",
      fixed: "right",
      width: 180,
      slot: "operation"
    }
  ];

  const courseTypeoptions = ref([
    {
      value: "",
      label: "全部"
    },
    {
      value: false,
      label: "正常"
    },
    {
      value: true,
      label: "冻结"
    }
  ]);

  const router = useRouter();
  const dataList = ref([]);
  const loadingTable = ref(false);
  const pagination = {
    total: 0,
    pageSize: 15,
    currentPage: 1,
    background: true,
    pageSizes: [15, 30, 50, 100]
  };
  const params = ref({
    page: 1,
    size: 15,
    sort: "createdAt,desc"
  });
  const startAdd = async () => {
    const paramsArg = {
      page: params.value.page - 1,
      size: params.value.size,
      sort: params.value.sort
    };
    const [err, res] = await requestTo(courseFindAll(paramsArg));
    if (res) {
      dataList.value = res.content;
      pagination.total = res.totalElements;
      // pagination.pageSize = res.size;
      // pagination.currentPage = 1;
    }
    if (err) {
    }
  };
  const removeEmptyValues = obj => {
    return Object.fromEntries(
      Object.entries(obj).filter(
        ([key, value]) =>
          value !== null &&
          value !== "" &&
          !(Array.isArray(value) && value.length === 0)
      )
    );
  };
  const startAdd1 = async val => {
    // console.log("🍧-----val-----", val);
    const paramsArg = {
      name: val.name || "",
      phone: val.phone || "",
      freeze: val.freeze,
      startTime: val.startTime || "",
      endTime: val.endTime || "",
      page: val.page || 0,
      size: val.size || "",
      sort: "createdAt,desc"
    };
    // console.log("🌵-----paramsArg-----", paramsArg);
    let aee = removeEmptyValues(paramsArg);
    const [err, res] = await requestTo(parentFindAll(aee));
    if (res) {
      dataList.value = res.content;
      pagination.total = res.totalElements;
    }
    if (err) {
    }
  };

  // 每页多少条
  const handleSizeChange = val => {
    pagination.pageSize = val;
    params.value.size = val;
    startAdd();
  };
  // 前往页数
  const handleCurrentChange = val => {
    pagination.currentPage = val;
    params.value.page = val;
    startAdd();
  };

  const handleInput = value => {
    const regex = /^[0-9+\-*/]*$/;
    let regExp = /^\d+$/;
    const phoneRegex = /^1[3-9]\d{9}$/; // 匹配中国大陆手机号码
    if (!regex.test(value)) {
      from.phone = "";
      ElMessage({
        type: "error",
        message: "请输入正确的手机号"
      });
    }
  };
  //   const getInfoid = item => {
  //     console.log('🌵item------------------------------>',item);
  //     emites("selectCourse", item);
  //   };

  onActivated(() => {
    startAdd();
  });

  onMounted(() => {
    startAdd();
  });

  return {
    pagination,
    dataList,
    columns,
    handleCurrentChange,
    handleSizeChange,
    loadingTable,
    courseTypeoptions
    // getInfoid
  };
}
