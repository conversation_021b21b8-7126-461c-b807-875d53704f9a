// directives/countdown.js
export const countdown = {
  mounted(el, binding) {
    const {
      value = 60,
      callback,
      validate,
      countdownText = "s后重新获取",
      loadingText = "发送中...",
      disabledClass = "is-disabled" // 禁用时添加的CSS类
    } = binding.value || {};

    el._countdown = {
      timer: null,
      originalText: el.textContent,
      count: 0,
      duration: value,
      isRunning: false,
      externalDisabled: el.disabled || false, // 记录外部disabled状态
      countdownText,
      loadingText,
      disabledClass
    };

    const startCountdown = () => {
      if (el._countdown.isRunning) return;

      el._countdown.count = el._countdown.duration;
      el._countdown.isRunning = true;

      // 设置禁用状态和样式
      el.disabled = true;
      el.classList.add(el._countdown.disabledClass);

      el._countdown.timer = setInterval(() => {
        el.textContent = `${el._countdown.count}${el._countdown.countdownText}`;
        el._countdown.count--;

        if (el._countdown.count < 0) {
          clearInterval(el._countdown.timer);
          el._countdown.timer = null;
          el._countdown.isRunning = false;

          // 恢复时考虑外部disabled状态
          el.disabled = el._countdown.externalDisabled;
          if (!el._countdown.externalDisabled) {
            el.classList.remove(el._countdown.disabledClass);
          }
          el.textContent = el._countdown.originalText;
        }
      }, 1000);
    };

    const handleClick = async e => {
      // 如果已经在倒计时中或外部禁用，阻止点击
      if (el._countdown.isRunning || el._countdown.externalDisabled) {
        e.preventDefault();
        e.stopPropagation();
        return false;
      }

      // 执行验证函数（如果提供）
      if (validate && typeof validate === "function") {
        try {
          const isValid = await validate();
          if (!isValid) {
            e.preventDefault();
            e.stopPropagation();
            return false;
          }
        } catch (error) {
          console.error("验证失败:", error);
          e.preventDefault();
          e.stopPropagation();
          return false;
        }
      }

      // 如果有回调函数，执行回调
      if (callback && typeof callback === "function") {
        try {
          // 临时禁用按钮，显示加载状态
          el.disabled = true;
          el.classList.add(el._countdown.disabledClass);
          el.textContent = el._countdown.loadingText;

          const result = await callback();

          // 成功后开始倒计时
          startCountdown();
        } catch (error) {
          // 失败时恢复按钮状态
          el.disabled = el._countdown.externalDisabled;
          if (!el._countdown.externalDisabled) {
            el.classList.remove(el._countdown.disabledClass);
          }
          el.textContent = el._countdown.originalText;
          console.error("获取验证码失败:", error);

          // 可以在这里添加错误提示
          if (window.ElMessage) {
            window.ElMessage.error("获取验证码失败，请重试");
          }
        }
      } else {
        // 没有回调函数时，直接开始倒计时
        startCountdown();
      }
    };

    // 使用 capture 模式确保我们的处理器先执行
    el.addEventListener("click", handleClick, true);
  },

  updated(el, binding) {
    // 更新配置
    if (binding.value !== binding.oldValue) {
      const {
        value = 60,
        countdownText = "s后重新获取",
        loadingText = "发送中...",
        disabledClass = "is-disabled"
      } = binding.value || {};
      if (el._countdown) {
        el._countdown.duration = value;
        el._countdown.countdownText = countdownText;
        el._countdown.loadingText = loadingText;
        el._countdown.disabledClass = disabledClass;
      }
    }

    // 更新外部disabled状态
    if (el._countdown && !el._countdown.isRunning) {
      const wasExternalDisabled = el._countdown.externalDisabled;
      el._countdown.externalDisabled = el.disabled;

      // 如果外部disabled状态发生变化，更新样式
      if (wasExternalDisabled !== el.disabled) {
        if (el.disabled) {
          el.classList.add(el._countdown.disabledClass);
        } else {
          el.classList.remove(el._countdown.disabledClass);
        }
      }
    }
  },

  unmounted(el) {
    if (el._countdown?.timer) {
      clearInterval(el._countdown.timer);
    }
    if (el._countdown) {
      // 清理样式类
      el.classList.remove(el._countdown.disabledClass);
      delete el._countdown;
    }
  }
};
