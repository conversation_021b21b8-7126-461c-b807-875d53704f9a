<script setup>
import FileItem from "@/components/PreviewV2/FileItem.vue";
import { computed, onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";

import {
  confirmRefund,
  confirmRefundSub,
  findByOrdersId,
  getOrderDetails,
  refundRejected,
  refundRejectedSub,
  ordersUploadFile,
  refundFindByOrdersId,
  refundSub,
  refund
} from "@/api/orderManagement";
import dayjs from "dayjs";
import { requestTo } from "@/utils/http/tool";
import { uploadFile } from "@/utils/upload/upload";
import uploadImg from "@/assets/login/upload1.png";
import { formatTime } from "@/utils/index";
import { decrypt } from "@/utils/SM4.js";
import { Hide, View } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { sortOrders } from "element-plus/es/components/table-v2/src/constants.mjs";
import PeriodInfo from "@/views/course/components/descriptionList.vue";
import { debounce } from "@iceywu/utils";
import { COURSE_PERIOD_ENUM, AUDIT_ENUM } from "@/utils/enum.js";
import { courseStore } from "@/store/modules/course.js";
import RefundRejectDialog from "@/views/institution/components/refundRejectDialog.vue";
const props = defineProps({
  adminId: {
    default: ""
  },
  styleTyle: {
    type: Boolean,
    default: false
  },
  ordersDataJson: {
    type: String,
    default: ""
  }
});
const useCourseStore = courseStore();
const router = useRouter();
const route = useRoute();
const form = ref({});

const formRef = ref(null);
const richFlag = ref(false);
const adminId = ref(null); //订单id
const styleTyle = ref(false);
const ordersDataJson = ref(""); //财务数据
const courseID = ref(""); //课期id
const isRefundButtonDisabled = ref(false); // 控制全部退单按钮的禁用状态
const disabledButtonIds = ref(new Set()); // 存储已禁用按钮的子订单ID
onMounted(async () => {
  adminId.value = props?.adminId;
  // console.log("🍪-----props.adminId-----", props.adminId);
  if (props?.styleTyle === true) {
    styleTyle.value = props?.styleTyle;
  }
  if (route.query.type === "course") {
    styleTyle.value = true;
  }
  // if (route.query.data) {
  //   // console.log("🐳-----route.query.data-----", JSON.parse(route.query.data));
  //   courseID.value = JSON.parse(route.query.data).ordersId;
  // }
  ordersDataJson.value = props?.ordersDataJson;
  courseID.value = ordersDataJson.value?.id;
  // ? JSON.parse(props?.ordersDataJson)
  // : "";
  await getData();
  // await getByOrders();
  richFlag.value = true;
});
const formFile = ref([]);
const judgeTime = () => {
  switch (form.value.ordersStatus) {
    case "PAY":
      return "";
      break;
    case "REFUND":
      return "退单时间";
      break;
    case "CANCELLED":
      return "取消时间";
      break;
    default:
      return "完成时间";
      break;
  }
};
const judgestead = val => {
  // console.log("🍧-----val-----", val);
  switch (val) {
    case "UNPAID":
      return "未支付";
      break;
    case "PAY":
      return "支付中";
      break;
    case "PAID":
      return "已支付";
      break;
    case "PAY_FAIL":
      return "支付失败";
      break;
    case "REFUNDING":
      return "退款中"; //1
      break;
    case "REFUND":
      return "已退款"; //1
      break;
    case "PARTIALLY_REFUNDED":
      return "部分退款";
      break;
    case "CANCELLED":
      return "已取消"; //1
      break;
    case "COMPLETED":
      return "已完成";
      break;
  }
};
const refundType = val => {
  // console.log("🍧-----val-----", val);
  switch (val) {
    case "APPLICATION":
      return "申请退款";
      break;
    case "APPROVED":
      return "已批准";
      break;
    case "REFUNDING":
      return "退款中"; //1
      break;
    case "REFUNDED":
      return "已退款"; //1
      break;
    case "COMPLETED":
      return "已完成"; //1
      break;
    case "REJECTED":
      return "退款驳回";
      break;
    case "REFUND_REJECT":
      return "退款驳回";
      break;
    case "REFUND_FAILED":
      return "退款失败"; //1
      break;
    case "PARTIAL_REFUND":
      return "部分退款";
      break;
    case "PARTIAL_CANCEL":
      return "部分取消";
      break;
    case "CANCEL":
      return "取消";
      break;
  }
};
const steadArr = ["已支付", "部分退款", "已完成"];
// const steadArrv2 = ["已支付", "已完成"];
const steadArrv2 = ["REFUNDING"];
// 退单功能相关变量（参考平台端）
const steadArrForRefund = ["已支付", "已完成"];
const refunArr = ["REFUND_REJECT", "REFUND_FAILED", "PARTIAL_CANCEL", "CANCEL"];
const refunArrV2 = [
  "REFUND_REJECT",
  "REFUND_FAILED",
  "PARTIAL_CANCEL",
  "PARTIAL_REFUND",
  "CANCEL"
];
const orderType = row => {
  let type = {
    WECHAT: "微信支付",
    ALPAY: "支付宝支付",
    PLATFORM: "平台",
    PUBLIC_ACCOUNT: "公账"
  };
  return type[row];
};
const displayValue = computed(() => {
  return (item, data) => {
    if (item.prop === "createdAt") {
      return formatTime(data[item.prop], "YYYY-MM-DD HH:mm:ss");
    } else if (item.prop === "channelType") {
      return orderType(data[item.prop]);
    } else if (item.prop === "recordType") {
      return data[item.prop] === "INCOME" ? "收入" : "支出";
    } else {
      return data[item.prop] || "--";
    }
  };
});

// 计算是否有子订单显示退单按钮
const hasSubOrderRefundButton = computed(() => {
  if (
    !form.value.subOrdersDetails ||
    !steadArrForRefund.includes(form.value.orderStatus)
  ) {
    return false;
  }

  return form.value.subOrdersDetails.some(subOrder => {
    return (
      subOrder.refundStatus === null ||
      subOrder.refundStatus === "申请退款" ||
      subOrder.refundStatus === "退款驳回" ||
      subOrder.refundStatus === "退款失败" ||
      subOrder.refundStatus === "部分取消" ||
      subOrder.refundStatus === "取消"
    );
  });
});
const formData = ref([
  {
    label: "订单号",
    type: "text",
    prop: "orderNo",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "创建时间",
    type: "text",
    // check: true,
    prop: "createdAt",
    placeholder: "",
    width: "200px"
  },
  {
    label: "订单状态",
    type: "text",
    prop: "orderStatus",
    // check: true,
    placeholder: "",
    width: "200px",
    span: 2
  },
  {
    label: "付款时间",
    type: "text",
    prop: "payTime",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    // label: "完成时间",
    label: judgeTime(),
    type: "text",
    prop: "finishTime",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "课程名",
    type: "text",
    prop: "courseName",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "课程期号",
    type: "text",
    prop: "termNumber",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "机构",
    type: "text",
    prop: "organizationName",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "机构客服热线",
    type: "text",
    prop: "organizationServicePhone",
    // check: true,
    // isEye: true,
    isView: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "机构管理员",
    type: "text",
    prop: "organizationManager",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "管理员电话",
    type: "text",
    prop: "organizationManagerPhone",
    // check: true,
    isEye: true,
    isView: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "购买人",
    type: "text",
    prop: "buyer",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "购买人电话",
    type: "text",
    prop: "buyerPhone",
    // check: true,
    isEye: true,
    isView: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "费用明细",
    type: "text",
    prop: "specification",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "购买价格",
    type: "text",
    prop: "totalPrice",
    // check: true,
    placeholder: "",
    width: "200px",
    span: 2
  },
  {
    label: "订单附带文件",
    type: "upload",
    // check: true,
    prop: "orderFile",
    placeholder: "",
    width: "200px",
    span: 2
  }
]);
const formfootData = ref([
  {
    label: "子订单号",
    type: "text",
    prop: "id",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "学生",
    type: "text",
    prop: "studentName",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "证件类型",
    type: "text",
    prop: "idType",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "证件号",
    type: "text",
    prop: "idNumber",
    isEye: true,
    isView: true,
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "退款状态",
    type: "text",
    prop: "refundStatus",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: row => (row?.refundStatus ? "退款金额" : "订单金额"),
    type: "text",
    prop: "price",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "退款理由",
    type: "text",
    prop: "refundReason",
    // check: true,
    placeholder: "",
    width: "200px",
    // 只有当退款理由存在且不为空时才显示该字段
    showCondition: row => row?.refundReason && row.refundReason.trim() !== ""
  }
  // {
  //   label: "订单附带文件",
  //   type: "upload",
  //   // check: true,
  //   prop: "orderFile",
  //   placeholder: "",
  //   width: "200px"
  // }
]);
const formDataV2 = ref([
  {
    label: "编号",
    type: "text",
    prop: "id",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "类型",
    type: "text",
    prop: "recordType",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "支付渠道",
    type: "text",
    prop: "channelType",
    // check: true,
    placeholder: "",
    width: "200px"
  },
  {
    label: "备注",
    type: "text",
    prop: "remarks",
    // check: true,
    placeholder: "",
    width: "200px"
  }
]);
const coursePeriodId = ref("");
const newData = ref();
// 根据id查询
const getData = async () => {
  let params = { id: adminId.value };
  // console.log("🎉-----params>>>>>>>>>>>>>>>-----", params);
  // return
  try {
    const { code, data, msg } = await getOrderDetails(params);
    console.log("🎁-----data-----", code, data, msg);
    if (code == 200 && data) {
      coursePeriodId.value = data.coursePeriodId || 0;
      //判断是否为团单
      if (data.groupBuying) {
        // 检查是否已经添加了团购相关字段，避免重复添加
        const hasPromoterId = formData.value.some(
          item => item.prop === "promoterId"
        );
        if (!hasPromoterId) {
          formData.value.unshift(
            {
              label: "团购ID",
              type: "text",
              prop: "promoterId",
              // check: true,
              placeholder: "",
              width: "200px"
            },
            {
              label: "团购发起人",
              type: "text",
              prop: "promoter",
              // check: true,
              placeholder: "",
              width: "200px"
            }
          );
        }
      }
      form.value = data;
      if (data.files === null) {
        form.value.files = [];
      } else {
        fileList.value = [];
        form.value.files.forEach(element => {
          fileList.value.push({
            fileIdentifier: element.uploadFile.fileIdentifier,
            sortOrder: fileList.value.length + 1
          });
        });
      }
      form.value.orderStatus = judgestead(data.orderStatus);
      // 处理主订单的退款状态
      if (data.refundStatus) {
        form.value.refundStatus = refundType(data.refundStatus);
      }
      if (
        form.value.subOrdersDetails &&
        form.value.subOrdersDetails.length > 0
      ) {
        form.value.subOrdersDetails.forEach(subOrder => {
          subOrder.orderStatus = judgestead(subOrder.orderStatus);
          // 处理子订单的退款状态
          if (subOrder.refundStatus) {
            subOrder.refundStatus = refundType(subOrder.refundStatus);
          }
          // 处理退款理由
          if (
            subOrder.refund &&
            subOrder.refund.reason &&
            subOrder.refund.reason.trim() !== ""
          ) {
            subOrder.refundReason = subOrder.refund.reason;
          }
          // 为每个子订单对象设置初始的 isView 状态为 true
          subOrder.isView = true;
        });
      }
      // formFile.value = data.files;
      newData.value = JSON.parse(JSON.stringify(data));
    }
  } catch (error) {
    console.log("catch-----error-----", error);
  }
};

const getByOrders = async () => {
  try {
    let params = { ordersId: adminId.value };
    const { code, data, msg } = await refundFindByOrdersId(params);
    // console.log("🍭-----根据订单id查询退款列表-----", code, data, msg);
    if (code == 200 && data) {
      // console.log("🐬-----data-----", data);
      if (data.length > 0) {
        const subOrdersMap = new Map(
          form.value.subOrdersDetails.map(val => [val.id, val])
        );
        data.forEach(element => {
          console.log("🎁-----element.refundStatus-----", element);
          if (
            // element.refundType === "ORDERS" &&
            element.refundStatus === "APPLICATION"
          ) {
            // form.value.orderStatusV2 = true;
          }
          if (
            // element.refundType === "SUBORDERS" &&
            element.refundStatus === "APPLICATION"
          ) {
            console.log("🌳-----element.subOrdersId-----", element.subOrdersId);
            const subOrder = subOrdersMap.get(element.subOrdersId);
            if (subOrder) {
              // subOrder.orderStatusV2 = true;
            }
          }
        });
      }
    }
  } catch (error) {
    console.log("🦄-----error-----", error);
  }
};

//查看关联课程
const cancel = () => {
  router.push({
    path: "/course/currentDetails/relatedCourse",
    query: {
      periodId: route.query.periodId || coursePeriodId.value || courseID.value,
      text: "course",
      courseId: route.query.courseId,
      related: "otherModle", //其他模块跳转到课期详情，不展示下架编辑等按钮
      fromFinance: true // 添加标记表明是从财务管理页面跳转
    }
  });
  useCourseStore.savePeriodState("ONLINE");
};
const edit = () => {
  router.push({
    path: "/institution/baseEdit",
    query: { id: adminId.value }
  });
};

const isViewFn = (val, type, item) => {
  // console.log('🎁-----val-----', val);
  if (type) {
    form.value.subOrdersDetails[val].isView =
      !form.value.subOrdersDetails[val].isView;
    if (form.value.subOrdersDetails[val].isView) {
      form.value.subOrdersDetails[val][item] =
        newData.value.subOrdersDetails[val][item];
    } else {
      let v = item + "Ct";
      form.value.subOrdersDetails[val][item] = decrypt(
        newData.value.subOrdersDetails[val][v]
      );
    }
    // console.log('🐬----- form.value.subOrdersDetails-----', form.value.subOrdersDetails);
  } else {
    formData.value[val].isView = !formData.value[val].isView;
    if (formData.value[val].isView) {
      form.value[item] = newData.value[item];
    } else {
      let v = item + "Ct";
      form.value[item] = decrypt(newData.value[v]);
    }
  }
};
//文件处理
const fileData = () => {
  //机构营业执照
  let institutionLicense = formFile.value.institutionLicense;
  //资质文件
  let qualificationDocuments = formFile.value.qualificationDocuments;
  if (institutionLicense.length > 0) {
    setFilesFn(institutionLicense, "BUSINESS_LICENSE");
  }
  if (qualificationDocuments.length > 0) {
    setFilesFn(qualificationDocuments, "QUALIFICATION_DOCUMENT");
  }
};
const setFilesFn = (val, type) => {
  for (let index = 0; index < val.length; index++) {
    const element = val[index].fileIdentifier;
    // console.log("🌵-----element-----", element);
    form.value.files.push({
      fileType: type,
      fileIdentifier: element,
      sortOrder: index + 1
    });
  }
};
const uploadCount = ref(0); // 上传文件计数器
const allowedFileTypes = [
  "application/pdf",
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  "image/png",
  "image/jpeg"
];
// 文件上传
const beforeUpload = async (file, item) => {
  if (!allowedFileTypes.includes(file.type)) {
    ElMessage.error("只能上传 pdf, word, png, jpg, jpeg 文件");
    return false;
  }
  let { code, data } = await uploadFile(file);
  if (code === 200) {
    console.log("🌳-----data-----", data);
    // form.value.files = []; // 清空文件集合
    form.value.files.push(data);
    console.log("🐬-----fileList.value-----", form.value.files);
    // setFilesFnV2(); //文件集合处理
    // return;
    // ordersFile();
    fileList.value.push({
      fileIdentifier: data.fileIdentifier,
      sortOrder: fileList.value.length + 1
    });
    ordersFile("上传");
  }
};
const fileList = ref([]);

const setFilesFnV2 = () => {
  // 使用 Set 来确保 fileIdentifier 的唯一性
  const fileSet = new Set(fileList.value.map(file => file.fileIdentifier));
  form.value.files.forEach((element, index) => {
    if (!fileSet.has(element.fileIdentifier)) {
      fileList.value.push({
        fileIdentifier: element.fileIdentifier,
        sortOrder: index + 1
      });
      fileSet.add(element.fileIdentifier);
      console.log("🍪-----element.fileIdentifier-----", element.fileIdentifier);
    }
  });
};
const ordersFile = async val => {
  let pormise = {
    ordersId: form.value.id,
    files: fileList.value
  };
  // let text = val  ? "上传" : "删除";
  // if (val === "删除") {
  //   pormise = {
  //     ordersId: form.value.id
  //   };
  // } else {
  // pormise = {
  //   ordersId: form.value.id,
  //   files: fileList.value
  //   // files: [
  //   //   {
  //   //     fileIdentifier: val.fileIdentifier,
  //   //     sortOrder: 1
  //   //   }
  //   // ]
  // };
  // }
  console.log("🐳-----pormise-----", pormise);
  const operateLog = {
    operateLogType: "ORDER_MANAGEMENT",
    operateType: `${val}了`,
    // operateType: "",
    operatorTarget: `“${form.value.courseName}”的订单文件`
  };
  let { code, data } = await ordersUploadFile(pormise, operateLog);
  if (code == 200) {
    ElMessage({
      type: "success",
      // message: `${val}成功`
      message: "系统处理中，请稍后查询确认"
    });
    getData();
  } else {
    ElMessage({
      type: "error",
      message: `${val}失败`
    });
  }
  // console.log("🌈-----code, data-----", code, data);
};
//删除文件
const getDeleted = (item, index) => {
  console.log("🐳-----index-----", index);
  form.value.files.splice(index, 1);
  fileList.value.splice(index, 1);
  // 重新排序 fileList 中的文件
  fileList.value.forEach((file, idx) => {
    file.sortOrder = idx + 1;
  });
  console.log("🐳-----fileList.value-----", fileList.value);
  // return;
  ordersFile("删除");
};
const isSubmitting = ref(false);

// 退款驳回弹窗相关
const refundRejectDialogVisible = ref(false);
const currentRejectOrderId = ref(null);
const currentRejectType = ref("");

const Freeze = async (row, type, val = "") => {
  if (isSubmitting.value) return; // 如果已经在提交中，则不再执行

  const typeName = val ? "" : type === "reject" ? "驳回" : "退单";

  // 如果是驳回操作，显示驳回理由弹窗
  if (type === "reject") {
    currentRejectOrderId.value = row;
    currentRejectType.value = val === "全部驳回" ? "all" : "single"; // 'single' 表示单个驳回，'all' 表示全部驳回
    refundRejectDialogVisible.value = true;
    return;
  }

  try {
    await ElMessageBox.confirm(
      `你确定要${val}${typeName}吗?`,
      `确认${typeName}`,
      {
        confirmButtonText: `确认${typeName}`,
        cancelButtonText: `取消`,
        type: ""
      }
    );

    isSubmitting.value = true; // 设置提交状态为true
    disabledButtonIds.value.add(row); // 添加当前子订单ID到禁用集合中

    try {
      await isChargebackApi(row, type, val);
    } finally {
      // 无论请求成功或失败，都重置提交状态
      setTimeout(() => {
        isSubmitting.value = false;
      }, 1000); // 设置一个短暂的延迟，防止快速重复点击
    }
  } catch (error) {
    // 用户取消操作，不做任何处理
  }
};
// 处理退款驳回弹窗确认
const handleRefundRejectConfirm = async reason => {
  if (isSubmitting.value) return;

  isSubmitting.value = true;
  disabledButtonIds.value.add(currentRejectOrderId.value);

  try {
    await isChargebackApi(
      currentRejectOrderId.value,
      "reject",
      currentRejectType.value === "all" ? "全部驳回" : "",
      reason
    );
  } finally {
    setTimeout(() => {
      isSubmitting.value = false;
    }, 1000);
    refundRejectDialogVisible.value = false;
    currentRejectOrderId.value = null;
    currentRejectType.value = "";
  }
};

// 处理退款驳回弹窗重置
const handleRefundRejectReset = () => {
  refundRejectDialogVisible.value = false;
  currentRejectOrderId.value = null;
  currentRejectType.value = "";
};

// 新增：退单功能（参考平台端实现）
const handleRefund = async subOrderId => {
  if (isSubmitting.value) return; // 如果已经在提交中，则不再执行

  try {
    await ElMessageBox.confirm("你确定要退单吗?", "确认退单", {
      confirmButtonText: "确认退单",
      cancelButtonText: "我再想想",
      type: ""
    });

    isSubmitting.value = true; // 设置提交状态为true
    disabledButtonIds.value.add(subOrderId); // 添加当前子订单ID到禁用集合中

    try {
      await refundApi(subOrderId);
    } finally {
      // 无论请求成功或失败，都重置提交状态
      setTimeout(() => {
        isSubmitting.value = false;
      }, 1000); // 设置一个短暂的延迟，防止快速重复点击
    }
  } catch (error) {
    // 用户取消操作，不做任何处理
  }
};

// 子订单退单API调用
const refundApi = async subOrderId => {
  const params = {
    subOrderId: subOrderId
  };

  const operateLog = {
    operateLogType: "ORDER_MANAGEMENT",
    operateType: "退单了子订单",
    operatorTarget: form.value.courseName
  };

  try {
    const { code } = await refundSub(params, operateLog);
    if (code === 200) {
      // 操作成功后，从禁用列表中移除对应的ID
      disabledButtonIds.value.delete(subOrderId);
      ElMessageBox.alert("系统处理中，请稍后查询确认", "提示", {
        confirmButtonText: "确认"
      });
      getData();
    } else {
      // 操作失败时，也从禁用列表中移除对应的ID，允许重试
      disabledButtonIds.value.delete(subOrderId);
      ElMessage({
        type: "error",
        message: "子订单退单失败"
      });
    }
  } catch (error) {
    // 发生异常时，也从禁用列表中移除对应的ID，允许重试
    disabledButtonIds.value.delete(subOrderId);
    console.error("子订单退单API调用出错:", error);
    ElMessage.error("子订单退单操作失败");
  }
};

// 新增：主订单退单功能
const handleMainOrderRefund = async () => {
  if (isSubmitting.value) return; // 如果已经在提交中，则不再执行

  try {
    await ElMessageBox.confirm("你确定要退单吗?", "确认主订单退单", {
      confirmButtonText: "确认退单",
      cancelButtonText: "我再想想",
      type: ""
    });

    isSubmitting.value = true; // 设置提交状态为true

    try {
      await mainOrderRefundApi();
    } finally {
      // 无论请求成功或失败，都重置提交状态
      setTimeout(() => {
        isSubmitting.value = false;
      }, 1000); // 设置一个短暂的延迟，防止快速重复点击
    }
  } catch (error) {
    // 用户取消操作，不做任何处理
  }
};

// 主订单退单API调用
const mainOrderRefundApi = async () => {
  const params = {
    orderId: form.value.id
  };

  const operateLog = {
    operateLogType: "ORDER_MANAGEMENT",
    operateType: "退单了主订单",
    operatorTarget: form.value.courseName
  };

  try {
    const { code } = await refund(params, operateLog);
    if (code === 200) {
      ElMessageBox.alert("系统处理中，请稍后查询确认", "提示", {
        confirmButtonText: "确认"
      });
      getData();
    } else {
      ElMessage({
        type: "error",
        message: "主订单退单失败"
      });
    }
  } catch (error) {
    console.error("主订单退单API调用出错:", error);
    ElMessage.error("主订单退单操作失败");
  }
};

const isChargebackApi = async (row, type, val, reason = "") => {
  const typeName = type == "reject" ? "驳回" : "退单";
  const params = {
    id: row
  };

  // 如果是驳回操作且有理由，添加reason参数
  if (type === "reject" && reason) {
    params.reason = reason;
  }

  let api = null;
  if (val == "全部退单") {
    api = confirmRefund;
  } else if (!val && typeName == "退单") {
    api = confirmRefundSub;
  } else if (!val && typeName == "驳回") {
    api = refundRejectedSub;
  } else if (val === "全部驳回") {
    api = refundRejected;
  }

  const operateLog = {
    operateLogType: "ORDER_MANAGEMENT",
    operateType: `${typeName}了订单`,
    operatorTarget: form.value.courseName
  };

  try {
    const { code } = await api(params, operateLog);
    if (code === 200) {
      // 操作成功后，从禁用列表中移除对应的ID
      disabledButtonIds.value.delete(row);
      if (typeName === "驳回") {
        ElMessage({
          type: "success",
          message: `${typeName}成功`
        });
      } else {
        // 如果是全部退单并且成功，禁用全部退单按钮
        if (val === "全部退单") {
          isRefundButtonDisabled.value = true;
        }

        ElMessageBox.alert("系统处理中，请稍后查询确认", "提示", {
          confirmButtonText: "确认"
        });
      }
      getData();
    } else {
      // 操作失败时，也从禁用列表中移除对应的ID，允许重试
      disabledButtonIds.value.delete(row);
      ElMessage({
        type: "error",
        message: `${typeName === "驳回" ? "驳回" : "退单"}失败`
      });
    }
  } catch (error) {
    // 发生异常时，也从禁用列表中移除对应的ID，允许重试
    disabledButtonIds.value.delete(row);
    console.error("API调用出错:", error);
    ElMessage.error("操作失败");
  }
};
const dialogTableVisible = ref(false);
const gridData = ref([]);
const getOrdersId = async () => {
  let params = { ordersId: form.value.id };
  try {
    const { code, data, msg } = await findByOrdersId(params);
    // console.log("🎁-----data-----", code, data, msg);
    if (code == 200 && data) {
      gridData.value = data;
      gridData.value.forEach(e => {
        // 适配新接口：ordersType 替代 refundType
        e.refundType = e.ordersType === "ORDERS" ? "主订单" : "子订单";
        // 适配新接口：applyStatus 替代 refundStatus
        e.refundStatus = refundType(e.applyStatus);
      });
    }
  } catch (error) {
    console.log("catch-----error-----", error);
  }
};
function onCloseOnClickModalClick() {
  dialogTableVisible.value = true;
  getOrdersId();
}
//处理购买规格字符串
const buyGroupToString = str => {
  // console.log(str)
  try {
    // 尝试将字符串作为 JSON 解析
    return JSON.parse(str).join("+");
  } catch (error) {
    // 若解析失败，直接返回原字符串
    return str;
  }
};
</script>

<template>
  <div :class="{ content: !styleTyle }">
    <div class="period">
      <PeriodInfo
        v-if="route.query.type === 'course'"
        :periodId="Number(route.query.periodId)"
      />
    </div>
    <div :class="route.query.type === 'course' ? 'other' : ''">
      <el-scrollbar :height="route.query.type === 'course' ? '57vh' : '76vh'">
        <el-descriptions
          v-if="ordersDataJson"
          title=""
          :column="2"
          border
          :label-width="'15%'"
        >
          <el-descriptions-item
            v-for="(item, index) in formDataV2"
            :key="index"
            label-align="center"
            label-class-name="my-label"
          >
            <template #label>
              {{ item.label }}
            </template>
            <el-form-item
              :prop="item.prop"
              :inline-message="item.check"
              style="margin-bottom: 0"
              :show-message="true"
              error-placement="right"
            >
              <!-- text -->
              <template v-if="item.type === 'text'">
                <div class="cell_item">
                  <div>
                    {{ displayValue(item, ordersDataJson) }}
                  </div>
                </div>
              </template>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>

        <div v-if="ordersDataJson" class="box" />
        <el-descriptions title="" :column="2" border :label-width="'15%'">
          <el-descriptions-item
            v-for="(item, index) in formData"
            :key="index"
            label-align="center"
            label-class-name="my-label"
            :span="item.span || 1"
          >
            <template #label>
              {{ item.label }}
            </template>
            <el-form-item
              :prop="item.prop"
              :inline-message="item.check"
              style="margin-bottom: 0"
              :show-message="true"
              error-placement="right"
            >
              <!-- text -->
              <template v-if="item.type === 'text'">
                <div class="cell_item">
                  <div style="">
                    <span v-if="item.prop === 'totalPrice'">
                      <!-- 保留两位数小数 -->
                      {{
                        `￥${form[item.prop]?.toFixed(2) || "--"} （原价￥${form.originalPrice?.toFixed(2) || "--"}）`
                      }}
                    </span>
                    <span v-else-if="item.prop === 'specification'">
                      {{ buyGroupToString(form[item.prop]) }}
                    </span>
                    <span v-else>
                      {{
                        item.prop === "createdAt" ||
                        (item.prop === "payTime" && form[item.prop]) ||
                        (item.prop === "finishTime" && form[item.prop])
                          ? formatTime(form[item.prop], "YYYY-MM-DD HH:mm:ss")
                          : form[item.prop] || "--"
                      }}</span>
                  </div>
                  <span v-if="form[item.prop] && item.isEye" class="icon">
                    <el-icon
                      v-if="item.isView"
                      style="cursor: pointer"
                      @click="isViewFn(index, false, item.prop)"
                    >
                      <Hide />
                    </el-icon>
                    <el-icon
                      v-else
                      style="cursor: pointer"
                      @click="isViewFn(index, false, item.prop)"
                    >
                      <View />
                    </el-icon>
                  </span>
                </div>
              </template>
              <!-- 示例：上传组件 -->
              <template v-else-if="item.type === 'upload'">
                <div class="upload_box">
                  <el-upload
                    action="#"
                    :show-file-list="false"
                    class="upload-demo"
                    :http-request="() => {}"
                    accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.txt"
                    :before-upload="file => beforeUpload(file, item.prop)"
                    multiple
                  >
                    <img :src="uploadImg" alt="">
                    <!-- <img v-if="" :src="uploadImg" alt="" /> -->
                  </el-upload>
                  <!-- <span v-if="form?.files === null">{{ "--" }}</span> -->
                  <template v-for="(item2, index2) in form.files" :key="index2">
                    <!-- {{ item2 }} -->
                    <FileItem
                      isNeedDelte
                      :data="item2.uploadFile ? item2.uploadFile : item2"
                      :index="index2"
                      @delete="getDeleted(item.prop, index2)"
                    />
                    <!-- <div v-show="item2?.uploadFile.fileName" class="fileOther"> -->
                    <!-- <div class="title">附件</div>
                   <img class="link" src="@/assets/login/link.png" alt=""> -->
                    <!-- <div
                   v-preview="{
                     url: item2.url,
                     type: fileType(item2?.fileName),
                     name: item2?.fileName
                   }"
                   class="fileName"
                 >
                   {{ item2?.uploadFile.fileName }}
                 </div> -->

                    <!-- <img
                   class="linkDetele"
                   src="@/assets/login/linkDetele.png"
                   alt=""
                   @click="handleClickDetele(item.prop, index2)"
                 > -->
                    <!-- </div> -->
                  </template>
                </div>
              </template>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
        <div v-for="(t, i) in form.subOrdersDetails" :key="i" class="subclass">
          <el-descriptions title="" :column="2" border :label-width="'15%'">
            <el-descriptions-item
              v-for="(item, index) in formfootData"
              v-show="!item.showCondition || item.showCondition(t)"
              :key="index"
              label-align="center"
              label-class-name="my-label"
            >
              <template #label>
                {{
                  typeof item.label === "function" ? item.label(t) : item.label
                }}
              </template>
              <el-form-item
                :prop="item.prop"
                :inline-message="item.check"
                style="margin-bottom: 0"
                :show-message="true"
                error-placement="right"
              >
                <!-- text -->
                <template v-if="item.type === 'text'">
                  <div class="cell_item">
                    <div
                      style="
                        display: flex;
                        justify-content: space-between;
                        width: 100%;
                      "
                    >
                      <div style="display: flex">
                        <span v-if="item.prop === 'price'">
                          <!-- 保留两位数小数 -->
                          {{ `￥${t[item.prop]?.toFixed(2) || "--"}` }}
                        </span>
                        <span v-else>
                          {{
                            item.prop === "createdAt"
                              ? formatTime(
                                  t[item.prop],
                                  "YYYY-MM-DD HH:mm:ss"
                                ) || "--"
                              : t[item.prop] || "--"
                          }}
                        </span>
                        <span v-if="t[item.prop] && item.isEye" class="icon">
                          <el-icon
                            v-if="t.isView"
                            style="cursor: pointer"
                            @click="isViewFn(i, true, item.prop)"
                          >
                            <Hide />
                          </el-icon>
                          <el-icon
                            v-else
                            style="cursor: pointer"
                            @click="isViewFn(i, true, item.prop)"
                          >
                            <View />
                          </el-icon>
                        </span>
                      </div>
                      <!-- 新增：独立的退单按钮（参考平台端，在orderStatus字段显示） -->
                      <el-button
                        v-if="
                          item.prop === 'refundStatus' &&
                          steadArrForRefund.includes(form.orderStatus) &&
                          (t.refundStatus === null ||
                            t.refundStatus === '申请退款' ||
                            t.refundStatus === '退款驳回' ||
                            t.refundStatus === '退款失败' ||
                            t.refundStatus === '部分取消' ||
                            t.refundStatus === '取消')
                        "
                        style="margin-left: auto"
                        type="warning"
                        :disabled="disabledButtonIds.has(t.id)"
                        @click="handleRefund(t.id)"
                      >
                        退单
                      </el-button>
                      <el-button
                        v-if="
                          item.prop === 'refundStatus' &&
                          t.refundStatus === '申请退款' &&
                          !form.groupBuying
                        "
                        style="margin-left: auto"
                        type="primary"
                        :disabled="disabledButtonIds.has(t.id)"
                        @click="Freeze(t.id, 'reject')"
                      >
                        驳回退单
                      </el-button>
                      <!-- {{ t }} -->
                      <el-button
                        v-if="
                          item.prop === 'refundStatus' &&
                          t.refundStatus === '申请退款' &&
                          !form.groupBuying
                        "
                        type="primary"
                        :disabled="disabledButtonIds.has(t.id)"
                        @click="Freeze(t.id, 'resolve')"
                      >
                        确认退单
                      </el-button>
                    </div>
                  </div>
                </template>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-scrollbar>
      <div class="account_management">
        <el-button type="primary" @click="cancel">查看关联课程</el-button>
        <el-button type="primary" @click="onCloseOnClickModalClick">
          退单详情
        </el-button>
        <el-button
          v-if="
            form.refundStatus === '申请退款' &&
            form.subOrdersDetails &&
            form.subOrdersDetails.some(
              order => order.refundStatus === '申请退款'
            ) &&
            !form.groupBuying
          "
          type="primary"
          @click="Freeze(form.id, 'reject', '全部驳回')"
        >
          全部驳回
        </el-button>

        <!-- 新增：主订单退单按钮 -->
        <el-button
          v-if="hasSubOrderRefundButton"
          type="warning"
          :disabled="isSubmitting"
          @click="handleMainOrderRefund"
        >
          主订单退单
        </el-button>

        <el-button
          v-if="
            form.refundStatus === '申请退款' &&
            form.subOrdersDetails?.some(
              order => order.refundStatus === '申请退款'
            ) &&
            !form.groupBuying
          "
          type="primary"
          :disabled="isRefundButtonDisabled"
          @click="Freeze(form.id, 'resolve', '全部退单')"
        >
          确认全部退单
        </el-button>

        <!-- <el-button
       v-if="steadArr.includes(form.orderStatus)"
       type="primary"
       @click="Freeze(form.id, 'resolve', '全部驳回')"
     >
       全部驳回
     </el-button> -->
      </div>
      <el-dialog v-model="dialogTableVisible" title="" align-center width="800">
        <template #header>
          <div class="my-header" style="font-weight: 600">退单详情</div>
        </template>
        <el-table :data="gridData">
          <el-table-column
            property="id"
            label="编号"
            width="100"
            align="center"
          />
          <el-table-column
            property="ordersId"
            label="订单Id"
            width="100"
            align="center"
          />
          <el-table-column
            property="subOrdersId"
            label="子订单Id"
            align="center"
          />
          <el-table-column property="userName" label="用户名" align="center">
            <template #default="scope">
              {{ scope.row.userName || "--" }}
            </template>
          </el-table-column>
          <el-table-column
            property="refundPrice"
            label="退款金额"
            align="center"
          >
            <template #default="scope">
              {{ scope.row.refundPrice || "--" }}
            </template>
          </el-table-column>
          <el-table-column
            property="reason"
            label="退款原因"
            align="center"
            width="110"
          >
            <template #default="scope">
              {{ scope.row.reason || "--" }}
            </template>
          </el-table-column>
          <el-table-column
            property="refundStatus"
            label="申请状态"
            align="center"
          >
            <template #default="scope">
              {{ scope.row.refundStatus || "--" }}
            </template>
          </el-table-column>
          <el-table-column
            property="refundType"
            label="申请类型"
            align="center"
          >
            <template #default="scope">
              {{ scope.row.refundType || "--" }}
            </template>
          </el-table-column>
        </el-table>
      </el-dialog>

      <!-- 退款驳回弹窗 -->
      <RefundRejectDialog
        v-model:dialogFormVisible="refundRejectDialogVisible"
        :title="currentRejectType === 'all' ? '全部驳回退款' : '驳回退款'"
        @confirm="handleRefundRejectConfirm"
        @reset="handleRefundRejectReset"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-descriptions__cell) {
  width: 34%;
}
.content {
  padding: 20px;
  background: #fff;
}
.other {
  padding: 20px;
  background: #fff;
}
.header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 20px;

  .title_left {
    display: flex;

    :nth-child(2) {
      margin-left: 40px;
    }
  }

  .title_rigth {
    display: flex;

    :nth-child(2) {
      margin-left: 40px;
    }
  }
}

.account_management {
  margin-top: 20px;
  box-sizing: border-box;
  display: flex;
  justify-content: flex-end;
  // justify-content: space-between;
  :nth-child(2) {
    // margin-left: 20px;
  }
}

:deep(.my-label) {
  background: #e1f5ff !important;
}
.cell_item {
  width: 100%;
  display: flex;
  .icon {
    display: flex;
    align-items: center;
    margin-left: 10px;
  }
}
.upload-demo {
  display: flex;
  align-items: center;
}
.fileOther {
  // margin-top: 26px;
  margin-left: 20px;
  display: flex;
  align-items: center;
  line-height: 22px;
  width: 420px;
  // margin-bottom: 56px;
  .title {
    color: #0e1832;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    white-space: nowrap;
  }
  .link {
    width: 20px;
    height: 20px;
    margin: 0 10px;
    cursor: pointer;
  }
  .fileName {
    color: #3876fd;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    cursor: pointer;
    // width: 350px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .linkDetele {
    width: 20px;
    height: 20px;
    margin-left: 10px;
    cursor: pointer;
  }
}
.subclass {
  margin-top: 20px;
}
.box {
  margin-bottom: 20px;
}
.upload_box {
  display: flex;
  width: 100%;
  flex-direction: column;
}
</style>
