<script setup>
import { onMounted, ref } from "vue";
import { getBankAccount, editBankAccount } from "@/api/institution";
import { ElMessage } from "element-plus";

// 表单数据
const form = ref({
  corporateName: "",
  corporateAddress: "",
  corporateTaxID: "",
  bankName: "",
  bankAddress: "",
  bankAccount: ""
});

// 对话框显示状态
const dialogVisible = ref(false);
// 表单引用
const formRef = ref(null);
// 加载状态
const loading = ref(false);

// 账户信息展示数据
const accountInfo = ref([
  {
    label: "公司名称",
    value: "",
    key: "corporateName"
  },
  {
    label: "公司地址",
    value: "",
    key: "corporateAddress"
  },
  {
    label: "公司税号",
    value: "",
    key: "corporateTaxID"
  },
  {
    label: "开户银行名称",
    value: "",
    key: "bankName"
  },
  {
    label: "开户银行地址",
    value: "",
    key: "bankAddress"
  },
  {
    label: "银行账户",
    value: "",
    key: "bankAccount"
  }
]);

// 表单校验规则 - 移除必填验证
const rules = ref({});

onMounted(() => {
  getAccount();
});

// 获取银行账户信息
const getAccount = async () => {
  try {
    loading.value = true;
    const { code, data, msg } = await getBankAccount();
    if (code === 200 && data) {
      // 更新展示数据
      accountInfo.value.forEach(item => {
        item.value = data[item.key] || "";
      });

      // 保存原始数据用于编辑
      Object.keys(form.value).forEach(key => {
        form.value[key] = data[key] || "";
      });
    } else if (code === 200 && !data) {
      // 处理数据为空的情况
      console.log("银行账户信息为空");
      // 清空展示数据
      accountInfo.value.forEach(item => {
        item.value = "";
      });
      // 清空表单数据
      Object.keys(form.value).forEach(key => {
        form.value[key] = "";
      });
    } else {
      ElMessage({
        message: msg || "获取银行账户信息失败",
        type: "error"
      });
    }
  } catch (error) {
    console.log("🚨-----error-----", error);
    ElMessage({
      message: "获取银行账户信息失败",
      type: "error"
    });
  } finally {
    loading.value = false;
  }
};

// 打开编辑对话框
const openEditDialog = () => {
  dialogVisible.value = true;
};

// 取消编辑
const handleCancel = () => {
  dialogVisible.value = false;
};

// 提交表单
const handleSave = () => {
  formRef.value.validate(async valid => {
    if (valid) {
      try {
        loading.value = true;
        // 构建参数对象,过滤掉空值
        const params = Object.entries({
          corporateName: form.value.corporateName,
          corporateAddress: form.value.corporateAddress,
          corporateTaxID: form.value.corporateTaxID,
          bankName: form.value.bankName,
          bankAddress: form.value.bankAddress,
          bankAccount: form.value.bankAccount
        }).reduce((acc, [key, value]) => {
          if (value) {
            acc[key] = value;
          }
          return acc;
        }, {});

        const { code, data, msg } = await editBankAccount(params);

        if (code === 200) {
          getAccount();
          ElMessage({
            message: "保存成功",
            type: "success"
          });

          dialogVisible.value = false;
        } else {
          ElMessage({
            message: "保存失败",
            type: "error"
          });
        }
      } catch (error) {
        console.log("🚨-----error-----", error);
        ElMessage({
          message: "保存失败",
          type: "error"
        });
      } finally {
        loading.value = false;
      }
    }
  });
};
</script>

<template>
  <div>
    <!-- 银行账户信息展示 -->
    <el-card v-loading="loading" style="min-height: 88vh">
      <div class="card-header">
        <h2 class="page-title">银行账户信息</h2>
        <el-button type="primary" class="edit-button" @click="openEditDialog">
          编辑
        </el-button>
      </div>

      <div class="info-sections">
        <!-- 公司信息区 -->
        <div class="info-section">
          <div class="section-header">
            <span style="margin-left: 10px">公司信息</span>
          </div>
          <el-descriptions :column="1" border class="descriptions-content">
            <el-descriptions-item
              v-for="item in accountInfo.slice(0, 3)"
              :key="item.key"
              :label="item.label"
              label-width="120px"
              label-class-name="custom-label"
              class-name="custom-content"
              label-align="center"
            >
              {{ item.value || "--" }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 银行信息区 -->
        <div class="info-section">
          <div class="section-header">
            <span style="margin-left: 10px">银行信息</span>
          </div>
          <el-descriptions :column="1" border class="descriptions-content">
            <el-descriptions-item
              v-for="item in accountInfo.slice(3)"
              :key="item.key"
              :label="item.label"
              label-width="120px"
              label-class-name="custom-label"
              class-name="custom-content"
              label-align="center"
            >
              {{ item.value || "--" }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-card>

    <!-- 编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="银行账户信息编辑"
      width="60vh"
      :close-on-click-modal="false"
      style="padding: 4vh"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        class="form-container"
      >
        <el-form-item
          v-for="item in accountInfo.slice(0, 3)"
          :key="item.key"
          :label="item.label"
          :prop="item.key"
          label-width="120px"
          class="form-item"
        >
          <el-input v-model="form[item.key]" placeholder="请输入" />
        </el-form-item>
        <el-form-item
          v-for="item in accountInfo.slice(3)"
          :key="item.key"
          :label="item.label"
          :prop="item.key"
          label-width="120px"
          class="form-item"
        >
          <el-input v-model="form[item.key]" placeholder="请输入" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span>
          <el-button @click="handleCancel">取消</el-button>
          <el-button :loading="loading" type="primary" @click="handleSave">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 20px;
}

.page-title {
  font-size: 20px;
  color: #303133;
  margin: 0;
}

.edit-button {
  min-width: 100px;
}

.info-sections {
  display: flex;
  flex-direction: column;
  gap: 30px;
  padding: 0 20px;
  margin-bottom: 20px;
}

.info-section {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #ebeef5;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  background-color: #ebf4fc;
  border-radius: 4px 4px 0 0;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.descriptions-content {
  padding: 0;
}

.form-container {
  padding: 0 20px;
}

.form-item {
  margin-bottom: 22px;
}

.form-section-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin: 20px 0 15px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

// 调整 el-descriptions 样式
:deep(.el-descriptions__label) {
  text-align: right;
  margin-right: 20px;
  font-weight: bold;
  color: #606266;
}

:deep(.el-descriptions__cell) {
  padding: 12px !important;
}

:deep(.custom-label) {
  background-color: #f5f7fa;
  padding: 8px 12px;
}

:deep(.custom-content) {
  padding: 12px 15px;
}

:deep(.el-descriptions__body) {
  width: 100%;
}

:deep(.el-descriptions) {
  width: 100%;
  margin-bottom: 0;
}

:deep(.el-descriptions__table) {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

// Dialog 样式
:deep(.el-dialog__header) {
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 15px;
  margin-bottom: 10px;
}

:deep(.el-dialog__title) {
  font-weight: bold;
  color: #303133;
}

:deep(.el-dialog__footer) {
  border-top: 1px solid #ebeef5;
  padding-top: 15px;
  margin-top: 10px;
}

:deep(.el-card) {
  border-radius: 4px;
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

:deep(.el-card__body) {
  padding: 20px 0;
}

@media screen and (min-width: 992px) {
  .info-sections {
    flex-direction: row;
    align-items: stretch;

    .info-section {
      flex: 1;
      margin: 0 10px;

      &:first-child {
        margin-left: 0;
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
