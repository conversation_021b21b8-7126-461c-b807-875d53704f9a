<script setup>
import { ref, onMounted, onActivated } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  courseFindId,
  leaderLecturerFind,
  coursePeriodOffline,
  coursePeriodOnline,
  coursePeriodDelete,
  periodOpenGroupOrder,
  courseDelete
} from "@/api/course.js";
import { findCommentsAll } from "@/api/period.js";
import { ElMessage, ElMessageBox } from "element-plus";
import { requestTo } from "@/utils/http/tool";
import { formatTime } from "@/utils/index";
import { courseStore } from "@/store/modules/course.js";
import { to } from "@iceywu/utils";
import OrderDialog from "@/components/Base/orderDialog.vue";
import { Warning } from "@element-plus/icons-vue";
import { AUDIT_ENUM } from "@/utils/enum";
import { ImageThumbnail } from "@/utils/imageProxy.js";
defineOptions({
  name: "CourseDetails"
});
onActivated(() => {
  getTablePeriodList();
  getTableList();
});
const useCourseStore = courseStore();
const router = useRouter();
const route = useRoute();
const copyShow = ref(false);
const url = ref();
const srcList = ref([]);
// 表头
const tableHeader = ref([
  {
    id: "1",
    label: "课程名",
    value: "",
    width: "107px"
  },
  {
    id: "2",
    label: "课程ID",
    value: "",
    width: "107px"
  },
  {
    id: "3",
    label: "创建时间",
    value: "",
    width: "107px"
  },
  {
    id: "4",
    label: "课程类型",
    value: "",
    width: "107px"
  },
  {
    id: "5",
    label: "课程亮点标签",
    value: "",
    width: "107px"
  },
  {
    id: "6",
    label: "课程状态",
    value: "",
    width: "107px"
  }
]);
// 表格
const tableData = ref([]);
// 搜索表单
const form = ref({
  startTime: "",
  endTime: "",
  termNumber: "",
  userName: "",
  score: "",
  startRating: "",
  endRating: "",
  pickTime: ""
});

// 重置
const setData = () => {
  form.value = {};
  params.value.page = 1;
  value1.value = [];
  pickTime.value = "";
  getTablePeriodList();
};
// 清除数据
const clearEvt = val => {
  if (val === "name") {
    form.value.userName = "";
  } else if (val === "time") {
    form.value.startTime = "";
    form.value.endTime = "";
  } else if (val === "termNumber") {
    form.value.termNumber = "";
  } else if (val === "startRating") {
    form.value.startRating = "";
  }
  getTablePeriodList();
};
//搜索
const searchData = () => {
  params.value.page = 1;
  getTablePeriodList();
};
const pickTime = ref("");
const value1 = ref([]);
// 选择时间
const timeChange = value => {
  // console.log("🐬-----value-----", value);
  if (value) {
    form.value.startTime = new Date(value[0])?.getTime();
    // form.value.endTime = value[1];
    const endDate = new Date(value[1]);
    endDate.setHours(23, 59, 59, 999); // 关键修改
    form.value.endTime = endDate.getTime();
  }
};

const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  const [err, result] = await requestTo(
    courseFindId({ id: route.query.courseId })
  );
  if (result) {
    tableHeader.value[0].value = result.name || "--";
    tableHeader.value[1].value = result.id || "--";
    tableHeader.value[2].value =
      formatTime(result.createdAt, "YYYY-MM-DD HH:mm:ss") || "--";
    tableHeader.value[3].value = result.courseType?.name || "--";
    if (result.tags && result.tags.length) {
      tableHeader.value[4].value = result.tags?.join("、");
    } else {
      tableHeader.value[4].value = "--";
    }

    tableHeader.value[5].value = result.freeze === false ? "正常" : "冻结";
    if (result.files?.length) {
      result.files.map(item => {
        srcList.value.push(item?.uploadFile?.url);
      });
      url.value = result.files[0]?.uploadFile?.url;
    }
    // tableHeader.value[5].value = result.complex?.name || "--";
    useCourseStore.saveCourseInfo(result);
  } else {
    ElMessage.error(err);
  }
  getListLoading.value = false;
};
const isOffline = ref(true);
// 获取用户评价列表信息
const gettLoading = ref(false);
const getTablePeriodList = async data => {
  if (gettLoading.value) {
    return;
  }
  gettLoading.value = true;
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort,
    courseId: Number(route.query.courseId)
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  const [err, result] = await requestTo(findCommentsAll(paramsData));
  if (result) {
    tableData.value = result?.content;
    params.value.totalElements = result.totalElements;
  } else {
    ElMessage.error(err);
  }
  gettLoading.value = false;
};
// 分数最大值和最小值
const handleChangeMax = () => {
  if (form.value.endRating < 0) {
    ElMessage({
      type: "error",
      message: "最大评分不能小于0"
    });
    form.value.endRating = "";
    return;
  }
  if (form.value.endRating > 99999) {
    ElMessage({
      type: "error",
      message: "最大评分不能大于99999"
    });
    form.value.endRating = 99999;
    return;
  }
  if (form.value.startRating && form.value.endRating) {
    if (form.value.startRating > form.value.endRating) {
      ElMessage({
        type: "error",
        message: "最小评分不能大于最大评分"
      });
      form.value.startRating = "";
      form.value.endRating = "";
      return;
    } else {
      getTablePeriodList();
    }
  }
};
const handleChangeMin = () => {
  if (form.value.startRating < 0) {
    ElMessage({
      type: "error",
      message: "最小评分不能小于0"
    });
    form.value.startRating = 0;
    return;
  }
  if (form.value.startRating > 99999) {
    ElMessage({
      type: "error",
      message: "最小评分不能大于99999"
    });
    form.value.startRating = 99999;
    return;
  }
};
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTablePeriodList();
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getTablePeriodList();
};
// 详情
const detailEvt = id => {
  // console.log("💗getInfoid---------->", row);
  router.push({
    path: "/course/evaluateContent",
    query: {
      courseId: route.query.courseId,
      id: id
    }
  });
  useCourseStore.savePeriodState(row.coursePeriodState);
};

onMounted(async () => {
  getTableList();
  getTablePeriodList();
});
</script>

<template>
  <div class="containers">
    <div class="content_top">
      <div class="tabHeastyle">
        <el-descriptions
          class="margin-top"
          title=""
          :column="3"
          border
          style="width: 1300px"
        >
          <template v-for="(item, index) in tableHeader" :key="index">
            <el-descriptions-item width="120px" label-align="center">
              <template #label>
                <div class="cell-item">{{ item.label }}</div>
              </template>

              <div :style="{ color: item.value === '冻结' ? '#f56c6c' : '' }">
                {{ item.value }}
              </div>
            </el-descriptions-item>
          </template>
        </el-descriptions>
        <div class="img">
          <!-- <img src="@/assets/user.jpg" alt="" /> -->
          <el-image
            :src="ImageThumbnail(url)"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="srcList"
            :hide-on-click-modal="true"
            show-progress
            :initial-index="4"
            fit="cover"
            class="img-pic"
          />
        </div>

        <div class="tabtn">
          <!-- <el-button type="primary" @click="turnfile">资质文件</el-button> -->
          <div class="text">用户评价数量</div>
          <div class="num">{{ params.totalElements || 0 }}</div>
        </div>
      </div>
    </div>
    <div class="content_bottom">
      <div class="con_search">
        <el-form :model="form" :inline="true">
          <el-form-item label="评价时间">
            <el-date-picker
              v-model="value1"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              @change="timeChange"
              @clear="clearEvt('time')"
            />
          </el-form-item>
          <el-form-item label="期号">
            <el-input
              v-model.trim="form.termNumber"
              placeholder="请输入"
              style="width: 240px"
              clearable
              @clear="clearEvt('termNumber')"
            />
          </el-form-item>
          <el-form-item label="用户名">
            <el-input
              v-model.trim="form.userName"
              style="width: 240px"
              placeholder="请输入"
              clearable
              @clear="clearEvt('name')"
            />
          </el-form-item>
          <el-form-item label="评分">
            <div class="range-input">
              <el-input
                v-model.number="form.startRating"
                placeholder="输最小值"
                type="number"
                style="width: 100px"
                :min="0"
                :max="99999"
                @change="handleChangeMin"
              />
              <span class="separator">-</span>
              <el-input
                v-model.number="form.endRating"
                placeholder="输最大值"
                type="number"
                style="width: 100px"
                :min="0"
                :max="99999"
                @change="handleChangeMax"
              />
            </div>
          </el-form-item>
          <el-form-item label=" ">
            <div class="flex">
              <el-button type="primary" @click="searchData"> 搜索 </el-button>
              <el-button @click="setData">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div class="con_table">
        <el-table
          :data="tableData"
          table-layout="fixed"
          :header-cell-style="{ backgroundColor: '#fafafa', color: '#565353' }"
          highlight-current-row
          height="calc(100vh - 400px)"
        >
          <el-table-column prop="termNumber" label="期号" align="left" fixed>
            <template #default="scope">
              <div>
                {{ scope.row.termNumber || "0" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="userName" label="用户名" align="left" fixed>
            <template #default="scope">
              <div>
                {{ scope.row.userName || "--" }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            width="200px"
            prop="createdAt"
            label="评价时间"
            align="left"
          >
            <template #default="scope">
              <div>
                {{
                  formatTime(scope.row?.createdAt, "YYYY-MM-DD HH:mm:ss") ||
                  "--"
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="rating" label="分数" align="left">
            <template #default="scope">
              <div>
                {{ scope.row.rating || "0" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="content"
            label="评论内容"
            align="left"
            width="300"
          >
            <template #default="scope">
              <span class="no-wrap-text">{{ scope.row?.content || "--" }}</span>
            </template>
          </el-table-column>
          <el-table-column
            width="200px"
            prop="replies.updatedAt"
            label="回复时间"
            align="left"
          >
            <template #default="scope">
              <div>
                {{
                  formatTime(
                    scope.row?.replies?.updatedAt,
                    "YYYY-MM-DD HH:mm:ss"
                  ) || "--"
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="replies.content"
            label="机构回复"
            align="left"
            width="300"
          >
            <template #default="scope">
              <span class="no-wrap-text">{{
                scope.row.replies?.content || "--"
              }}</span>
              <!-- {{ scope.row.replies?.content || "--" }} -->
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            align="left"
            width="150px"
          >
            <template #default="{ row }">
              <div class="option">
                <div class="btnse" @click="detailEvt(row.id)">详情</div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="con_pagination">
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="params.page"
          v-model:page-size="params.size"
          class="pagination"
          background
          :page-sizes="[15, 30, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="params.totalElements"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  box-sizing: border-box;
  width: calc(100% - 48px);
  height: 100%;
  // padding: 24px;
  background: #f0f2f5;

  .content_top {
    box-sizing: border-box;
    padding: 20px;
    background-color: #fff;
    .tabHeastyle {
      display: flex;
      // align-items: flex-end;
      justify-content: space-between;

      .img {
        width: 145px;
        // height: 100px;
        height: 85px;
        margin: 0 20px;

        .img-pic {
          width: 145px;
          height: 85px;
          // object-fit: cover;
        }
      }
    }

    .tabtn {
      display: flex;
      flex-direction: column;
      align-items: center;
      .text {
        margin-bottom: 20px;
        white-space: nowrap;
      }
    }
  }

  .content_bottom {
    box-sizing: border-box;
    padding: 20px;
    margin-top: 20px;
    background-color: #fff;
  }
}

.con_search {
  display: flex;
  align-items: center;
  width: 100%;
  height: fit-content;
  // margin-top: 40px;
}
.state-reject {
  width: 40px;
  display: flex;
  // justify-content: center;
  align-items: center;
  white-space: nowrap;
}
:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}

.con_table {
  // width: calc(100% - 25px);
  // min-height: 500px;
  margin-bottom: 24px;

  // margin-left: 25px;
  .option {
    display: flex;

    .btnse {
      display: flex;
      margin-right: 16px;
      color: #4095e5;
      cursor: pointer;
    }
  }
}

.con_pagination {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}
.delete-tip {
  font-size: 14px;
  &:nth-of-type(1) {
    margin-left: 10px;
  }
}
.range-input {
  display: flex;
  align-items: center;
}

.separator {
  margin: 0 10px;
  color: #606266;
}
:deep(.el-form--inline .el-form-item) {
  margin-right: 32px;
}
.no-wrap-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  width: 100%;
}
</style>
