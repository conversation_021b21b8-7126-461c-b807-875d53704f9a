// export interface iconType {
//   // iconify (https://docs.iconify.design/icon-components/vue/#properties)
//   inline?: boolean;
//   width?: string | number;
//   height?: string | number;
//   horizontalFlip?: boolean;
//   verticalFlip?: boolean;
//   flip?: string;
//   rotate?: number | string;
//   color?: string;
//   horizontalAlign?: boolean;
//   verticalAlign?: boolean;
//   align?: string;
//   onLoad?: Function;
//   includes?: Function;
//   // svg 需要什么SVG属性自行添加
//   fill?: string;
//   // all icon
//   style?: object;
// }

const iconType = {
  // iconify (https://docs.iconify.design/icon-components/vue/#properties)
  inline: undefined, // boolean
  width: undefined, // string or number
  height: undefined, // string or number
  horizontalFlip: undefined, // boolean
  verticalFlip: undefined, // boolean
  flip: undefined, // string
  rotate: undefined, // number or string
  color: undefined, // string
  horizontalAlign: undefined, // boolean
  verticalAlign: undefined, // boolean
  align: undefined, // string
  onLoad: undefined, // Function
  includes: undefined, // Function
  // Add any necessary SVG attributes
  fill: undefined, // string
  // Common for all icons
  style: undefined // object
};

export default iconType;
