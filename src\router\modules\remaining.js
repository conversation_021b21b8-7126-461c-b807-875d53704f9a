import { $t } from "@/plugins/i18n";
const Layout = () => import("@/layout/index.vue");
import { reCodesList } from "@/router/accidCode.js";

export default [
  {
    path: "/login",
    name: "Login",
    component: () => import("@/views/login/index.vue"),
    meta: {
      title: $t("menus.pureLogin"),
      showLink: false,
      rank: 101
    }
  },
  {
    path: "/redirect",
    component: Layout,
    meta: {
      title: $t("status.pureLoad"),
      showLink: false,
      rank: 102
    },
    children: [
      {
        path: "/redirect/:path(.*)",
        name: "Redirect",
        component: () => import("@/layout/redirect.vue")
      },
      // {
      //   path: "/account/teacherDetails",
      //   name: "teacherDetails",
      //   component: () => import("@/views/account/teacherDetails.vue"),
      //   meta: {
      //     title: "详情",
      //     idCode: reCodesList.teacherDetails
      //   }
      // },
      // {
      //   path: "/account/qualificationsFile",
      //   name: "qualificationsFile",
      //   component: () => import("@/views/account/qualificationsFile.vue"),
      //   meta: {
      //     title: "资质文件",
      //     idCode: reCodesList.qualificationsFile
      //   }
      // },
      // {
      //   path: "/account/editInformation",
      //   name: "editInformation",
      //   component: () => import("@/views/account/editInformation.vue"),
      //   meta: {
      //     title: "编辑信息",
      //     idCode: reCodesList.editInformation
      //   }
      // },
      // {
      //   path: "/account/accountCreate",
      //   name: "accountCreate",
      //   component: () => import("@/views/account/accountCreate.vue"),
      //   meta: {
      //     title: "创建账号",
      //     idCode: reCodesList.accountCreate
      //   }
      // },
      // {
      //   path: "/account/accountDetails",
      //   name: "accountDetails",
      //   component: () => import("@/views/account/accountDetails.vue"),
      //   meta: {
      //     title: "账号详情",
      //     idCode: reCodesList.accountDetails
      //   }
      // },
      // {
      //   path: "/course/courseCreate",
      //   name: "courseCreate",
      //   component: () => import("@/views/course/courseCreate.vue"),
      //   meta: {
      //     title: "课程创建",
      //     idCode: reCodesList.courseCreate
      //   }
      // },
      // {
      //   path: "/course/courseDetails",
      //   name: "courseDetails",
      //   component: () => import("@/views/course/courseDetails.vue"),
      //   meta: {
      //     title: "课程详情",
      //     idCode: reCodesList.courseDetails
      //   }
      // },
      // {
      //   path: "/course/detailInformation",
      //   name: "detailInformation",
      //   component: () => import("@/views/course/detailInformation.vue"),
      //   meta: {
      //     title: "详细资料",
      //     idCode: reCodesList.detailInformation
      //   }
      // },
      // {
      //   path: "/course/periodCreate",
      //   name: "periodCreate",
      //   component: () => import("@/views/course/coursePeriodCreate.vue"),
      //   meta: {
      //     title: "课期创建",
      //     idCode: reCodesList.periodCreate
      //   }
      // },
      // {
      //   path: "/course/periodCopy",
      //   name: "periodCopy",
      //   component: () => import("@/views/course/copyPeriod.vue"),
      //   meta: {
      //     title: "复制课期",
      //     idCode: reCodesList.periodCopy
      //   }
      // },
      // {
      //   path: "/course/currentDetails",
      //   name: "currentDetails",
      //   redirect: "/course/currentDetails/tableList",
      //   component: () => import("@/views/course/currentDetails.vue"),
      //   meta: {
      //     title: "当期详情",
      //     idCode: reCodesList.currentDetails
      //   },
      //   children: [
      //     {
      //       path: "/course/currentDetails/tableList",
      //       name: "tableList",
      //       component: () => import("@/views/course/components/tableList.vue"),
      //       meta: {
      //         title: "切换列表",
      //         idCode: reCodesList.tableList
      //       }
      //     },
      //     {
      //       path: "/course/currentDetails/tripEdite",
      //       name: "tripEdite",
      //       component: () => import("@/views/course/components/tripEdite.vue"),
      //       meta: {
      //         title: "行程编辑",
      //         idCode: reCodesList.tripEdite
      //       }
      //     },
      //     {
      //       path: "/course/currentDetails/itineraryAdd",
      //       name: "itineraryAdd",
      //       component: () =>
      //         import("@/views/course/components/itineraryAdd.vue"),
      //       meta: {
      //         title: "行程点新增",
      //         idCode: reCodesList.itineraryAdd
      //       }
      //     },
      //     {
      //       path: "/course/currentDetails/introductionEdite",
      //       name: "introductionEdite",
      //       component: () =>
      //         import("@/views/course/components/introductionEdite.vue"),
      //       meta: {
      //         title: "课程介绍",
      //         idCode: reCodesList.introductionEdite
      //       }
      //     },
      //     {
      //       path: "/course/currentDetails/groupOrder",
      //       name: "groupOrder",
      //       component: () => import("@/views/course/components/groupOrder.vue"),
      //       meta: {
      //         title: "团购分享",
      //         idCode: reCodesList.groupOrder
      //       }
      //     },
      //     {
      //       path: "/course/currentDetails/freeEdite",
      //       name: "freeEdite",
      //       component: () => import("@/views/course/components/freeEdite.vue"),
      //       meta: {
      //         title: "费用退款",
      //         idCode: reCodesList.freeEdite
      //       }
      //     },
      //     {
      //       path: "/course/currentDetails/priceEdite",
      //       name: "priceEdite",
      //       component: () => import("@/views/course/components/priceEdite.vue"),
      //       meta: {
      //         title: "价格编辑",
      //         idCode: reCodesList.priceEdite
      //       }
      //     },
      //     {
      //       path: "/course/currentDetails/reportEdite",
      //       name: "reportEdite",
      //       component: () =>
      //         import("@/views/course/components/reportEdite.vue"),
      //       meta: {
      //         title: "课程报告编辑",
      //         idCode: reCodesList.reportEdite
      //       }
      //     },
      //     {
      //       path: "/course/currentDetails/evaluateDetail",
      //       name: "evaluateDetail",
      //       component: () =>
      //         import("@/views/course/components/evaluateDetail.vue"),
      //       meta: {
      //         title: "用户评价详情",
      //         idCode: reCodesList.evaluateDetail
      //       }
      //     },
      //     {
      //       path: "/course/currentDetails/homeWorkEdited",
      //       name: "homeWorkEdited",
      //       component: () =>
      //         import("@/views/course/components/homeWorkEdited.vue"),
      //       meta: {
      //         title: "作业设计编辑",
      //         idCode: reCodesList.homeWorkEdited
      //       }
      //     },
      //     {
      //       path: "/course/currentDetails/workDetail",
      //       name: "workDetail",
      //       component: () => import("@/views/course/components/workDetail.vue"),
      //       meta: {
      //         title: "作业详情",
      //         idCode: reCodesList.workDetail
      //       }
      //     },
      //     {
      //       path: "/course/currentDetails/relatedOrder",
      //       name: "relatedOrder",
      //       component: () =>
      //         import("@/views/course/components/relatedOrders.vue"),
      //       meta: {
      //         title: "关联订单",
      //         idCode: reCodesList.relatedOrder
      //       }
      //     }
      //   ]
      // },
      // {
      //   path: "/course/periodEdite",
      //   name: "periodEdite",
      //   component: () => import("@/views/course/periodInfoEdite.vue"),
      //   meta: {
      //     title: "课期信息编辑",
      //     idCode: reCodesList.periodEdite
      //   }
      // },
      // {
      //   path: "/institution/institutionEdit",
      //   name: "institutionEdit",
      //   component: () => import("@/views/institution/institutionEdit.vue"),
      //   meta: {
      //     title: "编辑信息",
      //     idCode: reCodesList.institutionEdit
      //   }
      // },
      {
        path: "/welcome/homeEdit",
        name: "homeEdit",
        component: () => import("@/views/welcome/homeEdit.vue"),
        meta: {
          title: "编辑信息",
          idCode: reCodesList.homeEdit
        }
      },
      {
        path: "/welcome/changePassword",
        name: "changePassword",
        component: () => import("@/views/welcome/changePassword.vue"),
        meta: {
          title: "修改密码",
          idCode: reCodesList.changePassword
        }
      }
      // {
      //   path: "/institution/baseAdd",
      //   name: "baseAdd",
      //   component: () => import("@/views/institution/baseAdd.vue"),
      //   meta: {
      //     title: "新建基地",
      //     idCode: reCodesList.baseAdd
      //   }
      // },
      // {
      //   path: "/institution/baseDetails",
      //   name: "baseDetails",
      //   component: () => import("@/views/institution/baseDetails.vue"),
      //   meta: {
      //     title: "基地详情",
      //     idCode: reCodesList.baseDetails
      //   }
      // },
      // {
      //   path: "/institution/baseEdit",
      //   name: "baseEdit",
      //   component: () => import("@/views/institution/baseEdit.vue"),
      //   meta: {
      //     title: "基地编辑",
      //     idCode: reCodesList.baseEdit
      //   }
      // },
      // {
      //   path: "/course/orderDetails",
      //   name: "courseOrderDetails",
      //   component: () => import("@/views/course/orderDetails.vue"),
      //   meta: {
      //     title: "订单详情",
      //     idCode: reCodesList.courseOrderDetails
      //   }
      // },
      // {
      //   path: "/institution/orderDetails",
      //   name: "orderDetails",
      //   component: () => import("@/views/institution/orderDetails.vue"),
      //   meta: {
      //     title: "关联订单",
      //     idCode: reCodesList.orderDetails
      //   }
      // },
    ]
  },
  // 下面是一个无layout菜单的例子（一个全屏空白页面），因为这种情况极少发生，所以只需要在前端配置即可（配置路径：src/router/modules/remaining.ts）
  {
    path: "/empty",
    name: "Empty",
    component: () => import("@/views/empty/index.vue"),
    meta: {
      title: $t("menus.pureEmpty"),
      showLink: false,
      rank: 103
    }
  },
  {
    path: "/account-settings",
    name: "AccountSettings",
    component: () => import("@/views/account-settings/index.vue"),
    meta: {
      title: $t("buttons.pureAccountSettings"),
      showLink: false,
      rank: 104
    }
  },
  {
    path: "/briefVideo/briefindex",
    name: "briefindex",
    component: Layout,
    meta: {
      title: "视频介绍",
      showLink: false,
      rank: 105
    },
    children: [
      {
        path: "",
        name: "BriefIndex",
        component: () => import("@/views/briefVideo/briefindex.vue"),
        meta: {
          title: "视频介绍",
          idCode: reCodesList.briefindex
        }
      }
    ]
  }
];
