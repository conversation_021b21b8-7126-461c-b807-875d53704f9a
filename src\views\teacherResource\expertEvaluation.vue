<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useRole } from "./utils/expertEvaluationHook.jsx";
const emites = defineEmits(["selectCourse"]);
// defineOptions({
//   name: "ParentManagement"
// });
const router = useRouter();
const {
  pagination,
  dataList,
  columns,
  handleCurrentChange,
  handleSizeChange,
  loadingTable,
  courseTypeoptions
  //   getInfoid
} = useRole();
const defaultTime = [
  new Date(2024, 4, 1, 0, 0, 0),
  new Date(2024, 4, 1, 23, 59, 59)
];
</script>

<template>
  <div>
    <div class="common">
      <div class="puretable">
        <pure-table
          ref="tableRef"
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="left"
          table-layout="auto"
          :loading="loadingTable"
          :data="dataList"
          :columns="columns"
          :pagination="{ ...pagination }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row }">
            <div class="operate">
              <el-button
                type="primary"
                link
                @click="
                  router.push({
                    path: '/expert/evaluation/current/detail',
                    query: { courseId: row.id, periodId: row.coursePeriodId }
                  })
                "
              >
                关联课程
              </el-button>
            </div>
          </template>
        </pure-table>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.common {
  // width: 100%;
  // height: 100%;
  padding: 20px 20px 2px;
  background-color: #fff;
  .title {
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
    color: #606266;
  }
  .search {
    display: flex;
    // justify-content: space-between;
    .button {
      display: flex;
      justify-content: right;
    }
  }
  // .puretable {
  //   margin-left: 25px;
  // }
}

.button_A {
  width: 80px;
  height: 30px;
  font-size: 16px;
  line-height: 30px;
  color: rgb(121.3 187.1 255);
  text-align: center;
  cursor: pointer;
  border: solid 1px rgb(121.3 187.1 255);
  border-radius: 8px;
}

.bottom {
  margin-bottom: 20px;
  padding-bottom: 2px;
}
:deep(.is-disabled) {
  background-color: transparent !important;
}
</style>
