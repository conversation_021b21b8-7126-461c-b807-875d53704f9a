import { http } from "@/utils/http";

//异步导入
export const getAsyncTask = params => {
  return http.request(
    "get",
    "/common/asyncTask/findById",
    { params },
    { isNeedEncrypt: true }
  );
};

//异步导入 AI工具接口
export const getAsyncTaskVET = params => {
  return http.request(
    "get",
    "/common/asyncTask/findById",
    { params },
    {
      serverName: "aiQuestion",
      isNeedFullRes: false, // 是否需要返回完整的响应对象
      isNeedToken: false, // 是否需要token
      isNeedEncrypt: false
    }
  );
};
