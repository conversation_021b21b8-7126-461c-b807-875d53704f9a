<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { codeInfo } from "@/utils/http/base";

const props = defineProps({
  redirectPath: {
    type: String,
    default: ""
  },
  fastLogin: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(["ready", "qrcode-ready", "error"]);

// DOM 引用
const qrContainerRef = ref(null);

// 状态管理
const isInitialized = ref(false);
const currentState = ref("");

// 生成新的 state
const generateState = () => {
  return Date.now().toString().slice(-5);
};

// 存储和获取 state 的方法
const saveStateToStorage = state => {
  sessionStorage.setItem("wx_login_state", state);
};

const getStateFromStorage = () => {
  return sessionStorage.getItem("wx_login_state");
};

const clearStateFromStorage = () => {
  sessionStorage.removeItem("wx_login_state");
};

// 环境配置
const getEnvConfig = () => {
  // 定义k8s默认环境配置
  const k8sConfig = {
    url: "https://gs-svc.gostaredu.com",
    code: 0,
    app_id: "wx4f152d3ccf5af3ed"
  };

  console.log("codeInfo", codeInfo);
  return codeInfo || k8sConfig;
};

// 固定配置
const FIXED_PATH = "/common/redirect/";
const SCOPE = "snsapi_login";

const redirectUri = computed(() => {
  const envConfig = getEnvConfig();

  // 处理redirectPath，分离路径和查询参数
  let cleanPath = props.redirectPath.startsWith("/")
    ? props.redirectPath.slice(1)
    : props.redirectPath;

  let baseUrl = `${envConfig.url}${FIXED_PATH}${envConfig.code}`;
  let finalUrl;

  // 如果cleanPath包含查询参数，需要特殊处理
  if (cleanPath.includes("?")) {
    const [pathPart, queryPart] = cleanPath.split("?");
    // 构建URL：基础URL + ?path=路径部分 + &原有查询参数
    // 不对路径部分进行编码，保持斜杠
    finalUrl = `${baseUrl}?path=${pathPart}&${queryPart}`;
  } else {
    // 没有查询参数的情况
    finalUrl = `${baseUrl}?path=${cleanPath}`;
  }

  // 微信登录SDK要求对整个重定向URI进行编码
  return encodeURIComponent(finalUrl);
});

const wxConfig = computed(() => ({
  self_redirect: false,
  id: "wx-qr-container",
  appid: getEnvConfig().app_id,
  scope: SCOPE,
  redirect_uri: redirectUri.value,
  state: currentState.value,
  style: "black",
  sizetype: "0", // 修改为 0，生成更小的二维码
  href: "",
  fast_login: props.fastLogin,
  stylelite: 1,
  onReady: isReady => {
    console.log("微信登录准备状态:", isReady);
    emit("ready", isReady);
  },
  onQRcodeReady: () => {
    console.log("二维码已生成");
    emit("qrcode-ready");
  }
}));

// 初始化微信登录
const initWxCode = () => {
  if (isInitialized.value) {
    console.log("微信登录已初始化，跳过重复初始化");
    return;
  }

  // 检查微信登录SDK是否已加载
  if (typeof WxLogin === "undefined") {
    console.error("微信登录SDK未加载，请检查script标签");
    emit("error", new Error("微信登录SDK未加载"));
    return;
  }

  // 生成新的 state 并存储到 sessionStorage
  currentState.value = generateState();
  saveStateToStorage(currentState.value);
  console.log("生成新的 state:", currentState.value);

  // 清理容器
  if (qrContainerRef.value) {
    qrContainerRef.value.innerHTML = "";
  }

  try {
    console.log("初始化微信登录配置:", wxConfig.value);
    new WxLogin(wxConfig.value);
    isInitialized.value = true;
    console.log("微信登录初始化成功");
  } catch (error) {
    console.error("微信登录初始化失败:", error);
    emit("error", error);
  }
};

// 重置方法
const reset = () => {
  isInitialized.value = false;
  clearStateFromStorage(); // 清除 sessionStorage 中的 state
  if (qrContainerRef.value) {
    qrContainerRef.value.innerHTML = "";
  }
};

// 重新初始化
const resetInit = () => {
  reset();
  setTimeout(() => {
    initWxCode();
  }, 100);
};

// 生命周期
onMounted(() => {
  // 可以在这里初始化，如果需要的话
  initWxCode();
});

onUnmounted(() => {
  reset();
});

// 暴露方法
defineExpose({
  initWxCode,
  reset,
  resetInit,
  getStateFromStorage // 暴露获取 state 的方法，供外部组件使用
});

const handleTest = () => {
  console.log("=== 微信登录重定向信息 ===");
  console.log("原始redirectPath:", props.redirectPath);

  // 显示处理过程
  let cleanPath = props.redirectPath.startsWith("/")
    ? props.redirectPath.slice(1)
    : props.redirectPath;
  console.log("清理后的路径:", cleanPath);

  const envConfig = getEnvConfig();
  let baseUrl = `${envConfig.url}${FIXED_PATH}${envConfig.code}`;
  let finalUrl;

  if (cleanPath.includes("?")) {
    const [pathPart, queryPart] = cleanPath.split("?");
    console.log("分离的路径部分:", pathPart);
    console.log("分离的查询参数部分:", queryPart);
    finalUrl = `${baseUrl}?path=${pathPart}&${queryPart}`;
  } else {
    finalUrl = `${baseUrl}?path=${cleanPath}`;
  }

  console.log("编码前的URL:", finalUrl);
  console.log("编码后的URL:", encodeURIComponent(finalUrl));
  console.log("最终的redirectUri:", redirectUri.value);
  console.log("当前state:", currentState.value);
};
</script>

<template>
  <div class="wx-qr-code">
    <div id="wx-qr-container" ref="qrContainerRef" class="qr-container" />
  </div>
  <!-- <el-button @click="handleTest">测试</el-button> -->
</template>

<style scoped>
.wx-qr-code {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 250px;
}

#wx-qr-container {
  height: 100%;
}

.qr-container {
  width: 100%;
  min-height: 150px;
  display: flex;
  justify-content: center;
  align-items: center;
}

:deep(#wx-qr-container iframe) {
  width: 100% !important;
  height: 100% !important;
  border: none !important;
  border-radius: 8px !important;
}
</style>
