<script setup lang="ts">
// import ReQrcode from "@/components/ReQrcode";
import { useUserStoreHook } from "@/store/modules/user";
import { useI18n } from "vue-i18n";
import Motion from "../utils/motion";
import { onMounted, onUnmounted, ref, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  Loading,
  User,
  Check,
  ArrowLeft,
  Right
} from "@element-plus/icons-vue";
import { getTopMenu, initRouter } from "@/router/utils";
import { message } from "@/utils/message";
import { getWxLogin } from "@/api/user.js";
import WxQrCode from "@/components/WxQrCode/index.vue";

const router = useRouter();
const route = useRoute();
const { t } = useI18n();

const loading = ref(false);
const isCallback = ref(false);
const callbackData = ref({
  code: "",
  state: ""
});

const wxQrCodeRef = ref(null);

// 多账号选择相关状态
const showAccountDialog = ref(false);
const accountList = ref([]);
const selectedAccountId = ref("");
const showAccountSelection = ref(false); // 显示账号选择页面

// 微信登录初始化状态
const isWxLoginInitialized = ref(false);

// 重置到初始状态
const resetToInitialState = () => {
  isCallback.value = false;
  loading.value = false;
  callbackData.value = { code: "", state: "" };
  showAccountDialog.value = false;
  showAccountSelection.value = false; // 重置账号选择页面状态
  accountList.value = [];
  selectedAccountId.value = "";
  isWxLoginInitialized.value = false; // 重置初始化状态

  // 清除处理记录，允许重新处理
  processedCallbacks.value.clear();

  // 清除URL参数并重新初始化微信登录
  router.replace({
    path: route.path,
    query: {}
  });
};

// 处理微信回调
const handleWxCallback = async (code: string, state: string) => {
  // 从 sessionStorage 获取 state 进行验证
  const storedState = sessionStorage.getItem("wx_login_state");
  if (state !== storedState) {
    message("微信登录状态不一致，请重新扫码", { type: "error" });
    resetToInitialState();
    return;
  }

  try {
    loading.value = true;

    const res = await getWxLogin({ code });
    console.log(res, "res");

    if (res.code === 200) {
      // 检查返回的账号数量
      if (res.data && res.data.length > 1) {
        // 多个账号，显示选择页面
        accountList.value = res.data;
        showAccountSelection.value = true;
        loading.value = false;
      } else if (res.data && res.data.length === 1) {
        // 单个账号，直接登录（loginWithAccount 内部会管理 loading 状态）
        await loginWithAccount(res.data[0].id);
      } else {
        message("当前微信尚未绑定平台账号，请先完成绑定后再次尝试", {
          type: "error"
        });
        // 重新显示二维码（resetToInitialState 内部会重置 loading）
        resetToInitialState();
      }
    } else {
      // 接口返回失败状态码
      message(res.msg || "微信登录失败，请重新扫码", { type: "error" });
      resetToInitialState();
    }
  } catch (error) {
    console.error("微信登录请求失败:", error);
    message("微信登录失败，请重新扫码", { type: "error" });
    resetToInitialState();
  }
};

// 使用指定账号登录
const loginWithAccount = async (accountId: string) => {
  try {
    loading.value = true;
    await useUserStoreHook()
      .loginById({ id: accountId }, "微信扫码")
      .then(res => {
        return initRouter().then(() => {
          router.push(getTopMenu(true).path).then(() => {
            message(t("login.pureLoginSuccess"), { type: "success" });
          });
        });
      })
      .catch(err => {
        // 登录失败后重置状态
        resetToInitialState();
      });
  } catch (err) {
    console.log(err);
    message("登录失败，请重试", { type: "error" });
    // 登录失败后重置状态
    resetToInitialState();
  } finally {
    loading.value = false;
  }
};

// 确认选择的账号
const confirmAccountSelection = async () => {
  if (!selectedAccountId.value) {
    message("请选择要登录的账号", { type: "warning" });
    return;
  }

  showAccountSelection.value = false;
  await loginWithAccount(selectedAccountId.value);
};

// 取消账号选择，返回二维码登录页面
const cancelAccountSelection = () => {
  showAccountSelection.value = false;
  accountList.value = [];
  selectedAccountId.value = "";
  loading.value = false;

  // 清除URL参数，回到初始状态
  router.replace({
    path: route.path,
    query: {}
  });

  // 清除处理记录，允许重新处理
  processedCallbacks.value.clear();
};

// 用于防止重复处理同一个回调
const processedCallbacks = ref(new Set());

// 监听路由参数变化
watch(
  () => route.query,
  newQuery => {
    const { code, state } = newQuery;
    console.log(code, state, "code, state");

    if (code && state) {
      // 生成唯一标识，防止重复处理
      const callbackId = `${code}_${state}`;

      // 如果已经处理过这个回调，就不再处理
      if (processedCallbacks.value.has(callbackId)) {
        console.log("回调已处理过，跳过:", callbackId);
        return;
      }

      // 标记为已处理
      processedCallbacks.value.add(callbackId);

      isCallback.value = true;
      callbackData.value = { code: code as string, state: state as string };
      handleWxCallback(code as string, state as string);
    } else {
      // 没有回调参数时，清理处理记录
      processedCallbacks.value.clear();
    }
  },
  { immediate: true }
);

const testFn = () => {
  router.push({
    path: "login",
    query: {
      code: "021tGXll2UA6Pf41Ihll2YhUdd4tGXlC",
      state: "82269"
    }
  });
};

// 返回账号登录
const onBack = () => {
  useUserStoreHook().SET_CURRENTPAGE(0);
};

// 切换到手机号登录
const switchToPhoneLogin = () => {
  useUserStoreHook().SET_CURRENTPAGE(1);
};
</script>

<template>
  <!-- 二维码登录页面 -->
  <div v-if="!showAccountSelection">
    <Motion class="-mt-2 -mb-2">
      <!-- 二维码组件 -->
      <WxQrCode ref="wxQrCodeRef" :redirectPath="route.path" :fastLogin="1" />
    </Motion>
    <!-- <el-button class="w-full mt-4" @click="testFn"> 测试login </el-button> -->

    <Motion :delay="300">
      <el-form-item>
        <div class="w-full h-[20px] flex justify-between items-center">
          <el-button class="w-full" size="default" @click="onBack">
            <!-- {{ t("login.pureBack") }} -->
            {{ "账号登录" }}
          </el-button>
          <el-button class="w-full" size="default" @click="switchToPhoneLogin">
            <!-- {{ t("login.pureBack") }} -->
            {{ "手机号登录" }}
          </el-button>
        </div>
      </el-form-item>
    </Motion>
  </div>

  <!-- 账号选择页面 -->
  <div v-if="showAccountSelection" class="account-selection-page">
    <!-- 页面头部 -->
    <Motion>
      <div class="selection-header">
        <div class="header-top">
          <el-button
            link
            type="primary"
            class="back-button"
            @click="cancelAccountSelection"
          >
            <el-icon class="mr-1">
              <ArrowLeft />
            </el-icon>
            切换登录方式
          </el-button>
        </div>
        <div class="header-content">
          <h3 class="selection-title">选择登录账号</h3>
          <p class="selection-subtitle">
            当前微信关联了 {{ accountList.length }} 个账号，请选择要登录的账号
          </p>
        </div>
      </div>
    </Motion>

    <!-- 账号列表 -->
    <Motion :delay="100">
      <div class="accounts-section">
        <div
          v-for="account in accountList"
          :key="account.id"
          class="account-card"
          :class="{ selected: selectedAccountId === account.id }"
          @click="selectedAccountId = account.id"
        >
          <div class="account-content">
            <div class="account-info flex items-center">
              <div class="account-name mr-2">
                {{ account.name || account.username || "未知用户" }}
              </div>
              <div class="account-organization">
                {{ account.organizationName || "未知机构" }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Motion>

    <!-- 底部按钮 -->
    <Motion :delay="200">
      <div class="selection-footer">
        <el-button
          type="primary"
          size="large"
          class="continue-button"
          :loading="loading"
          :disabled="!selectedAccountId"
          @click="confirmAccountSelection"
        >
          继续
          <el-icon class="ml-1">
            <Right />
          </el-icon>
        </el-button>
      </div>
    </Motion>
  </div>

  <!-- 账号选择对话框 -->
  <el-dialog
    v-model="showAccountDialog"
    title=""
    width="440px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="account-dialog"
    align-center
  >
    <!-- 自定义标题 -->
    <template #header>
      <div class="dialog-header">
        <div class="header-icon">
          <el-icon size="24" color="#3b82f6">
            <User />
          </el-icon>
        </div>
        <div class="header-content">
          <h3 class="header-title">选择登录账号</h3>
          <p class="header-subtitle">
            当前微信关联了多个账号，请选择要登录的账号
          </p>
        </div>
      </div>
    </template>

    <div class="account-selection">
      <div class="account-list">
        <div
          v-for="account in accountList"
          :key="account.id"
          class="account-item"
          :class="{ 'account-item-selected': selectedAccountId === account.id }"
          @click="selectedAccountId = account.id"
        >
          <div class="account-content">
            <div class="account-avatar">
              <span class="avatar-text">
                {{ (account.name || account.username || "未知用户").charAt(0) }}
              </span>
            </div>
            <div class="account-info">
              <div class="account-name-row">
                <span class="account-name">
                  {{ account.name || account.username || "未知用户" }}
                </span>
                <el-icon
                  v-if="selectedAccountId === account.id"
                  size="16"
                  color="#3b82f6"
                  class="check-icon"
                >
                  <Check />
                </el-icon>
              </div>
              <div class="account-organization">
                {{ account.organizationName || "未知机构" }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button
          size="large"
          class="cancel-btn"
          @click="cancelAccountSelection"
        >
          取消
        </el-button>
        <el-button
          type="primary"
          size="large"
          :loading="loading"
          :disabled="!selectedAccountId"
          class="confirm-btn"
          @click="confirmAccountSelection"
        >
          确认登录
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
/* 账号选择对话框样式 */
:deep(.account-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.account-dialog .el-dialog__header) {
  padding: 0;
  margin: 0;
  border-bottom: none;
}

:deep(.account-dialog .el-dialog__body) {
  padding: 0 24px 24px;
}

:deep(.account-dialog .el-dialog__footer) {
  padding: 16px 24px 24px;
  border-top: none;
}

/* 对话框头部 */
.dialog-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 24px;
  gap: 12px;
}

.header-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #dbeafe;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.header-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  line-height: 1.2;
}

.header-subtitle {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  max-width: 320px;
}

/* 账号列表 */
.account-selection {
  padding: 16px 0;
}

.account-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.account-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
  background: #fff;
  cursor: pointer;
}

.account-item:hover {
  border-color: #d1d5db;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.account-item-selected {
  border-color: #3b82f6 !important;
  background: #eff6ff;
  box-shadow: 0 0 0 2px #3b82f6;
}

.account-content {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 12px;
}

.account-avatar {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar-text {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
}

.account-info {
  flex: 1;
  min-width: 0;
}

.account-name-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.account-name {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.account-organization {
  font-size: 14px;
  color: #6b7280;
  margin-top: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.check-icon {
  flex-shrink: 0;
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.cancel-btn {
  min-width: 80px;
}

.confirm-btn {
  min-width: 100px;
}

/* 确保二维码容器有合适的样式 */
#login_container {
  width: 100%;
  min-height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 账号选择页面样式 */
.account-selection-page {
  width: 100%;
  max-width: 400px;
}

.selection-header {
  text-align: left;
  margin-bottom: 24px;
}

.header-top {
  margin-bottom: 16px;
}

.back-button {
  padding: 0;
  font-size: 14px;
}

.header-content {
  margin-top: 8px;
}

.selection-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  text-align: left;
}

.selection-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  text-align: left;
}

.accounts-section {
  margin-bottom: 24px;
}

.account-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #fff;
}

.account-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.account-card.selected {
  border-color: #1677ff;
  background: #fff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.2);
}

.account-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.account-info {
  flex: 1;
  min-width: 0;
}

.account-name-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.account-name {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.account-organization {
  font-size: 14px;
  color: #6b7280;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.selection-footer {
  position: sticky;
  bottom: 0;
  background: #fff;
  padding: 16px 0;
  border-top: 1px solid #f3f4f6;
  margin-top: 24px;
}

.continue-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
}
</style>
