<script setup>
import { ref, onMounted } from "vue";
import TabTitle from "@/components/Base/tabInfo.vue";
import Scheduling from "./scheduling.vue";
import CourseReport from "./courseReport.vue";
import PriceSetting from "./priceSetting.vue";
import courseIntroduction from "./courseIntroduction.vue";
import StudentSituation from "./studentSituation.vue";
import JobDesign from "./jobDesign.vue";
import UserEvaluate from "./userEvaluate.vue";
import ClassTrack from "./classTrack.vue";
import { useRouter, useRoute } from "vue-router";
import { requestTo } from "@/utils/http/tool";
import Knowledge from "./knowledge.vue";
import {
  introductionfindById,
  nowledgefindById,
  descriptionfindById,
  precautionsfindById,
  createOrUpdate,
  nowledgeCreateOrUpdate,
  descriptionCreateOrUpdate,
  precautionsCreateOrUpdate,
  userAgreementId,
  userAgreementUpdate
} from "@/api/period.js";
const props = defineProps({
  maxPeopleNumber: {
    type: [Number, String],
    default: ""
  },
  minPeopleNumber: {
    type: [Number, String],
    default: ""
  },
  dynamicHeight: {
    type: String,
    default: ""
  }
});
const router = useRouter();
const route = useRoute();
// 判断api类型
const getApiType = (val, isEdit) => {
  let res = "";
  switch (val) {
    case "课期介绍":
      res = isEdit ? createOrUpdate : introductionfindById;
      break;
    // case "课期知识点":
    //   res = isEdit ? nowledgeCreateOrUpdate : nowledgefindById;
    //   break;
    case "材料说明":
      res = isEdit ? descriptionCreateOrUpdate : descriptionfindById;
      break;
    case "注意事项":
      res = isEdit ? precautionsCreateOrUpdate : precautionsfindById;
      break;
    case "用户协议":
      res = isEdit ? userAgreementUpdate : userAgreementId;
      break;
    default:
      break;
  }
  return res;
};
const tabTitle = ref([
  { id: 0, name: "行程安排" },
  { id: 1, name: "课期介绍" },
  { id: 2, name: "课期知识点" },
  { id: 3, name: "材料说明" },
  { id: 4, name: "注意事项" },
  { id: 5, name: "价格设置" },
  { id: 6, name: "实践感悟" },
  { id: 7, name: "用户协议" },
  { id: 8, name: "学生情况" },
  { id: 9, name: "课堂跟踪" },
  { id: 10, name: "课期报告" },
  { id: 11, name: "用户评价" }
]);
const infoShow = ref("行程安排");
const btnText = ref("编辑介绍");
const tabInfoEvt = obj => {
  infoShow.value = obj.name;
  if (obj.name === "课期介绍") {
    btnText.value = "编辑介绍";
    getIntroductionfindById(obj.name);
  }
  // else if (obj.name === "课期知识点") {
  //   getIntroductionfindById(obj.name);
  //   btnText.value = "编辑知识点";
  // }
  else if (obj.name === "材料说明") {
    getIntroductionfindById(obj.name);

    btnText.value = "编辑材料说明";
  } else if (obj.name === "注意事项") {
    getIntroductionfindById(obj.name);

    btnText.value = "编辑注意事项";
  } else if (obj.name === "用户协议") {
    getIntroductionfindById(obj.name);
  }
};
const showContent = ref();
// 查询详情
const getIntroductionfindById = async typeName => {
  showContent.value = "";
  const params = {
    coursePeriodId: Number(route.query.periodId)
  };
  let api = getApiType(typeName, false);
  let [err, res] = await requestTo(api(params));
  if (res) {
    showContent.value = res.content || "";
  } else {
    console.log("🐬-----err-----", err);
  }
};
// 判断按钮类型
const getBtnType = val => {
  let btnName = "";
  switch (val) {
    case "课期介绍":
      btnName = "编辑介绍";
      break;
    case "课期知识点":
      btnName = "编辑知识点";
      break;
    case "材料说明":
      btnName = "编辑材料说明";
      break;
    case "注意事项":
      btnName = "编辑注意事项";
      break;
    default:
      break;
  }
  return btnName;
};
onMounted(() => {
  if (route.query.infoShow) {
    infoShow.value = route.query.infoShow;
    btnText.value = getBtnType(infoShow.value);
  }
  if (
    infoShow.value === "课期介绍" ||
    infoShow.value === "课期知识点" ||
    infoShow.value === "材料说明" ||
    infoShow.value === "注意事项" ||
    infoShow.value === "用户协议"
  ) {
    getIntroductionfindById(infoShow.value);
  }
});
</script>

<template>
  <div class="tab-container">
    <!-- tab切换 -->
    <TabTitle :tabTitle="tabTitle" @tab-data="tabInfoEvt" />
    <!-- 切换信息 -->
    <div
      class="tab-info"
      :style="dynamicHeight ? { height: dynamicHeight } : {}"
    >
      <Scheduling v-if="infoShow === '行程安排'" />
      <CourseReport v-else-if="infoShow === '课期报告'" />
      <PriceSetting v-else-if="infoShow === '价格设置'" />
      <courseIntroduction
        v-else-if="
          infoShow === '课期介绍' ||
          infoShow === '材料说明' ||
          infoShow === '注意事项' ||
          infoShow === '用户协议'
        "
        :infoShow="infoShow"
        :btnText="btnText"
        :showContent="showContent"
      />
      <Knowledge
        v-else-if="infoShow === '课期知识点'"
        :infoShow="infoShow"
        :btnText="btnText"
        :showContent="showContent"
      />
      <StudentSituation
        v-else-if="infoShow === '学生情况'"
        :maxPeopleNumber="maxPeopleNumber"
        :minPeopleNumber="minPeopleNumber"
      />
      <JobDesign v-else-if="infoShow === '实践感悟'" />
      <UserEvaluate v-else-if="infoShow === '用户评价'" />
      <ClassTrack v-else-if="infoShow === '课堂跟踪'" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.tab-container {
  height: 100%;
  width: 100%;
  overflow: auto;
  // height: 100%;
  // display: flex;
  // flex-direction: column;
}
.tab-info {
  height: calc(100% - 42px);
  box-sizing: border-box;
  width: 100%;
  // height: 540px;
  padding: 20px;
  background-color: #fff;
  // border-top:2px solid red;
}
</style>
