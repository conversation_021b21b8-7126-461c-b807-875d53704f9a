<script setup>
import { ref, onUnmounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import RichEditor from "@/components/Base/RichEditor.vue";
import { complexSave, findArea } from "@/api/institution.js";
import { requestTo } from "@/utils/http/tool";
import { decrypt, encryption } from "@/utils/SM4.js";
import { courseStore } from "@/store/modules/course.js";
import { debounce } from "@iceywu/utils";
import Map from "./components/Map.vue";
import ImgPos from "@/assets/pos.png";
import FileItem from "@/components/PreviewV2/FileItem.vue";
import uploadImg from "@/assets/login/upload1.png";
import { Plus } from "@element-plus/icons-vue";
import {
  uploadFile,
  validateFileType,
  isOverSizeLimit
} from "@/utils/upload/upload";
const useCourseStore = courseStore();
const router = useRouter();
const route = useRoute();

// 表单
const form = ref({
  name: "", //实践点名称
  contacts: "",
  phoneNumber: "",
  introduction: "", //实践点介绍
  detailedAddress: "", // 详细地址
  emergencyPeople: "", // 紧急联系人
  emergencyPhone: "", // 紧急联系人电话
  latitude: "",
  longitude: "",
  tags: [],
  complexType: "", //类型
  oneSentenceIntroduction: "", //一句话简介
  files: [], //文件上传
  province: "", //省份
  city: "", //市
  district: "", //区
  street: "" //街道
});
const formFile = ref({
  images: [],
  video: []
});
// 提交
const formRef = ref(null);
const formData = ref([
  {
    label: "实践点名称",
    type: "input",
    prop: "name",
    check: true,
    placeholder: "请输入实践点名称",
    width: "400px",
    maxLength: 20
  },
  {
    label: "归属地信息",
    type: "cascader",
    prop: "address",
    check: true,
    span: 2,
    placeholder: "请选择详细地址",
    width: "400px"
  },
  {
    label: "详细地址",
    type: "map",
    prop: "detailedAddress",
    check: true,
    placeholder: "请输入详细地址",
    width: "400px"
  },
  {
    label: "类型",
    type: "select",
    prop: "complexType",
    check: true,
    placeholder: "请选择类型",
    width: "400px",
    opt: [
      { name: "会议室", val: "MEETING_ROOM" },
      { name: "教室", val: "CLASSROOM" },
      { name: "大厅", val: "LOBBY" },
      { name: "广场", val: "SQUARE" },
      { name: "公园", val: "PARK" },
      { name: "景点", val: "ATTRACTION" },
      { name: "户外", val: "OUTDOORS" },
      { name: "其他", val: "OTHER" }
    ]
  },
  {
    label: "实践点联系人",
    type: "input",
    prop: "emergencyPeople",
    check: true,
    placeholder: "请输入实践点联系人",
    width: "400px",
    maxLength: 10
  },
  {
    label: "实践点联系电话",
    type: "input",
    prop: "emergencyPhone",
    check: true,
    placeholder: "请输入实践点联系电话",
    width: "400px",
    maxLength: 15
  },
  {
    label: "标签",
    type: "tag",
    prop: "tags",
    check: true,
    placeholder: "请新建标签",
    // width: "400px",
    // maxLength: 15,
    tags: []
  },
  {
    label: "一句话简介",
    type: "textarea",
    prop: "oneSentenceIntroduction",
    check: true,
    placeholder: "请输入一句话简介",
    width: "400px",
    maxLength: 15
  },
  {
    label: "实践点介绍",
    type: "editor",
    prop: "introduction",
    check: true,
    placeholder: "请输入实践点介绍",
    width: "400px",
    span: 2
  },
  {
    label: "照片",
    type: "upload",
    prop: "images",
    limit: 9,
    check: true,
    placeholder: "请上传照片",
    width: "400px",
    maxLength: 15,
    text: "支持上传png、jpg、jpeg图片格式，最多上传9张，最佳尺寸:200*200px，单张图片大小不超过10MB"
  },
  {
    label: "视频",
    type: "upload",
    type2: "video",
    prop: "video",
    limit: 3,
    // check: true,
    placeholder: "请上传视频",
    width: "400px",
    maxLength: 15,
    text: "支持上传vido等视频格式，最多上传3个，单个视频大小不超过800MB "
  }
]);
// 自定义校验方法
const validateIntroduction = (rule, value, callback) => {
  const errorMsg = "实践点介绍不能为空";

  // 检查是否为空
  if (!value) {
    return callback(new Error(errorMsg));
  }

  // 移除HTML标签和HTML空格字符，并去除前后空格
  let cleanValue = value
    .replace(/<[^>]*>/g, "")
    .replace(/&nbsp;|&ensp;|&emsp;|&thinsp;/g, "")
    .trim();

  return cleanValue === "" ? callback(new Error(errorMsg)) : callback();
};
// 自定义校验类型
const validateEmergencyPeople = (rule, value, callback) => {
  if (!value) {
    callback(new Error("请选择类型"));
  } else {
    callback();
  }
};
// 自定义校验图片
const validateImagesFile = (rule, value, callback) => {
  if (formFile.value.images.length <= 0) {
    callback(new Error("请上传图片"));
  } else {
    callback();
  }
};
// 自定义校验一句话简介
const validateOneSentenceIntroduction = (rule, value, callback) => {
  if (!value) {
    return callback(new Error("一句话简介不能为空"));
  } else if (value.length > 100) {
    return callback(new Error("一句话简介不能超过100个字"));
  } else {
    callback();
  }
};
// 自定义校验标签
const validateTags = (rule, value, callback) => {
  if (formData.value[6].tags.length <= 0) {
    return callback(new Error("标签不能为空"));
  } else {
    callback();
  }
};

// validateFileTypeObj.required = true;
// 校验规则
const rules = ref({
  name: [
    { required: true, message: "实践点名称不能为空", trigger: "blur" }
    // { min: 2, max: 10, message: "姓名长度应在2-10个字符之间", trigger: "blur" }
  ],
  detailedAddress: [
    { required: true, message: "详细地址不能为空", trigger: "blur" }
  ],
  introduction: [
    { required: true, validator: validateIntroduction, trigger: ["blur"] }
  ],
  complexType: [{ required: true, message: "类型不能为空", trigger: "blur" }],
  emergencyPeople: [
    { required: true, validator: validateEmergencyPeople, trigger: "blur" }
  ],
  tags: [{ required: true, validator: validateTags, trigger: "blur" }],
  emergencyPhone: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error("实践点联系电话不能为空"));
          return;
        }
        if (isValidHotline(value)) {
          callback();
          return;
        }

        if (!/^1\d+$/.test(value)) {
          callback("联系电话格式不正确");
          return;
        }
        if (value.length !== 11) {
          callback("联系电话长度应为11位");
          return;
        }
        callback();
      },
      trigger: ["change", "blur"]
    }
  ],
  oneSentenceIntroduction: [
    {
      required: true,
      validator: validateOneSentenceIntroduction,
      trigger: "blur"
    }
  ],
  images: [{ required: true, validator: validateImagesFile, trigger: "blur" }],
  address: [{ required: true, message: "详细地址不能为空", trigger: "blur" }]
});

function isValidHotline(phone) {
  const cleanedPhone = phone.replace(/[-\s.]/g, "");
  const pattern = /^(?:400\d{7}|800\d{7}|95\d{3,4}|96\d{4,5}|0\d{9,11})$/;
  return pattern.test(cleanedPhone);
}

const submitForm = debounce(
  () => {
    formRef.value.validate(valid => {
      if (valid) {
        console.log("表单数据:", form.value);
        addbase();
      } else {
        console.log("表单校验失败");
      }
    });
  },
  1000,
  { immediate: true }
);
//新建实践点
const getListLoading = ref(false);
const addbase = async () => {
  if (getListLoading.value) return;
  getListLoading.value = true;
  fileData(); //文件集合处理
  let paramsData = {};
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  if (form.value.emergencyPhone) {
    paramsData.emergencyPhone = encryption(form.value.emergencyPhone);
  }
  paramsData.tags = [...formData.value[6].tags];
  // console.log("🦄-----paramsData-----", paramsData);

  const operateLog = {
    operateLogType: "COMPLEX_MANAGEMENT",
    operateType: "新增了",
    operatorTarget: `一个名为"${paramsData.name}"的实践点`
  };
  const { code, data } = await complexSave(paramsData, operateLog);
  if (code === 200) {
    ElMessage({
      type: "success",
      message: "新增成功"
    });
    if (route.query.type || route.query.periodId) {
      useCourseStore.saveBaseInfo({ id: data.id, name: data.name });
    }
    cancelForm();
  } else {
    ElMessage({
      type: "error",
      message: "新增失败"
    });
  }
  getListLoading.value = false;
};
// 取消
const cancelForm = () => {
  const currentPath = router.currentRoute.value.path;
  const isFromCopy = currentPath.includes("periodCopy");

  if (
    route.query.type === "create" ||
    route.query.type === "draft" ||
    route.query?.type === "createPeriod"
  ) {
    // 从课期创建页面进入的新建实践点
    router.replace({
      path: "/course/courseCreate",
      query: {
        type: route.query.type,
        courseId: route.query.courseId,
        create: "create",
        name: route.query.name,
        termNumber: route.query.termNumber,
        draftId: route.query.draftId
      }
    });
  } else if (route.query.type === "edite") {
    router.replace({
      path: "/course/coursePeriodEdite",
      query: {
        type: route.query.type,
        periodId: route.query.periodId,
        courseId: route.query.courseId,
        fromPage: route.query.fromPage,
        create: "create",
        name: route.query.name,
        termNumber: route.query.termNumber
      }
    });
  } else {
    router.push({
      path: "/institution/baseManage"
    });
  }
};
const dialogTableVisible = ref(false);
const locationPostion = ref([116.3912757, 39.906217]);
const selectedLocation = ref(null); // 存储用户选择的位置信息
const teacherTimeInfo = {
  location_range: 1000
};
const handleMapConfirm = data => {
  // console.log("🎉-----handleMapConfirm-----", data);
  form.value.detailedAddress = data.address;
  form.value.latitude = data.point.lat;
  form.value.longitude = data.point.lng;

  // 保存选择的位置信息，下次打开时会自动定位到这里
  selectedLocation.value = data;
};
// 自建标签
const inputPeriodVisible = ref(false);
const showPeriodInput = () => {
  inputPeriodVisible.value = true;
  // nextTick(() => {
  //   periodInputRef.value?.input?.focus();
  // });
};
const inputPeriodValue = ref("");
const tagesShow = ref(true);
const handlePeriodInputConfirm = () => {
  if (inputPeriodValue.value) {
    if (formData.value[6].tags.includes(inputPeriodValue.value)) {
      ElMessage.error("标签名称不可重复，请重新输入");
      inputPeriodVisible.value = false;
      inputPeriodValue.value = "";
      return false; // 阻止添加
    }

    if (formData.value[6].tags.length >= 19) {
      tagesShow.value = false;
    }

    if (formData.value[6].tags.length >= 20) {
      ElMessage.error("标签最多不超过20个");
      inputPeriodVisible.value = false;
      inputPeriodValue.value = "";
      return false; // 阻止添加
    }

    if (inputPeriodValue.value.length > 20) {
      ElMessage.error("输入的字数不能超过20个");
    } else {
      formData.value[6].tags.push(inputPeriodValue.value);
    }
  }
  inputPeriodVisible.value = false;
  inputPeriodValue.value = "";
};
// 清除标签
const handlePeriodClose = tag => {
  formData.value[6].tags.splice(formData.value[6].tags.indexOf(tag), 1);
  tagesShow.value = true;
};
// 文件上传限制
const handleExceed = (files, item, limit) => {
  // console.log("🍪-----files, item, limit-----", files, item, limit);
  switch (item) {
    case "images":
      return ElMessage({
        message: "当前限制上传九张图片",
        type: "error"
      });
    case "video":
      return ElMessage({
        message: "当前限制上传三个视频",
        type: "error"
      });
    default:
      break;
  }
};
// 文件上传
const beforeUpload = async (file, item) => {
  // console.log("🍧-----file, item-----", file, item);
  // 判断是否重复
  // console.log("🌵----- formFile.value[item]-----", formFile.value[item]);
  const isDuplicate = formFile.value[item].some(
    f =>
      (f.name === file.name || f.fileName === file.name) &&
      (f.size === file.size || f.totalSize === file.size)
  );
  console.log("🐳-----isDuplicate-----", isDuplicate);
  if (isDuplicate) {
    ElMessage.error("不能上传重复的文件");
    return false;
  }
  let fileType = [];
  switch (item) {
    case "video":
      fileType = ["video"];
      break;
    case "images":
      fileType = ["image"];
      break;
    default:
      break;
  }
  let strData = validateFileType(file, fileType);
  let typeImage = validateFileType(file, ["image"]);
  let isSize = isOverSizeLimit(
    file,
    item === "video" ? 800 : typeImage.valid === true ? 10 : 30
  );
  if (item === "images") {
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png"];
    const fileTypes = file.type;
    if (!allowedTypes.includes(fileTypes)) {
      strData.valid = false;
      ElMessage.error("请上传jpg、jpeg、png格式的图片");
      return false;
    }
  }

  if (strData.valid === true && isSize.valid === true) {
    try {
      let { code, data } = await uploadFile(file, () => {}, fileType);
      if (code === 200) {
        formFile.value[item].push({
          ...data,
          name: data.name || data.fileName || file.name,
          url: data.url || data.fileUrl || "",
          uid: data.uploadId || Date.now() + Math.random(),
          status: "success" // 关键
        });
        console.log(formFile.value[item], "查看文件");
      }
    } catch (error) {
      ElMessage({
        message: error.message,
        type: "error"
      });
    }
  } else {
    if (strData.message) {
      ElMessage.error(strData.message);
      return false;
    }
    if (isSize.message) {
      ElMessage.error(isSize.message);
      return false;
    }
  }
};
// 移除文件
const handleRemove = (file, item) => {
  if (item === "video") return;
  const idx = formFile.value[item].findIndex(
    i => i.url === file.url && i.uid === file.uid
  );
  if (idx !== -1) {
    formFile.value[item].splice(idx, 1);
  } else {
    formFile.value[item].splice(0, 1);
  }
};
// 预览文件
const dialogVisible = ref(false);
const dialogImageUrl = ref("");
const handlePreview = (file, prop) => {
  dialogImageUrl.value = file.url;
  dialogVisible.value = true;
};
//删除文件
const uploadKey = ref(Date.now());
const getDeleted = (item, index) => {
  formFile.value[item].splice(index, 1);
  uploadKey.value = Date.now(); // 关键：强制刷新
};
//文件处理
const fileData = () => {
  //照片
  let images = formFile.value.images;
  // 视频
  let video = formFile.value.video;
  if (images.length > 0) {
    setFilesFn(images, "PHOTO");
  }
  if (video.length > 0) {
    setFilesFn(video, "PROMOTIONAL_VIDEO");
  }
};
// 🌈thanks for AnNan!! 🎇🎇🎇🎇🎇🎇
const setFilesFn = (val, type) => {
  for (let index = 0; index < val.length; index++) {
    const element = val[index].fileIdentifier;
    // console.log("🌵-----element-----", element);
    form.value.files.push({
      fileType: type,
      fileIdentifier: element,
      sortOrder: index + 1
    });
  }
};
onUnmounted(() => {
  useCourseStore.saveCreateInfo("create");
});

const propsCascader = ref({
  lazy: true,
  // value: "name",
  async lazyLoad(node, resolve) {
    const { level, data } = node;
    try {
      let parentId = 0;

      if (level === 0) {
        parentId = 0;
      } else {
        parentId = data.value;
      }
      const { code, data: areaData } = await findArea({ parentId });
      if (
        code === 200 &&
        areaData &&
        Array.isArray(areaData) &&
        areaData.length > 0
      ) {
        // 如果是第二层或第三层，需要预先检查下一层是否有数据
        if (level === 2 || level === 3) {
          // 只调用一次接口检查下一层是否有数据
          const { code: childCode, data: childData } = await findArea({
            parentId: areaData[0].id // 使用第一个节点的ID来检查
          });

          const hasChildren =
            childCode === 200 &&
            childData &&
            Array.isArray(childData) &&
            childData.length > 0;
          // 根据检查结果设置所有节点的leaf属性
          const nodes = areaData.map(item => {
            return {
              value: item.id,
              label: item.name,
              name: item.name,
              leaf: !hasChildren // 如果下一层没有数据，所有节点都设置为叶子节点
            };
          });
          resolve(nodes);
        } else {
          // 其他层级正常处理
          const nodes = areaData.map(item => {
            return {
              value: item.id,
              label: item.name,
              name: item.name,
              leaf: false
            };
          });

          resolve(nodes);
        }
      } else {
        resolve([]);
      }
    } catch (error) {
      console.error(`获取第${level}级数据出错:`, error);
      resolve([]);
    }
  }
});
// 多级连选
const handleChange = val => {
  if (val.length <= 3) {
    form.value.province = String(val[0]) || "";
    form.value.city = "直辖市";
    form.value.district = String(val[1]) || "";
    form.value.street = String(val[2]) || "";
  } else {
    form.value.province = String(val[0]) || "";
    form.value.city = String(val[1]) || "";
    form.value.district = String(val[2]) || "";
    form.value.street = String(val[3]) || "";
  }
};
</script>

<template>
  <div class="containers">
    <div class="table_content">
      <el-form ref="formRef" :model="form" :rules="rules">
        <el-scrollbar class="scrollbar">
          <el-descriptions title="" label-width="200px" :column="1" border>
            <el-descriptions-item
              v-for="(item, index) in formData"
              :key="index"
              label-align="center"
              label-class-name="my-label"
            >
              <template #label>
                <span v-if="item.check" class="star">*</span>{{ item.label }}
              </template>
              <el-form-item
                :prop="item.prop"
                :inline-message="item.check"
                style="margin-bottom: 0"
                :show-message="true"
                error-placement="right"
              >
                <!-- input输入 -->
                <template v-if="item.type === 'input'">
                  <el-input
                    v-model.trim="form[item.prop]"
                    :placeholder="item.placeholder"
                    :style="{ width: item.width }"
                    :maxlength="item.maxLength"
                    :show-word-limit="true"
                  />
                </template>
                <!-- 富文本 -->
                <template v-else-if="item.type === 'editor'">
                  <div style="width: 100%">
                    <RichEditor v-model="form[item.prop]" height="200px" />
                  </div>
                </template>
                <!-- 地图 -->
                <template v-else-if="item.type === 'map'">
                  <div class="selsect-pos">
                    <div class="cover" @click="dialogTableVisible = true" />
                    <el-input
                      v-model="form[item.prop]"
                      class="input-part"
                      placeholder="请选择地址"
                      :style="{ width: item.width }"
                      :tabindex="-1"
                    >
                      <template #suffix>
                        <div class="pos-icon">
                          <img :src="ImgPos" class="wfull h-full" alt="">
                        </div>
                      </template>
                    </el-input>
                  </div>
                </template>
                <!-- 下拉框(单选) -->
                <template v-if="item.type === 'select'">
                  <el-select
                    v-model="form[item.prop]"
                    :placeholder="item.placeholder"
                    :style="{ width: item.width }"
                    :options="item.opt"
                  >
                    <el-option
                      v-for="items in item.opt"
                      :key="items.val"
                      :label="items.name"
                      :value="items.val"
                    />
                  </el-select>
                </template>
                <!-- 上传组件 -->
                <template v-else-if="item.type === 'upload'">
                  <div style="width: 100%">
                    <el-upload
                      :key="
                        item.prop === 'video'
                          ? uploadKey + item.prop
                          : item.prop
                      "
                      action="#"
                      :show-file-list="item.prop === 'video' ? false : true"
                      :file-list="formFile[item.prop]"
                      :http-request="() => {}"
                      :limit="item.limit"
                      :on-exceed="
                        file => handleExceed(file, item.prop, item.limit)
                      "
                      :accept="item.prop === 'video' ? 'video/*' : 'image/*'"
                      :list-type="item.type2 ? '' : 'picture-card'"
                      :before-upload="file => beforeUpload(file, item.prop)"
                      :on-remove="file => handleRemove(file, item.prop)"
                      :on-preview="file => handlePreview(file, item.prop)"
                      :class="{
                        hideUploadBtn: formFile[item.prop].length >= item.limit
                      }"
                    >
                      <template v-if="item.type2 === 'video'">
                        <img :src="uploadImg" alt="">
                      </template>
                      <template v-else>
                        <el-icon><Plus /></el-icon>
                      </template>
                    </el-upload>

                    <template v-if="item.type2">
                      <template
                        v-for="(item2, index2) in formFile[item.prop]"
                        :key="index2"
                      >
                        <FileItem
                          isNeedDelte
                          :data="item2"
                          :index="index2"
                          :isTypeVideo="true"
                          style="width: 50%; min-width: 130px"
                          @delete="getDeleted(item.prop, index2)"
                        />
                      </template>
                    </template>
                  </div>

                  <div class="upload_text">{{ item.text }}</div>
                </template>
                <!-- 标签 -->
                <template v-if="item.type === 'tag'">
                  <el-tag
                    v-for="tag in item.tags"
                    :key="tag"
                    style="margin: 0 10px 4px 0"
                    closable
                    :disable-transitions="false"
                    @close="handlePeriodClose(tag)"
                  >
                    {{ tag }}
                  </el-tag>
                  <div
                    v-if="tagesShow"
                    style="display: block; margin-top: -6px"
                  >
                    <el-input
                      v-if="inputPeriodVisible"
                      ref="periodInputRef"
                      v-model.trim="inputPeriodValue"
                      class="w-20"
                      size="small"
                      @keyup.enter="handlePeriodInputConfirm"
                      @blur="handlePeriodInputConfirm"
                    />
                    <el-button
                      v-else
                      class="button-new-tag"
                      size="small"
                      @click="showPeriodInput"
                    >
                      +
                    </el-button>
                  </div>
                </template>
                <!-- textArea -->
                <template v-if="item.type === 'textarea'">
                  <el-input
                    v-model="form[item.prop]"
                    :rows="3"
                    type="textarea"
                    :maxlength="100"
                    show-word-limit
                    :placeholder="item.placeholder"
                  />
                </template>
                <!-- 多级连选 -->
                <template v-if="item.type === 'cascader'">
                  <el-cascader
                    v-model="form[item.prop]"
                    :props="propsCascader"
                    :placeholder="item.placeholder"
                    style="width: 400px"
                    clearable
                    :show-all-levels="true"
                    :expand-trigger="'click'"
                    :check-strictly="false"
                    :multiple="false"
                    @change="handleChange"
                  />
                </template>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </el-scrollbar>

        <div class="table_bottom">
          <el-button type="default" @click="cancelForm"> 取消 </el-button>
          <el-button type="primary" @click="submitForm"> 确认新建 </el-button>
        </div>
      </el-form>
    </div>
    <Map
      v-if="dialogTableVisible"
      v-model="dialogTableVisible"
      :center="locationPostion"
      :selected-location="selectedLocation"
      :checkInResult="teacherTimeInfo"
      @confirm="handleMapConfirm"
    />
  </div>
</template>

<style lang="scss" scoped>
.scrollbar {
  // padding-top: 20px;
  // padding: 20px;
  // padding-bottom: 0px;

  height: calc(100vh - 200px);
  background-color: #fff;
}
.containers {
  box-sizing: border-box;
  width: calc(100% - 48px);
  height: 100%;
  //   padding: 24px;
  background: #f0f2f5;

  .table_top {
    box-sizing: border-box;
    padding: 20px;
    background-color: #fff;
  }

  .table_content {
    box-sizing: border-box;
    padding: 20px;
    margin-top: 20px;
    background-color: rgb(255 255 255);

    .star {
      margin-right: 3px;
      color: red;
    }

    .el-descriptions-item {
      display: flex;
      align-items: center;
    }

    .Vacode {
      margin-left: 20px;
    }

    .isQR {
      margin-right: 240px;
    }

    .codeQR {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }

  .table_bottom {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
  }
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
  //   width: 240px;
}

:deep(.my-label) {
  background: #e1f5ff !important;
}

:deep(.my-content) {
  background: var(--el-color-danger-light-9);
}
.selsect-pos {
  position: relative;
  // width: 490px;
  // height: 60px;
  // background: red;
  .cover {
    cursor: pointer;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    // background: red;
    width: 100%;
    height: 100%;
  }

  .pos-icon {
    width: 24px;
    height: 24px;
  }
}
:deep(.hideUploadBtn .el-upload--picture-card) {
  display: none;
}

.upload_text {
  display: block; // 改为块级元素
  font-size: 12px;
  color: #8c939d;
  white-space: pre-line;
  word-wrap: break-word;
  width: 100%;
}
</style>
