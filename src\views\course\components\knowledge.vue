<script setup>
import { onMounted, ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { requestTo } from "@/utils/http/tool";
import { nowledgefindById, nowledgeCreateOrUpdate } from "@/api/period.js";
import { draftCourseKnowledgePointFindByDraftId } from "@/api/drafts.js";
const props = defineProps({
  infoShow: {
    type: String,
    default: "课期介绍"
  },
  btnText: {
    type: String,
    default: "编辑介绍"
  },
  showContent: {
    type: String,
    default: ""
  },
  draftId: {
    type: Number,
    default: 0
  }
});
const router = useRouter();
const route = useRoute();

// const refName = ref("行程安排");
// 编辑内容按钮
const editeEvt = () => {
  router.push({
    path: "/course/currentDetails/introductionEdite",
    query: {
      periodId: route.query.periodId,
      infoShow: props.infoShow,
      courseId: route.query.courseId
    }
  });
};

onMounted(() => {
  if (props.draftId) {
    getTableListDraft();
  } else {
    getTableList();
  }
});
// 表格数据
const tableData = ref([]);
const getApiType = isEdit => {
  let res = isEdit ? nowledgeCreateOrUpdate : nowledgefindById;
  return res;
};
// 获取列表信息
const getTableList = async data => {
  const params = {
    coursePeriodId: Number(route.query.periodId)
  };
  let api = getApiType(false);
  let [err, res] = await requestTo(api(params));
  if (res) {
    tableData.value = res;
  } else {
    console.log(err);
  }
};
// 获取列表信息(草稿箱)
const getTableListDraft = async data => {
  const params = {
    draftId: Number(props.draftId)
  };
  let [err, res] = await requestTo(
    draftCourseKnowledgePointFindByDraftId(params)
  );
  if (res) {
    tableData.value = res;
  } else {
    console.log(err);
  }
};
</script>

<template>
  <div class="course-introduction">
    <div class="con_table">
      <el-table
        :data="tableData"
        table-layout="fixed"
        :header-cell-style="{ backgroundColor: '#fafafa', color: '#565353' }"
        highlight-current-row
        height="100%"
        width="100%"
        :style="{ flex: 1, minHeight: 0 }"
      >
        <el-table-column
          prop="stage"
          label="学段"
          align="left"
          fixed
          width="100"
        >
          <template #default="scope">
            {{ scope.row.stage?.name || "--" }}
          </template>
        </el-table-column>

        <el-table-column width="100px" prop="subject" label="学科">
          <template #default="scope">
            {{ scope.row.subject?.name || "--" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="switch"
          label="教材关联"
          align="left"
          width="300"
        >
          <template #default="scope">
            <span
              v-if="scope.row.textbook?.name && scope.row.textbook?.name"
              class="no-wrap-text"
              >{{
                `${scope.row.textbook?.name} > ${scope.row.catalog?.name}`
              }}</span>
            <span v-else class="no-wrap-text">--</span>
          </template>
        </el-table-column>
        <el-table-column width="200px" prop="knowledge" label="核心知识点">
          <template #default="scope">
            <div>
              {{ scope.row.knowledge?.name || "--" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="content"
          label="预期目标"
          align="left"
          min-width="350"
        >
          <template #default="scope">
            <!-- <span class="no-wrap-text">{{ (scope.row.content) || "--" }}</span> -->
            <div v-html="scope.row.content || '--'" />
          </template>
        </el-table-column>
        <el-table-column
          prop="abilities"
          label="能力提升"
          align="left"
          min-width="350"
        >
          <template #default="scope">
            <span v-if="scope.row.abilities.length > 0" class="no-wrap-text">{{
              scope.row.abilities.map(i => i.abilityDict.name).join("，")
            }}</span>
            <span v-else class="no-wrap-text">--</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-button v-if="false" type="primary" class="editeBtn" @click="editeEvt">
      {{ btnText }}
    </el-button>
  </div>
</template>

<style lang="scss" scoped>
.course-introduction {
  height: 100%;
  display: flex;
  flex-direction: column;
  .editeBtn {
    display: flex;
    position: absolute;
    bottom: 0;
    left: 0;
  }
  .content {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    height: calc(100% - 52px);
    background: #f5f7fa;
    overflow-y: auto;
  }
}
.con_table {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 450px;
  width: 100%;
  margin: 10px 0 12px 0;
}
</style>
