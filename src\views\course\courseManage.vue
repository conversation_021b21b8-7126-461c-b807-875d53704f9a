<script setup>
import {
  onMounted,
  ref,
  onActivated,
  nextTick,
  onUnmounted,
  onDeactivated
} from "vue";
import { formatTime } from "@/utils/index";
import { courseFindAll, courseTypeFind, courseDelete } from "@/api/course.js";
import { draftGetCount } from "@/api/drafts.js";
import { findAllCourseType } from "@/api/period.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { to } from "@iceywu/utils";
defineOptions({
  name: "CourseManageIndex"
});
onMounted(() => {
  draftGetCountApi();
  courseTypeFindApi();
  getTableList();
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});
onActivated(() => {
  draftGetCountApi();
  // getTableList();
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});
onUnmounted(() => {
  window.removeEventListener("resize", calculateTableHeight);
});

onDeactivated(() => {
  window.removeEventListener("resize", calculateTableHeight);
});
// 计算表格高度
const searchFormHeight = ref(0);
const tableHeight = ref("89vh");

const calculateTableHeight = async () => {
  await nextTick();
  const searchForm = document.querySelector(".search .con_search");
  if (searchForm) {
    searchFormHeight.value = searchForm.offsetHeight;
    // 动态计算表格高度，减去搜索框高度、表头按钮高度、分页器高度和其他间距
    tableHeight.value = `calc(88vh - 140px - ${searchFormHeight.value}px - 46px)`;
  }
};
const router = useRouter();
// 搜索表单
const form = ref({
  startTime: "",
  endTime: "",
  name: "",
  organizationName: "",
  courseTypeId: ["all"],
  freeze: 0
});
// 表格数据
const tableData = ref([]);
const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});
const courseTypeoptions = ref([
  {
    label: "全部",
    value: "all"
  }
]);
// 课程状态选项
const stateOptions = ref([
  {
    name: "全部",
    id: 0
  },
  {
    name: "正常",
    id: 1
  },
  {
    name: "冻结",
    id: 2
  }
]);
// 选项卡
const activeName = ref([
  {
    name: "本机构课程",
    id: 0
  },
  {
    name: "专家合作课程",
    id: 1
  }
]);
// 选项卡切换
const handleClick = e => {
  if (e.props.label === "本机构课程") {
    params.value.page = 1;
    getTableList();
  } else {
    params.value.page = 1;
    expertCollaboration();
  }
};
const courseTypeFindApi = async () => {
  let [err, res] = await requestTo(findAllCourseType());
  // console.log("🐠res-----------------3333------------->", res);
  courseTypeoptions.value = courseTypeoptions.value.concat(transformArray(res));
  // let res1 = res?.map(it => {
  //   return {
  //     ...it,
  //     label: it.name,
  //     value: it.id
  //   };
  // });
  // courseTypeoptions.value = courseTypeoptions.value.concat(res1);
};
function transformArray(inputArray) {
  return inputArray.map(item => {
    const newItem = {
      value: item.id,
      label: item.name
    };

    // 如果有 children，则递归转换
    if (item.children && item.children.length > 0) {
      newItem.children = transformArray(item.children);
    }

    return newItem;
  });
}
// 获取本机构列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  if (paramsData.courseTypeId && paramsData.courseTypeId[0] === "all") {
    delete paramsData.courseTypeId;
  }
  if (paramsData.freeze && form.value.freeze === 1) {
    paramsData.freeze = false;
  } else if (paramsData.freeze && form.value.freeze === 2) {
    paramsData.freeze = true;
  }
  // console.log("🍧-----paramsData-----", paramsData);
  const [err, result] = await requestTo(courseFindAll(paramsData));
  // console.log("🎁-----result-----", result);
  if (result) {
    tableData.value = result?.content;

    params.value.totalElements = result.totalElements;
  } else {
    ElMessage.error(err);
  }
  getListLoading.value = false;
};
// 获取专家合作课程
const expertCollaboration = async () => {
  params.value.totalElements = 0;
  tableData.value = [
    {
      id: 133,
      createdAt: 1750994561837,
      updatedAt: 1751957135402,
      name: "陶瓷文化",
      organizationName: "阳光教育（劳动教育基地）",
      courseTypeName: "行走思政",
      termNumber: 12,
      onlineTermNumber: 2,
      freeze: false
    }
  ];
};
const handleSizeChange = val => {
  params.value.size = val;
  getTableList();
};
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTableList();
};
// 查看详情
const getId = id => {
  router.push({ path: "/course/courseDetails", query: { id } });
}; //搜索
const searchData = () => {
  params.value.page = 1;
  getTableList();
};
// 重置
const setData = () => {
  form.value = {};
  form.value.courseTypeId = ["all"];
  form.value.freeze = 0;
  params.value.page = 1;
  value1.value = [];
  getTableList();
};
// 清除数据
const clearEvt = val => {
  if (val === "name") {
    form.value.name = "";
  } else if (val === "time") {
    form.value.startTime = "";
    form.value.endTime = "";
  } else if (val === "courseTypeId") {
    form.value.courseTypeId = ["all"];
  }
  // params.value.page = 1;
  getTableList();
};
const courseTypeChange = val => {
  form.value.courseTypeId = val[val.length - 1];
};
// 选择时间
const timeChange = value => {
  if (value) {
    form.value.startTime = new Date(value[0])?.getTime();
    const endDate = new Date(value[1]);
    endDate.setHours(23, 59, 59, 999); // 关键修改
    form.value.endTime = endDate.getTime();
  }
};

const value1 = ref([]);
// 前往创建
const goSet = () => {
  router.push({
    path: "/course/courseCreate",
    query: {
      type: "create"
    }
  });
};
// 删除
const deleteEvt = val => {
  ElMessageBox.confirm(`确定要删除该课程吗？`, "删除课程", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      const operateLog = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `删除了“${val.name}”课程`
        // operatorTarget: form.value.name,
      };
      let [err, res] = await to(courseDelete({ id: val.id }, operateLog));
      if (res.code === 200) {
        ElMessage.success("删除成功");
        getTableList();
      } else {
        ElMessage.error(`删除失败,${res.msg}`);
      }
      if (err) {
        ElMessage.error("删除失败");
      }
    })
    .catch(() => {});
};
const draftsNum = ref(0);

// 查询草稿数量
const draftGetCountApi = async () => {
  let [err, res] = await to(draftGetCount());
  if (res.code === 200) {
    draftsNum.value = res?.data?.draftCount || 0;
    // console.log("🍭-----res-----", res.data.draftCount);
  } else {
    console.log("🎁-----err-----", err);
  }
};
const getCourseStatus = freeze => {
  return freeze === true ? "冻结" : "正常";
};

const getCourseColor = freeze => {
  return freeze === true ? "#f56c6c" : "";
};
</script>

<template>
  <div class="containers">
    <div class="search">
      <div class="con_search">
        <el-form :model="form" :inline="true">
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="value1"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              @change="timeChange"
              @clear="clearEvt('time')"
            />
          </el-form-item>
          <el-form-item label="课程名">
            <el-input
              v-model.trim="form.name"
              placeholder="请输入"
              clearable
              @clear="clearEvt('name')"
            />
          </el-form-item>
          <el-form-item label="课程类型">
            <el-cascader
              v-model="form.courseTypeId"
              :options="courseTypeoptions"
              :show-all-levels="false"
              @change="courseTypeChange"
              @clear="clearEvt('courseTypeId')"
            />
          </el-form-item>
          <el-form-item label="课程状态">
            <el-select
              v-model="form.freeze"
              style="width: 120px"
              placeholder="请选择"
              value-key="id"
            >
              <el-option
                v-for="item in stateOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label=" ">
            <div class="flex">
              <el-button type="primary" @click="searchData">搜索</el-button>
              <el-button @click="setData">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="main">
      <div class="btns">
        <!-- <div class="titles">课程管理</div> -->
        <el-button
          type="primary"
          style="padding: 8px 5px 8px 12px"
          @click="router.push('/course/drafts')"
        >
          草稿箱{{ `（${draftsNum}）` }}
        </el-button>
        <el-button type="primary" @click="goSet">新建课程</el-button>
      </div>
      <!-- <el-tabs class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane
          v-for="item in activeName"
          :key="item.id"
          :label="item.name"
        >

        </el-tab-pane>
      </el-tabs> -->
      <el-scrollbar class="scrollbar" :style="{ height: tableHeight }">
        <div class="con_table">
          <el-table
            :data="tableData"
            :header-cell-style="{
              backgroundColor: '#fafafa',
              color: '#565353'
            }"
            table-layout="fixed"
            :max-height="tableHeight"
          >
            <el-table-column
              prop="name"
              label="课程名"
              width="300"
              show-overflow-tooltip
              fixed
            >
              <template #default="scope">
                <!-- <el-text class="w-300px mb-2" truncated>
                  {{ scope.row.name || "--" }}
                </el-text> -->
                {{ scope.row.name || "--" }}
              </template>
            </el-table-column>
            <el-table-column
              prop="courseTypeName"
              label="课程类型"
              align="left"
              min-width="250"
            >
              <template #default="scope">
                <div>
                  {{ scope.row.courseTypeName || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              min-width="250"
              prop="createdAt"
              label="创建时间"
              align="left"
            >
              <template #default="scope">
                <div>
                  {{
                    formatTime(scope.row?.createdAt, "YYYY-MM-DD HH:mm:ss") ||
                    "--"
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="termNumber"
              label="总期数"
              align="left"
              min-width="100"
            >
              <template #default="scope">
                <div>
                  {{ scope.row.termNumber || "0" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="termNumber"
              label="上架期数"
              align="left"
              min-width="100"
            >
              <template #default="scope">
                <div>
                  {{ scope.row.onlineTermNumber || "0" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="freeze"
              label="课程状态"
              align="left"
              min-width="120"
            >
              <template #default="scope">
                <div :style="{ color: getCourseColor(scope.row.freeze) }">
                  {{ getCourseStatus(scope.row.freeze) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="left"
              width="300"
            >
              <template #default="scope">
                <div class="option">
                  <div class="btnse" @click="getId(scope.row.id)">查看详情</div>
                  <div
                    class="btnse"
                    @click="
                      router.push({
                        path: '/course/courseCreate',
                        query: {
                          type: 'createPeriod',
                          courseId: scope.row.id
                        }
                      })
                    "
                  >
                    新建课期
                  </div>
                  <div
                    class="btnse"
                    @click="
                      router.push({
                        path: '/course/allEvaluate',
                        query: { courseId: scope.row.id }
                      })
                    "
                  >
                    用户评价
                  </div>
                  <!-- <div
                        v-if="item.id === 1"
                        class="btnse"
                        @click="
                          router.push({
                            path: '/course/professionalTeacher',
                            query: { courseId: scope.row.id }
                          })
                        "
                      >
                        专家评价
                      </div> -->
                  <div
                    class="btnse"
                    style="color: #f56c6c"
                    @click="deleteEvt(scope.row)"
                  >
                    删除
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-scrollbar>
      <div class="con_pagination">
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="params.page"
          v-model:page-size="params.size"
          class="pagination"
          background
          :page-sizes="[15, 30, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="params.totalElements"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.scrollbar {
  // padding-top: 20px;
  // padding: 20px;
  // height: calc(100vh - 181px);
  // height: calc(100vh - 335px);
  background-color: #fff;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
}
// :deep(.el-table .cell) {
//   padding: 0;
// }
.containers {
  box-sizing: border-box;
  height: 88vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .search {
    padding: 20px 20px 2px 20px;
    background-color: #fff;
    margin-bottom: 20px;
    .con_search {
      display: flex;
      align-items: center;
      width: 100%;
      height: fit-content;
      overflow-y: auto;
      // .input_width {
      //   width: 200px;
      // }
    }
  }

  .main {
    background: #fff;
    padding: 20px;
    box-sizing: border-box;
    // height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: hidden;
    .btns {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      margin-bottom: 20px;
      // .draft{
      //   :deep(.el-button .el-button.is-round){
      //     padding: 8px 0 8px 10px !important;
      //   }
      // }
    }
  }
  .con_table {
    flex: 1;
    display: flex;
    flex-direction: column;
    .option {
      display: flex;
      .btnse {
        color: #409eff;
        cursor: pointer;
        margin-right: 20px;
      }
    }
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    // margin-top: 20px;
  }
}

:deep(.el-tabs__nav-wrap::after) {
  display: none !important;
}
</style>
