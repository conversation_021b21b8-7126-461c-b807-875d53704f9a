// import { getLogin, refreshToken<PERSON>pi } from "@/api/admin";
import { refreshTokenApi, getLogin, getLoginById } from "@/api/user";
// import { accountLogin, educationLogin } from "@/api/brUser";
import { accountLogin } from "@/api/brUser";
import { operateLogSave } from "@/api/institution";
import { saveLoginLog } from "@/utils/saveLog";
import {
  AccessTokenKey,
  ExpiresKey,
  RefreshTokenKey,
  removeToken,
  setToken,
  userKey
} from "@/utils/auth";
// import { requestTo } from "@/utils/http/tool";
import { defineStore } from "pinia";
import {
  resetRouter,
  router,
  routerArrays,
  storageLocal,
  store
} from "../utils";
import { useMultiTagsStoreHook } from "./multiTags";
import { requestTo } from "@/utils/http/tool";
import { getObjVal } from "@iceywu/utils";

export const useUserStore = defineStore({
  id: "pure-user",
  state: () => ({
    // 头像
    avatar: storageLocal().getItem(userKey)?.avatar ?? "",
    // 用户名
    username: storageLocal().getItem(userKey)?.username ?? "",
    // 昵称
    nickname: storageLocal().getItem(userKey)?.nickname ?? "",
    // 机构
    organizationName: storageLocal().getItem(userKey)?.organizationName ?? "",
    // 页面级别权限
    roles: storageLocal().getItem(userKey)?.roles ?? [],
    // 按钮级别权限
    permissions: storageLocal().getItem(userKey)?.permissions ?? [],
    // authorityDTOS
    authorityDTOS: storageLocal().getItem(userKey)?.authorityDTOS ?? [],
    // 前端生成的验证码（按实际需求替换）
    verifyCode: "",
    // 判断登录页面显示哪个组件（0：登录（默认）、1：手机登录、2：二维码登录、3：注册、4：忘记密码）
    currentPage: 0,
    // 是否勾选了登录页的免登录
    isRemembered: false,
    // 登录页的免登录存储几天，默认7天
    loginDay: 7,
    // 局端平台角色
    roleTarget: "",
    userInfoData: {}
  }),
  actions: {
    /** 存储头像 */
    SET_AVATAR(avatar) {
      this.avatar = avatar;
    },
    /** 存储用户名 */
    SET_USERNAME(username) {
      this.username = username;
    },
    /** 存储机构名 */
    SET_ORGANIZATIONNAME(organizationName) {
      this.organizationName = organizationName;
    },
    /** 存储昵称 */
    SET_NICKNAME(nickname) {
      this.nickname = nickname;
    },
    /** 存储角色 */
    SET_ROLES(roles) {
      this.roles = roles;
    },
    /** 存储按钮级别权限 */
    SET_PERMS(permissions) {
      this.permissions = permissions;
    },
    /** 存储authorityDTOS */
    SET_AUTHORITYDTOS(authorityDTOS) {
      this.authorityDTOS = authorityDTOS;
      // storageLocal().setItem(userKey, {
      //   authorityDTOS
      // });
      localStorage.setItem(
        "authorityDTOS",
        JSON.stringify({
          authorityDTOS
        })
      );
    },
    /** 存储前端生成的验证码 */
    SET_VERIFYCODE(verifyCode) {
      this.verifyCode = verifyCode;
    },
    /** 存储登录页面显示哪个组件 */
    SET_CURRENTPAGE(value) {
      this.currentPage = value;
    },
    /** 存储是否勾选了登录页的免登录 */
    SET_ISREMEMBERED(bool) {
      this.isRemembered = bool;
    },
    /** 设置登录页的免登录存储几天 */
    SET_LOGINDAY(value) {
      this.loginDay = Number(value);
    },
    // 储存登录角色
    SET_ROLETARGET(value) {
      this.roleTarget = value;
    },
    /** 登入 */
    async loginByUsername(data) {
      return new Promise(async (resolve, reject) => {
        //  const auth_data = {
        //   avatar: "https://avatars.githubusercontent.com/u/********",
        //   username: "common",
        //   nickname: "小林",
        //   roles: ["common"],
        //   permissions: ["permission:btn:add", "permission:btn:edit"],
        //   accessToken: "eyJhbGciOiJIUzUxMiJ9.common",
        //   refreshToken: "eyJhbGciOiJIUzUxMiJ9.commonRefresh",
        //   expires: "2030/10/30 00:00:00"
        // };
        // setToken(auth_data);
        // resolve(data);
        // accountLogin(data)
        //   .then(data => {

        //     // if (data?.success) setToken(data.data);
        //     const auth_data = {
        //       avatar: "https://avatars.githubusercontent.com/u/********",
        //       username: "common",
        //       nickname: "小林",
        //       roles: ["common"],
        //       permissions: ["permission:btn:add", "permission:btn:edit"],
        //       accessToken: "eyJhbGciOiJIUzUxMiJ9.common",
        //       refreshToken: "eyJhbGciOiJIUzUxMiJ9.commonRefresh",
        //       expires: "2030/10/30 00:00:00"
        //     };
        //     setToken(auth_data);
        //     resolve(data);
        //   })
        //   .catch(error => {
        //     reject(error);
        //   });
        // let res =
        //   this.roleTarget === "平台管理员"
        //     ? accountLogin(data)
        //     : educationLogin(data);

        const [err, result] = await requestTo(accountLogin(data));

        if (err) {
          reject(err);
        }
        if (result) {
          const { user, token } = result;
          this.userInfoData = user;
          localStorage.setItem("userInfoData", JSON.stringify(user));
          localStorage.setItem(
            "institution-name",
            result.user.organizationName
          );
          const authorityDTOS = getObjVal(user, "authorities", []);
          this.SET_AUTHORITYDTOS(authorityDTOS);
          this.SET_ORGANIZATIONNAME(result.user.organizationName);
          const TokenInfo = {
            username: user.name,
            roles: ["admin"],
            accessToken: token[AccessTokenKey],
            refreshToken: token[RefreshTokenKey],
            expires: token[ExpiresKey] * 1000,
            roleTarget: this.roleTarget,
            organizationName: result.user.organizationName
          };
          setToken(TokenInfo);

          saveLoginLog(user.name);

          resolve(result);
        }
      });
    },

    // 根据id登录
    async loginById(data, description) {
      return new Promise(async (resolve, reject) => {
        const [err, result] = await requestTo(getLoginById(data));

        if (err) {
          reject(err);
        }
        if (result) {
          const { user, token } = result;
          this.userInfoData = user;
          localStorage.setItem("userInfoData", JSON.stringify(user));
          localStorage.setItem(
            "institution-name",
            result.user.organizationName
          );
          const authorityDTOS = getObjVal(user, "authorities", []);
          this.SET_AUTHORITYDTOS(authorityDTOS);
          this.SET_ORGANIZATIONNAME(result.user.organizationName);
          const TokenInfo = {
            username: user.name,
            roles: ["admin"],
            accessToken: token[AccessTokenKey],
            refreshToken: token[RefreshTokenKey],
            expires: token[ExpiresKey] * 1000,
            roleTarget: this.roleTarget,
            organizationName: result.user.organizationName
          };
          setToken(TokenInfo);

          saveLoginLog(`${user.name}使用${description}`);

          resolve(result);
        }
      });
    },

    /** 前端登出（不调用接口） */
    logOut() {
      this.username = "";
      this.roles = [];
      this.permissions = [];
      localStorage.removeItem("userInfoData");
      localStorage.removeItem("institution-name");
      removeToken();
      useMultiTagsStoreHook().handleTags("equal", [...routerArrays]);
      resetRouter();
      router.push("/login");
    },
    /** 刷新`token` */
    async handRefreshToken(data) {
      return new Promise((resolve, reject) => {
        refreshTokenApi(data)
          .then(res => {
            if (res) {
              setToken(res.data);
              resolve(data);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    }
  }
});

export function useUserStoreHook() {
  return useUserStore(store);
}
