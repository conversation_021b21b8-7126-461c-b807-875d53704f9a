<script setup>
import { ref, reactive, onMounted, onActivated } from "vue";
import { formatTime } from "@/utils/index";
import dayjs from "dayjs";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox, genFileId, ElUpload } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import {
  findByOrganization,
  deleteOrganizationTeacher
} from "@/api/teachers/institutionalFaculty.js";
import {
  teacherDataFindAll,
  teachersIsResident,
  findModelChildrenByParentId
} from "@/api/teachers/teacherResourcePool.js";
import { decrypt } from "@/utils/SM4.js";
import { View, Hide, WarningFilled, Loading } from "@element-plus/icons-vue";
import { downloadFile, to } from "@iceywu/utils";
import { genderOptions, residentOptions } from "./utils/options.js";
import { EDUCATION_LEVEL, RESIDENT_STATUS } from "@/utils/enum.js";
import { getFindModelChildren } from "./utils/dataHttpFanction.js";
defineOptions({
  name: "InstitutionalFacultyIndex"
});
onActivated(() => {
  onSearch();
});
const router = useRouter();
const route = useRoute();

const loadingTable = ref(false);
const educationOptions = ref([]); //学历选项
const majorOptions = ref([]); //专业选项

const defaultTime = [
  new Date(2024, 4, 1, 0, 0, 0),
  new Date(2024, 4, 1, 23, 59, 59)
];
const dataList = ref([]);
const form = reactive({
  name: "",
  gender: "",
  educationLevel: "",
  major: "",
  residentStatus: ""
});

const pagination = {
  total: 0,
  pageSize: 15,
  currentPage: 1,
  background: true,
  pageSizes: [15, 30, 50, 100]
};

const columns = [
  {
    label: "教师姓名",
    prop: "name",
    minWidth: 90,
    formatter: ({ name }) => {
      return name || "--";
    }
  },
  {
    label: "性别",
    prop: "gender",
    minWidth: 90,
    formatter: ({ gender }) => {
      if (gender === 1) return "男";
      else if (gender === 2) return "女";
      else if (gender === 0) return "未知";
      else return "--";
    }
  },
  {
    label: "联系方式",
    prop: "phone",
    minWidth: 180,
    slot: "phone"
  },
  {
    label: "学历",
    prop: "educationLevel",
    minWidth: 90,
    formatter: ({ educationLevel }) => {
      return educationLevel || "--";
    }
  },
  {
    label: "专业",
    prop: "major",
    minWidth: 90,
    formatter: ({ major }) => {
      return major || "--";
    }
  },
  {
    label: "创建时间",
    minWidth: 180,
    prop: "createdAt",
    formatter: ({ createdAt }) => {
      return createdAt ? dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss") : "--";
    }
  },
  {
    label: "常驻状态",
    prop: "residentStatus",
    width: 100,
    slot: "state"
  },
  {
    label: "操作",
    fixed: "right",
    width: 340,
    slot: "operation"
  }
];
const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc"
});
// 列表 api
const onSearch = async val => {
  const paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort
  };
  for (const paramsDataKey in form) {
    let isArray = Array.isArray(form[paramsDataKey]);
    if (isArray) {
      if (form[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form[paramsDataKey];
      }
    } else {
      if (paramsDataKey === "freeze") {
        if (form[paramsDataKey] !== "all") {
          paramsData[paramsDataKey] = form[paramsDataKey];
        }
      } else if (form[paramsDataKey]) {
        paramsData[paramsDataKey] = form[paramsDataKey];
      }
    }
  }
  const [err, res] = await requestTo(findByOrganization(paramsData));
  // const [err, res] = await requestTo(teacherDataFindAll(paramsData));
  if (res) {
    // 初始化电话号码显示状态
    res.content.forEach(item => {
      item.type_phone = false;
    });
    dataList.value = res.content;
    pagination.total = res.totalElements;
  }
  if (err) {
    console.error(err);
  }
};

// 搜索
const searchData = () => {
  params.value.page = 1;
  onSearch();
};

// 重置
const setData = () => {
  form.name = "";
  form.gender = "";
  form.educationLevel = "";
  form.major = "";
  params.value.page = 1;
  onSearch();
};

// 每页多少条
const handleSizeChange = val => {
  pagination.pageSize = val;
  params.value.size = val;
  onSearch();
};
// 前往页数
const handleCurrentChange = val => {
  pagination.currentPage = val;
  params.value.page = val;
  onSearch();
};

// 切换手机号码显示状态
const eye_phone = (id, phoneCt) => {
  const item = dataList.value.find(item => item.id === id);
  if (item) {
    item.type_phone = !item.type_phone;
  }
};
const inviteResident = (item, type) => {
  // console.log("💗inviteResident---------->");
  const title = type === "end" ? "结束常驻" : "邀请常驻";
  let inviteText =
    type === "end"
      ? "确定结束该专家在机构中常驻么？确认后，该专家将不在机构中常驻"
      : "确定邀请该专家常驻么？同意后创建课程则可以直接选择该老师";
  ElMessageBox.confirm(`${inviteText}`, `${title}`, {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(() => {
      inviteApi(item, type);
    })
    .catch(() => {});
};
const inviteApi = async (row, type) => {
  const params = {
    teacherDatabaseId: +row.id,
    // resident: type === "end" ? false : true
    resident: true
  };
  const operateLog = {
    operateLogType: "TEACHER_DATABASE",
    operateType:
      type === "end"
        ? `结束了“${row.name}”专家常驻`
        : `邀请了“${row.name}”专家常驻`
  };

  const { code } = await teachersIsResident(params, operateLog);
  if (code === 200) {
    ElMessage({
      type: "success",
      message: type === "end" ? "结束常驻成功" : "邀请常驻成功"
    });
    onSearch();
  } else {
    ElMessage({
      type: "error",
      message: type === "end" ? "结束常驻失败" : "邀请常驻失败"
    });
  }
};
// 删除
const deleteEvt = val => {
  ElMessageBox.confirm(
    `删除该师资后，其将不再于机构师资展示列表中出现，确认删除么？`,
    "删除师资",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    }
  )
    .then(async () => {
      const operateLog = {
        operateLogType: "TEACHER_DATABASE",
        operateType: `删除了“${val.name}”师资`
        // operatorTarget: form.value.name,
      };
      let [err, res] = await to(
        deleteOrganizationTeacher({ id: val.id }, operateLog)
      );
      if (res.code === 200) {
        ElMessage.success("删除成功");
        onSearch();
      } else {
        ElMessage.error(`删除失败,${res.msg}`);
      }
      if (err) {
        ElMessage.error("删除失败");
      }
    })
    .catch(() => {});
};
onMounted(async () => {
  onSearch();
  educationOptions.value = await getFindModelChildren("453"); //学历
  majorOptions.value = await getFindModelChildren("548"); //专业
});
</script>

<template>
  <div>
    <div class="common">
      <div class="search">
        <div class="search-form">
          <el-form :inline="true" :model="form" class="demo-form-inline">
            <el-form-item label="姓名">
              <el-input
                v-model.trim="form.name"
                placeholder="请输入姓名"
                clearable
                style="width: 180px"
              />
            </el-form-item>
            <el-form-item label="性别">
              <el-select
                v-model="form.gender"
                style="width: 180px"
                placeholder="请选择性别"
                clearable
                value-key="id"
              >
                <el-option
                  v-for="item in genderOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="学历">
              <el-cascader
                v-model="form.educationLevel"
                :options="educationOptions"
                :props="{
                  expandTrigger: 'hover',
                  value: 'name',
                  label: 'name',
                  children: 'children',
                  emitPath: false
                }"
                style="width: 200px"
                placeholder="请选择学历"
                clearable
                :show-all-levels="false"
              />
            </el-form-item>
            <el-form-item label="专业">
              <el-cascader
                v-model="form.major"
                :options="majorOptions"
                :props="{
                  expandTrigger: 'hover',
                  value: 'name',
                  label: 'name',
                  children: 'children',
                  emitPath: false
                }"
                style="width: 200px"
                placeholder="请选择专业"
                clearable
                :show-all-levels="false"
              />
            </el-form-item>
            <el-form-item label="常驻状态">
              <el-select
                v-model="form.residentStatus"
                placeholder="请选择"
                style="width: 180px"
                clearable
              >
                <el-option
                  v-for="item in residentOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div class="button">
          <el-button type="primary" @click="searchData"> 搜索 </el-button>
          <el-button @click="setData">重置</el-button>
        </div>
      </div>
    </div>

    <div class="common">
      <div class="puretable">
        <pure-table
          ref="tableRef"
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="left"
          table-layout="auto"
          :loading="loadingTable"
          :data="dataList"
          :columns="columns"
          :pagination="{ ...pagination }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #phone="{ row }">
            <div class="phone-container">
              {{
                row.phone
                  ? row.type_phone
                    ? decrypt(row.phoneCt)
                    : row.phone
                  : "--"
              }}
              <div
                v-if="row.phone"
                class="eye-icon"
                @click="eye_phone(row.id, row.phoneCt)"
              >
                <el-icon v-if="!row.type_phone">
                  <Hide />
                </el-icon>
                <el-icon v-else>
                  <View />
                </el-icon>
              </div>
            </div>
          </template>
          <template #state="{ row }">
            <div
              :style="{
                color: row.residentStatus === 'REJECTED' ? 'red' : ''
              }"
            >
              {{ RESIDENT_STATUS[row.residentStatus]?.label || "--" }}
            </div>
          </template>
          <template #operation="{ row }">
            <el-button
              type="primary"
              link
              @click="
                router.push({
                  path: '/institution/details',
                  query: { id: row.teacherId, type: 'institutionalFaculty' }
                })
              "
            >
              详情
            </el-button>
            <el-button
              type="primary"
              link
              @click="
                router.push({
                  path: '/institution/expert/evaluation',
                  query: {
                    id: row.teacherId
                  }
                })
              "
            >
              专家评价
            </el-button>
            <el-button
              type="primary"
              link
              @click="
                router.push({
                  path: '/teacher/institution/join/course',
                  query: { teacherId: row.teacherId }
                })
              "
            >
              参与课程
            </el-button>
            <el-button
              v-if="
                row.residentStatus === 'NON_RESIDENT' ||
                row.residentStatus === 'REJECTED'
              "
              type="primary"
              link
              @click="inviteResident(row, 'invite')"
            >
              邀请常驻
            </el-button>
            <el-button
              v-if="row.residentStatus === 'RESIDENT'"
              link
              type="danger"
              @click="inviteResident(row, 'end')"
            >
              结束常驻
            </el-button>
            <el-button type="danger" link @click="deleteEvt(row)">
              删除
            </el-button>
          </template>
        </pure-table>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.common {
  padding: 20px 20px 2px 20px;
  background-color: #fff;
  margin-bottom: 20px;

  .con_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: fit-content;
    margin-bottom: 24px;

    .titles {
      font-size: 20px;
      font-weight: bold;
      color: #606266;
    }
  }

  .search {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    .search-form {
      flex: 1;
    }
    .button {
      display: flex;
      justify-content: flex-end;
      margin-left: 20px;
      flex-wrap: wrap;
    }
  }
}

.buttom {
  display: flex;
  justify-content: end;
}

.upload-demo {
  margin-bottom: 10px;
}

.template-buttons {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.phone-container {
  display: flex;
  align-items: center;

  .eye-icon {
    margin-left: 10px;
    display: flex;
    align-items: center;
    cursor: pointer;
    &:hover {
      color: #409eff;
    }
  }
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
