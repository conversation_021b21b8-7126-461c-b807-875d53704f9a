<script setup>
import {
  ref,
  onMounted,
  nextTick,
  reactive,
  onBeforeMount,
  onBeforeUnmount
} from "vue";
import { useRouter, useRoute } from "vue-router";
import RichEditor from "@/components/Base/RichEditor.vue";
import { complexUpdate, complexFindById, findArea } from "@/api/institution";
import { ElMessage } from "element-plus";
import { formatTime } from "@/utils/index";
import { decrypt, encryption } from "@/utils/SM4.js";
import { Hide, View, Plus } from "@element-plus/icons-vue";
import { compareObjects, debounce } from "@iceywu/utils";
import Map from "./components/Map.vue";
import ImgPos from "@/assets/pos.png";
import FileItem from "@/components/PreviewV2/FileItem.vue";
import uploadImg from "@/assets/login/upload1.png";
import {
  uploadFile,
  validateFileType,
  isOverSizeLimit
} from "@/utils/upload/upload";

const router = useRouter();
const route = useRoute();
const form = ref({
  id: "",
  name: "",
  detailedAddress: "",
  emergencyPeople: "",
  emergencyPhone: "",
  introduction: "",
  createdAt: "",
  updatedAt: "",
  state: "",
  latitude: "",
  longitude: "",
  ags: [],
  complexType: "", //类型
  oneSentenceIntroduction: "", //一句话简介
  files: [], //文件上传
  province: "", //省份
  city: "", //市
  district: "", //区
  street: "", //街道
  address: [] // 添加这个字段
});
const formFile = ref({
  images: [],
  video: []
});

const formRef = ref(null);
const richFlag = ref(false);
const adminId = ref(null);
onMounted(() => {
  adminId.value = route.query.id;
  getData();
});

// 地图弹窗
const dialogTableVisible = ref(false);
// 地图位置
const locationPostion = ref([]);
// 存储用户选择的位置信息
const selectedLocation = ref(null);
// 地图范围
const teacherTimeInfo = {
  location_range: 1000
};
// 地图确认
const handleMapConfirm = data => {
  console.log("🎉-----handleMapConfirm-----", data);
  form.value.detailedAddress = data.address;
  form.value.latitude = data.point.lat;
  form.value.longitude = data.point.lng;
  locationPostion.value = [data.point.lng, data.point.lat];

  // 保存选择的位置信息，下次打开时会自动定位到这里
  selectedLocation.value = data;
};

const formData = ref([
  {
    label: "编号",
    type: "text",
    prop: "id",
    check: true,
    placeholder: "请输入机构名称",
    width: "200px"
  },
  {
    label: "创建时间",
    type: "text",
    // check: true,
    prop: "createdAt",
    placeholder: "请输入机构别名",
    width: "200px"
  },
  {
    label: "实践点名称",
    type: "text",
    prop: "name",
    span: 2,
    check: true,
    placeholder: "请输入机构名称",
    width: "200px"
  },
  {
    label: "归属地信息",
    type: "cascader",
    prop: "address",
    check: true,
    span: 2,
    placeholder: "请选择详细地址",
    width: "400px"
  },
  {
    label: "详细地址",
    type: "map",
    span: 2,
    prop: "detailedAddress",
    check: true,
    placeholder: "请输入详细地址",
    width: "400px"
  },
  {
    label: "类型",
    type: "select",
    prop: "complexType",
    span: 2,
    check: true,
    placeholder: "请选择类型",
    width: "400px",
    opt: [
      { name: "会议室", val: "MEETING_ROOM" },
      { name: "教室", val: "CLASSROOM" },
      { name: "大厅", val: "LOBBY" },
      { name: "广场", val: "SQUARE" },
      { name: "公园", val: "PARK" },
      { name: "景点", val: "ATTRACTION" },
      { name: "户外", val: "OUTDOORS" },
      { name: "其他", val: "OTHER" }
    ]
  },
  {
    label: "实践点联系人",
    type: "input",
    check: true,
    prop: "emergencyPeople",
    placeholder: "请输入实践点联系人",
    width: "200px"
  },
  {
    label: "实践点联系电话",
    type: "input",
    check: true,
    prop: "emergencyPhone",
    placeholder: "请输入实践点联系电话",
    width: "200px"
  },
  {
    label: "标签",
    type: "tag",
    prop: "tags",
    check: true,
    placeholder: "请新建标签",
    span: 2,
    tags: []
  },
  {
    label: "一句话简介",
    type: "textarea",
    prop: "oneSentenceIntroduction",
    check: true,
    placeholder: "请输入一句话简介",
    width: "400px",
    maxLength: 15,
    span: 2
  },
  {
    label: "实践点介绍",
    type: "editor",
    check: true,
    prop: "introduction",
    placeholder: "请输入实践点介绍",
    width: "200px",
    span: 2
  },
  {
    label: "照片",
    type: "upload",
    prop: "images",
    limit: 9,
    span: 2,
    check: true,
    placeholder: "请上传照片",
    width: "400px",
    maxLength: 15,
    text: "支持上传png、jpg、jpeg图片格式，最多上传9张，最佳尺寸:200*200px，单张图片大小不超过10MB"
  },
  {
    label: "视频",
    type: "upload",
    type2: "video",
    prop: "video",
    limit: 3,
    span: 2,
    // check: true,
    placeholder: "请上传视频",
    width: "400px",
    maxLength: 15,
    text: "支持上传vido等视频格式，最多上传3个，单个视频大小不超过800MB "
  }
  //   {
  //     label: "状态",
  //     type: "text",
  //     // check: true,
  //     prop: "state",
  //     placeholder: "请输入机构别名",
  //     width: "200px"
  //   }
]);

const newData = ref();

// 根据id查询
const getData = async () => {
  let params = { id: route.query.id };
  try {
    const { code, data, msg } = await complexFindById(params);
    // console.log("🎁-----data-----", code, data, msg);
    if (code == 200 && data) {
      form.value = data;
      locationPostion.value = [data.longitude, data.latitude];
      // 文件集合回显
      if (data?.files) {
        data.files.forEach(item => {
          if (item.fileType === "PHOTO") {
            formFile.value.images.push(item.uploadFile);
          } else if (item.fileType === "PROMOTIONAL_VIDEO") {
            formFile.value.video.push(item.uploadFile);
          }
        });
      }
      // 标签回显
      if (data?.tags) {
        formData.value[8].tags = data.tags;
      }
      fileData();
      newData.value = JSON.parse(JSON.stringify(data));
      richFlag.value = true;

      // 如果有经纬度和地址信息，设置为初始选择位置
      if (data.longitude && data.latitude && data.detailedAddress) {
        selectedLocation.value = {
          point: {
            lng: data.longitude,
            lat: data.latitude
          },
          address: data.detailedAddress
        };
      }
      // 归属地回显
      await initAddressEcho(data);
    }
  } catch (error) {
    console.log("catch-----error-----", error);
  }
};
// 自定义校验方法
const validateIntroduction = (rule, value, callback) => {
  const errorMsg = "实践点介绍不能为空";

  // 检查是否为空
  if (!value) {
    return callback(new Error(errorMsg));
  }

  // 移除HTML标签和HTML空格字符，并去除前后空格
  let cleanValue = value
    .replace(/<[^>]*>/g, "")
    .replace(/&nbsp;|&ensp;|&emsp;|&thinsp;/g, "")
    .trim();

  return cleanValue === "" ? callback(new Error(errorMsg)) : callback();
};
// 自定义校验类型
const validateEmergencyPeople = (rule, value, callback) => {
  if (!value) {
    callback(new Error("请选择类型"));
  } else {
    callback();
  }
};
// 自定义校验图片
const validateImagesFile = (rule, value, callback) => {
  if (formFile.value.images.length <= 0) {
    callback(new Error("请上传图片"));
  } else {
    callback();
  }
};
// 自定义校验一句话简介
const validateOneSentenceIntroduction = (rule, value, callback) => {
  if (!value) {
    return callback(new Error("一句话简介不能为空"));
  } else if (value.length > 100) {
    return callback(new Error("一句话简介不能超过100个字"));
  } else {
    callback();
  }
};
// 自定义校验标签
const validateTags = (rule, value, callback) => {
  if (formData.value[8].tags.length <= 0) {
    return callback(new Error("标签不能为空"));
  } else {
    callback();
  }
};
// 校验规则
const rules = ref({
  detailedAddress: [
    { required: true, message: "详细地址不能为空", trigger: "blur" }
  ],
  introduction: [
    { required: true, validator: validateIntroduction, trigger: "blur" }
  ],
  complexType: [{ required: true, message: "类型不能为空", trigger: "blur" }],
  emergencyPeople: [
    { required: true, validator: validateEmergencyPeople, trigger: "blur" }
  ],
  tags: [{ required: true, validator: validateTags, trigger: "blur" }],
  oneSentenceIntroduction: [
    {
      required: true,
      validator: validateOneSentenceIntroduction,
      trigger: "blur"
    }
  ],
  images: [{ required: true, validator: validateImagesFile, trigger: "blur" }],
  address: [{ required: true, message: "详细地址不能为空", trigger: "blur" }]
});
const submitForm = debounce(
  () => {
    formRef.value.validate(valid => {
      if (valid) {
        console.log("表单数据:", form.value);
        addbase();
      } else {
        console.log("表单校验失败");
      }
    });
  },
  1000,
  { immediate: true }
);
const getListLoading = ref(false);
const addbase = async () => {
  if (getListLoading.value) return;
  getListLoading.value = true;
  form.value.files = []; // 清空文件集合
  fileData(); //文件集合处理
  let paramsData = {};
  // paramsData.tags = [...formData.value[7].tags];
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  paramsData = compareObjects(newData.value, paramsData);
  // console.log('🌈-----paramsData-----', paramsData);
  if (paramsData.emergencyPhone) {
    paramsData.emergencyPhone = encryption(paramsData.emergencyPhone || "");
  }
  console.log("🎁-----paramsData-----", paramsData);
  if (Object.keys(paramsData).length == 0) {
    ElMessage({
      type: "warning",
      message: "未修改任何信息"
    });
    getListLoading.value = false;
    return;
  }
  paramsData.id = adminId.value;
  paramsData.complexType = form.value.complexType;
  paramsData.oneSentenceIntroduction = form.value.oneSentenceIntroduction;
  if (form.value.address && form.value.address.length === 3) {
    // 如果只有3级（省市区），删除 street 字段
    paramsData.city = "直辖市";
    // delete paramsData.street;
  } else if (form.value.address && form.value.address.length === 4) {
    // 如果有4级（省市区街道），保留 street 字段
    paramsData.city = form.value.city;
  }

  const operateLog = {
    operateLogType: "COMPLEX_MANAGEMENT",
    operateType: "编辑了",
    operatorTarget: `"${form.value.name}"的实践点信息`
  };
  console.log("🍪-----paramsData-----", paramsData);
  // return;
  const { code } = await complexUpdate(paramsData, operateLog);
  if (code === 200) {
    ElMessage({
      type: "success",
      message: "编辑成功"
    });
    cancel();
  } else {
    ElMessage({
      type: "error",
      message: "编辑失败"
    });
  }
  getListLoading.value = false;
};
//返回上一页
const cancel = () => {
  router.go(-1);
};
const edit = () => {
  router.go(-1);
};
const isView = ref(true);
const isViewFn = () => {
  isView.value = !isView.value;
  if (isView.value) {
    form.value.emergencyPhone = newData.value.emergencyPhone;
  } else {
    form.value.emergencyPhone = decrypt(newData.value.emergencyPhoneCt);
  }
};

// 自建标签
const inputPeriodVisible = ref(false);
const showPeriodInput = () => {
  inputPeriodVisible.value = true;
};
const inputPeriodValue = ref("");
const tagesShow = ref(true);
const handlePeriodInputConfirm = () => {
  if (inputPeriodValue.value) {
    if (formData.value[8].tags.includes(inputPeriodValue.value)) {
      ElMessage.error("标签名称不可重复，请重新输入");
      inputPeriodVisible.value = false;
      inputPeriodValue.value = "";
      return false; // 阻止添加
    }

    if (formData.value[8].tags.length >= 19) {
      tagesShow.value = false;
    }

    if (formData.value[8].tags.length >= 20) {
      ElMessage.error("标签最多不超过20个");
      inputPeriodVisible.value = false;
      inputPeriodValue.value = "";
      return false; // 阻止添加
    }

    if (inputPeriodValue.value.length > 20) {
      ElMessage.error("输入的字数不能超过20个");
    } else {
      formData.value[8].tags.push(inputPeriodValue.value);
    }
  }
  inputPeriodVisible.value = false;
  inputPeriodValue.value = "";
};
// 清除标签
const handlePeriodClose = tag => {
  formData.value[8].tags.splice(formData.value[8].tags.indexOf(tag), 1);
  tagesShow.value = true;
};
// 文件上传限制
const handleExceed = (files, item, limit) => {
  switch (item) {
    case "images":
      return ElMessage({
        message: "当前限制上传九张图片",
        type: "error"
      });
    case "video":
      return ElMessage({
        message: "当前限制上传三个视频",
        type: "error"
      });
    default:
      break;
  }
};
// 文件上传
const beforeUpload = async (file, item) => {
  // console.log("🍧-----file, item-----", file, item);
  // 判断是否重复
  // console.log("🌵----- formFile.value[item]-----", formFile.value[item]);
  const isDuplicate = formFile.value[item].some(
    f =>
      (f.name === file.name || f.fileName === file.name) &&
      (f.size === file.size || f.totalSize === file.size)
  );
  // console.log('🐳-----isDuplicate-----', isDuplicate);
  if (isDuplicate) {
    ElMessage.error("不能上传重复的文件");
    return false;
  }
  let fileType = [];
  switch (item) {
    case "video":
      fileType = ["video"];
      break;
    case "images":
      fileType = ["image"];
      break;
    default:
      break;
  }
  let strData = validateFileType(file, fileType);
  let typeImage = validateFileType(file, ["image"]);
  let isSize = isOverSizeLimit(
    file,
    item === "video" ? 800 : typeImage.valid === true ? 10 : 30
  );
  if (item === "images") {
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png"];
    const fileTypes = file.type;
    if (!allowedTypes.includes(fileTypes)) {
      strData.valid = false;
      ElMessage.error("请上传jpg、jpeg、png格式的图片");
      return false;
    }
  }

  if (strData.valid === true && isSize.valid === true) {
    console.log("上传");
    try {
      let { code, data } = await uploadFile(file, () => {}, fileType);
      console.log("🍧-----code, data-----", code, data);
      console.log(formFile.value[item], "上传前查看");
      if (code === 200) {
        formFile.value[item].push({
          ...data,
          name: data.name || data.fileName || file.name,
          url: data.url || data.fileUrl || "",
          uid: data.uploadId || Date.now() + Math.random(),
          status: "success" // 关键
        });
        console.log(formFile.value[item], "查看文件");
      }
    } catch (error) {
      ElMessage({
        message: error.message,
        type: "error"
      });
    }
  } else {
    if (strData.message) {
      ElMessage.error(strData.message);
      return false;
    }
    if (isSize.message) {
      ElMessage.error(isSize.message);
      return false;
    }
  }
};
// 移除文件
const handleRemove = (file, item) => {
  if (item === "video") return;
  const idx = formFile.value[item].findIndex(
    i => i.url === file.url && i.uid === file.uid
  );
  if (idx !== -1) {
    formFile.value[item].splice(idx, 1);
  } else {
    formFile.value[item].splice(0, 1);
  }
};
// 预览文件
const dialogVisible = ref(false);
const dialogImageUrl = ref("");
const handlePreview = (file, prop) => {
  dialogImageUrl.value = file.url;
  dialogVisible.value = true;
};
//删除文件
const uploadKey = ref(Date.now());
const getDeleted = (item, index) => {
  formFile.value[item].splice(index, 1);
  uploadKey.value = Date.now(); // 关键：强制刷新
};
//文件处理
const fileData = () => {
  //照片
  let images = formFile.value.images;
  // 视频
  let video = formFile.value.video;
  if (images.length > 0) {
    setFilesFn(images, "PHOTO");
  }
  if (video.length > 0) {
    setFilesFn(video, "PROMOTIONAL_VIDEO");
  }
  // 过滤掉无效项，保证 files 结构正确
  form.value.files = (form.value.files || []).filter(
    f => f && typeof f === "object" && f.fileIdentifier
  );
  // 保证 files 至少是 []
  if (!Array.isArray(form.value.files)) {
    form.value.files = [];
  }
};
// 🌈thanks for AnNan!! 🎇🎇🎇🎇🎇🎇
const setFilesFn = (val, type) => {
  for (let index = 0; index < val.length; index++) {
    const element = val[index].fileIdentifier;
    // console.log("🌵-----element-----", element);
    form.value.files.push({
      fileType: type,
      fileIdentifier: element,
      sortOrder: index + 1
    });
  }
};

const addressKey = ref(0);
const propsCascader = ref({
  lazy: true,
  // value: "name",
  async lazyLoad(node, resolve) {
    const { level, data } = node;
    try {
      let parentId = 0;

      if (level === 0) {
        parentId = 0;
      } else {
        parentId = data.value;
      }

      console.log(`-----level: ${level}, parentId: ${parentId}-----`);

      const { code, data: areaData } = await findArea({ parentId });

      if (
        code === 200 &&
        areaData &&
        Array.isArray(areaData) &&
        areaData.length > 0
      ) {
        // 如果是第二层或第三层，需要预先检查下一层是否有数据
        if (level === 2 || level === 3) {
          // 只调用一次接口检查下一层是否有数据
          const { code: childCode, data: childData } = await findArea({
            parentId: areaData[0].id // 使用第一个节点的ID来检查
          });

          const hasChildren =
            childCode === 200 &&
            childData &&
            Array.isArray(childData) &&
            childData.length > 0;

          // 根据检查结果设置所有节点的leaf属性
          const nodes = areaData.map(item => {
            return {
              value: item.id,
              label: item.name,
              name: item.name,
              leaf: !hasChildren // 如果下一层没有数据，所有节点都设置为叶子节点
            };
          });
          resolve(nodes);
        } else {
          // 其他层级正常处理
          const nodes = areaData.map(item => {
            return {
              value: item.id,
              label: item.name,
              name: item.name,
              leaf: false
            };
          });

          resolve(nodes);
        }
      } else {
        resolve([]);
      }
    } catch (error) {
      console.error(`获取第${level}级数据出错:`, error);
      resolve([]);
    }
  }
});
// 多级连选
const handleChange = val => {
  if (val.length <= 3) {
    form.value.province = String(val[0]) || "";
    form.value.city = "直辖市";
    form.value.district = String(val[1]) || "";
    form.value.street = String(val[2]) || "";
  } else {
    form.value.province = String(val[0]) || "";
    form.value.city = String(val[1]) || "";
    form.value.district = String(val[2]) || "";
    form.value.street = String(val[3]) || "";
  }
};
const initAddressEcho = async data => {
  if (data.city !== "直辖市") {
    form.value.address = [data.province, data.city, data.district, data.street];
  } else {
    form.value.address = [data.province, data.district, data.street];
  }
  const list = await getCascaderLabelsByIds(form.value.address, findArea);
  form.value.address = list;
};

// 通用的函数
const getCascaderLabelsByIds = async (
  idArray,
  apiFunction,
  parentIdField = "parentId"
) => {
  if (!idArray || !Array.isArray(idArray) || idArray.length === 0) {
    return [];
  }

  const labels = [];

  try {
    for (let i = 0; i < idArray.length; i++) {
      const id = idArray[i];
      let parentId = 0;

      if (i > 0) {
        parentId = idArray[i - 1];
      }

      const { code, data } = await apiFunction({ [parentIdField]: parentId });

      if (code === 200 && data && Array.isArray(data)) {
        const item = data.find(
          area => area.id === id || String(area.id) === String(id)
        );
        if (item) {
          labels.push(parseInt(item.id));
        } else {
          labels.push("未知");
        }
      } else {
        labels.push("未知");
      }
    }

    return labels;
  } catch (error) {
    console.error("获取级联选择器标签失败:", error);
    return [];
  }
};
</script>

<template>
  <div class="main">
    <el-scrollbar class="scrollbar">
      <div>
        <el-form ref="formRef" :model="form" :rules="rules">
          <el-descriptions
            v-if="richFlag"
            itle=""
            :column="2"
            border
            :label-width="'200px'"
          >
            <el-descriptions-item
              v-for="(item, index) in formData"
              :key="index"
              label-align="center"
              label-class-name="my-label"
              :span="item.span || 1"
            >
              <template #label>
                <span v-if="item.check" class="star">*</span>{{ item.label }}
              </template>
              <el-form-item
                :prop="item.prop"
                :inline-message="item.check"
                style="margin-bottom: 0"
                :show-message="true"
                error-placement="right"
              >
                <!-- text -->
                <template v-if="item.type === 'text'">
                  <div style="color: #a8a8a8">
                    {{
                      item.prop === "createdAt"
                        ? formatTime(form[item.prop], "YYYY-MM-DD HH:mm:ss")
                        : form[item.prop] || "--"
                    }}
                  </div>
                </template>
                <!-- input输入 -->
                <template v-if="item.type === 'input'">
                  <el-input
                    v-model.trim="form[item.prop]"
                    :placeholder="item.placeholder"
                    :style="{ width: item.width }"
                    :disabled="
                      item.prop === 'emergencyPhone' && newData[item.prop]
                        ? isView
                        : false
                    "
                  >
                    <template
                      v-if="item.prop === 'emergencyPhone' && form[item.prop]"
                      #suffix
                    >
                      <el-icon
                        v-if="isView"
                        style="cursor: pointer"
                        @click="isViewFn"
                      >
                        <Hide />
                      </el-icon>
                      <el-icon v-else style="cursor: pointer" @click="isViewFn">
                        <View />
                      </el-icon>
                    </template>
                  </el-input>
                </template>
                <!-- 富文本 -->
                <template v-else-if="item.type === 'editor'">
                  <div style="width: 100%">
                    <RichEditor v-model="form[item.prop]" height="150px" />
                  </div>
                </template>
                <!-- 地图 -->
                <template v-else-if="item.type === 'map'">
                  <div class="selsect-pos">
                    <div class="cover" @click="dialogTableVisible = true" />
                    <el-input
                      v-model="form[item.prop]"
                      class="input-part"
                      placeholder="请选择地址"
                      :style="{ width: item.width }"
                    >
                      <template #suffix>
                        <div class="pos-icon">
                          <img :src="ImgPos" class="wfull h-full" alt="">
                        </div>
                      </template>
                    </el-input>
                  </div>
                </template>
                <!-- 下拉框(单选) -->
                <template v-if="item.type === 'select'">
                  <el-select
                    v-model="form[item.prop]"
                    :placeholder="item.placeholder"
                    :style="{ width: item.width }"
                    :options="item.opt"
                  >
                    <el-option
                      v-for="items in item.opt"
                      :key="items.val"
                      :label="items.name"
                      :value="items.val"
                    />
                  </el-select>
                </template>
                <!-- 上传组件 -->
                <template v-else-if="item.type === 'upload'">
                  <div style="width: 100%">
                    <el-upload
                      :key="
                        item.prop === 'video'
                          ? uploadKey + item.prop
                          : item.prop
                      "
                      action="#"
                      :show-file-list="item.prop === 'video' ? false : true"
                      :file-list="formFile[item.prop]"
                      :http-request="() => {}"
                      :limit="item.limit"
                      :on-exceed="
                        file => handleExceed(file, item.prop, item.limit)
                      "
                      :accept="item.prop === 'video' ? 'video/*' : 'image/*'"
                      :list-type="item.type2 ? '' : 'picture-card'"
                      :before-upload="file => beforeUpload(file, item.prop)"
                      :on-remove="file => handleRemove(file, item.prop)"
                      :on-preview="file => handlePreview(file, item.prop)"
                      :class="{
                        hideUploadBtn: formFile[item.prop].length >= item.limit
                      }"
                    >
                      <template v-if="item.type2 === 'video'">
                        <img :src="uploadImg" alt="">
                      </template>
                      <template v-else>
                        <el-icon><Plus /></el-icon>
                      </template>
                    </el-upload>

                    <template v-if="item.type2">
                      <template
                        v-for="(item2, index2) in formFile[item.prop]"
                        :key="index2"
                      >
                        <FileItem
                          isNeedDelte
                          :data="item2"
                          :index="index2"
                          :isTypeVideo="true"
                          style="width: 50%; min-width: 130px"
                          @delete="getDeleted(item.prop, index2)"
                        />
                      </template>
                    </template>
                  </div>

                  <div class="upload_text">{{ item.text }}</div>
                </template>
                <!-- 标签 -->
                <template v-if="item.type === 'tag'">
                  <el-tag
                    v-for="tag in item.tags"
                    :key="tag"
                    style="margin: 0 10px 4px 0"
                    closable
                    :disable-transitions="false"
                    @close="handlePeriodClose(tag)"
                  >
                    {{ tag }}
                  </el-tag>
                  <div
                    v-if="tagesShow"
                    style="display: block; margin-top: -6px"
                  >
                    <el-input
                      v-if="inputPeriodVisible"
                      ref="periodInputRef"
                      v-model.trim="inputPeriodValue"
                      class="w-20"
                      size="small"
                      @keyup.enter="handlePeriodInputConfirm"
                      @blur="handlePeriodInputConfirm"
                    />
                    <el-button
                      v-else
                      class="button-new-tag"
                      size="small"
                      @click="showPeriodInput"
                    >
                      +
                    </el-button>
                  </div>
                </template>
                <!-- textArea -->
                <template v-if="item.type === 'textarea'">
                  <el-input
                    v-model="form[item.prop]"
                    :rows="3"
                    type="textarea"
                    :maxlength="100"
                    show-word-limit
                    :placeholder="item.placeholder"
                  />
                </template>
                <!-- 多级连选 -->
                <template v-if="item.type === 'cascader'">
                  <!-- {{ form[item.prop] }} -->
                  <el-cascader
                    :key="`cascader-${addressKey}`"
                    v-model="form[item.prop]"
                    :props="propsCascader"
                    :placeholder="item.placeholder"
                    style="width: 400px"
                    clearable
                    :show-all-levels="true"
                    :expand-trigger="'click'"
                    :check-strictly="false"
                    :multiple="false"
                    @change="handleChange"
                  />
                </template>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </el-form>
      </div>
    </el-scrollbar>

    <div class="account_management">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" :loading="getListLoading" @click="submitForm">
        保存
      </el-button>
    </div>

    <Map
      v-if="dialogTableVisible"
      v-model="dialogTableVisible"
      :center="locationPostion"
      :selected-location="selectedLocation"
      :checkInResult="teacherTimeInfo"
      @confirm="handleMapConfirm"
    />
  </div>
</template>

<style lang="scss" scoped>
.scrollbar {
  // padding-top: 20px;
  // padding: 20px;
  height: calc(100vh - 201px);
  background-color: #fff;
}
.main {
  padding: 20px;
  background: #fff;
}

.header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 20px;

  .title_left {
    display: flex;

    :nth-child(2) {
      margin-left: 40px;
    }
  }

  .title_rigth {
    display: flex;

    :nth-child(2) {
      margin-left: 40px;
    }
  }
}

.account_management {
  margin: 20px 0 0 0;
  display: flex;
  justify-content: flex-end;
  // :nth-child(2) {
  //   margin-left: 20px;
  // }
}

:deep(.my-label) {
  background: #e1f5ff !important;
}
.star {
  margin-right: 3px;
  color: red;
}

.selsect-pos {
  position: relative;
  // width: 490px;
  // height: 60px;
  // background: red;
  .cover {
    cursor: pointer;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    // background: red;
    width: 100%;
    height: 100%;
  }

  .pos-icon {
    width: 24px;
    height: 24px;
  }
}

:deep(.hideUploadBtn .el-upload--picture-card) {
  display: none;
}

.upload_text {
  display: block; // 改为块级元素
  font-size: 12px;
  color: #8c939d;
  white-space: pre-line;
  word-wrap: break-word;
  width: 100%;
}
</style>
