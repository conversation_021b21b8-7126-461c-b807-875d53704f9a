import { Edit, Hide, View } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import { onMounted, reactive, ref, onActivated, defineEmits } from "vue";
// import { parentIsFreeze, parentFindAll } from "@/api/parentManage.js";
import { courseFindAll } from "@/api/course.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { decrypt, encryption } from "@/utils/SM4.js";
import { teacherDatabaseCommentsAll } from "@/api/teachers/teacherResourcePool.js";

export function useRole() {
  const getShortContent = content => {
    // 假设一行大概显示 50 个字符，两行就是 100 个，可根据实际情况调整
    if (content) {
      return content?.slice(0, 20) + "......";
    } else {
      return "......";
    }
  };
  const columns = [
    {
      label: "评价人",
      prop: "parentName",
      minWidth: 90,
      formatter: ({ parentName }) => {
        return parentName || "--";
      }
    },
    {
      label: "评价时间",
      minWidth: 90,
      prop: "createdAt",
      formatter: ({ createdAt }) => {
        return createdAt
          ? dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss")
          : "--";
      }
    },
    {
      label: "打分",
      minWidth: 180,
      prop: "rating",
      cellRenderer: ({ row }) => (
        <el-rate v-model={row.rating} disabled text-color="#ff9900" />
      )
    },
    {
      label: "评价内容",
      minWidth: 90,
      prop: "onlineTermNumber",
      cellRenderer: ({ row }) => {
        return (
          <div>
            {row.content
? (
              <div>
                {row.isCollapsed
? (
                  <>
                    {getShortContent(row.content)}
                    <el-link
                      type="primary"
                      underline={false}
                      onClick={() => (row.isCollapsed = false)}
                    >
                      展开
                    </el-link>
                  </>
                )
: (
                  <>
                    {row.content}
                    <el-link
                      class="text-[#ff9900] hover:text-[#ff9900]"
                      underline={false}
                      onClick={() => (row.isCollapsed = true)}
                    >
                      收起
                    </el-link>
                  </>
                )}
              </div>
            )
: (
              "--"
            )}
          </div>
        );
      }
    },
    {
      label: "操作",
      fixed: "right",
      width: 180,
      slot: "operation"
    }
  ];

  const courseTypeoptions = ref([
    {
      value: "",
      label: "全部"
    },
    {
      value: false,
      label: "正常"
    },
    {
      value: true,
      label: "冻结"
    }
  ]);

  const router = useRouter();
  const route = useRoute();
  const dataList = ref([]);
  const loadingTable = ref(false);
  const pagination = {
    total: 0,
    pageSize: 15,
    currentPage: 1,
    background: true,
    pageSizes: [15, 20, 30, 50]
  };
  const params = ref({
    page: 1,
    size: 15,
    sort: "createdAt,desc"
  });
  const startAdd = async () => {
    const paramsArg = {
      page: params.value.page - 1,
      size: params.value.size,
      sort: params.value.sort,
      teacherId: route.query.id
    };
    const [err, res] = await requestTo(courseFindAll(paramsArg));
    // const [err, res] = await requestTo(teacherDatabaseCommentsAll(paramsArg));
    if (res) {
      dataList.value = res.content?.map(it => {
        return {
          ...it,
          isCollapsed: true
        };
      });
      pagination.total = res.totalElements;
      // pagination.pageSize = res.size;
      // pagination.currentPage = 1;
    }
    if (err) {
    }
  };
  const removeEmptyValues = obj => {
    return Object.fromEntries(
      Object.entries(obj).filter(
        ([key, value]) =>
          value !== null &&
          value !== "" &&
          !(Array.isArray(value) && value.length === 0)
      )
    );
  };

  // 每页多少条
  const handleSizeChange = val => {
    pagination.pageSize = val;
    params.value.size = val;
    startAdd();
  };
  // 前往页数
  const handleCurrentChange = val => {
    pagination.currentPage = val;
    params.value.page = val;
    startAdd();
  };

  const handleInput = value => {
    const regex = /^[0-9+\-*/]*$/;
    let regExp = /^\d+$/;
    const phoneRegex = /^1[3-9]\d{9}$/; // 匹配中国大陆手机号码
    if (!regex.test(value)) {
      from.phone = "";
      ElMessage({
        type: "error",
        message: "请输入正确的手机号"
      });
    }
  };
  //   const getInfoid = item => {
  //     console.log('🌵item------------------------------>',item);
  //     emites("selectCourse", item);
  //   };

  onActivated(() => {
    let time1 = from.time !== null ? new Date(from.time[0]).getTime() : "";
    let time2 = from.time !== null ? new Date(from.time[1]).getTime() : "";
    const text = {
      name: from.name || "",
      phone: from.phone || "",
      freeze: from.freeze || "",
      startTime: time1 || "",
      endTime: time2 || "",
      page: pagination.currentPage - 1 || "",
      size: pagination.pageSize || ""
    };
    startAdd();
  });

  onMounted(() => {
    startAdd();
  });

  return {
    pagination,
    dataList,
    columns,
    handleCurrentChange,
    handleSizeChange,
    loadingTable,
    courseTypeoptions
    // getInfoid
  };
}
