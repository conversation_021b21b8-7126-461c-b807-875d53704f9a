<script setup>
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
// import TabTitle from "@/components/Base/tabInfo.vue";
// import AllEvalute from "./components/allEvaluate.vue";
import { formatTime } from "@/utils/index";
import { courseFindId } from "@/api/course.js";
import { requestTo } from "@/utils/http/tool";
const route = useRoute();
const router = useRouter();
const textarea = ref("");
const infoShow = ref(false);
// 表头
const tableHeader = ref([
  {
    id: "1",
    label: "课程名",
    value: "",
    width: "107px"
  },
  {
    id: "2",
    label: "课程ID",
    value: "",
    width: "107px"
  },
  {
    id: "3",
    label: "创建时间",
    value: "",
    width: "107px"
  },
  {
    id: "4",
    label: "课程类型",
    value: "",
    width: "107px"
  },
  {
    id: "5",
    label: "人数限制",
    value: "",
    width: "107px"
  }
  // {
  //   id: "6",
  //   label: "基地",
  //   value: "",
  //   width: "107px"
  // }
]);
const url = ref();

const srcList = ref([]);
const tabInfoEvt = obj => {
  console.log("💗tabInfoEvt---------->", obj);
};
// 编辑基本信息
const editinfo = val => {
  router.push({
    path: "/course/courseCreate",
    query: {
      type: "edite",

      id: route.query.id
    }
  });
};
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    id: route.query.id
  };
  const [err, result] = await requestTo(courseFindId(paramsData));
  if (result) {
    tableHeader.value[0].value = result.name || "--";
    tableHeader.value[1].value = result.id || "--";
    tableHeader.value[2].value =
      formatTime(result.createdAt, "YYYY-MM-DD HH:mm:ss") || "--";
    tableHeader.value[3].value = result.courseType.name || "--";
    if (result.minPeopleNumber && result.maxPeopleNumber) {
      tableHeader.value[4].value =
        result.minPeopleNumber + "-" + result.maxPeopleNumber;
    } else if (result.minPeopleNumber) {
      tableHeader.value[4].value = result.minPeopleNumber;
    } else if (result.maxPeopleNumber) {
      tableHeader.value[4].value = result.maxPeopleNumber;
    } else {
      tableHeader.value[4].value = "--";
    }
    if (result.files?.length) {
      result.files.map(item => {
        srcList.value.push(item?.uploadFile?.url);
      });
      url.value = result.files[0]?.uploadFile?.url;
    }
    // tableHeader.value[5].value = result.complex.name || "--";
  } else {
    ElMessage.error(err);
  }
  getListLoading.value = false;
};
onMounted(() => {
  getTableList();
});
</script>

<template>
  <div class="examine-detail">
    <div class="curse-table">
      <el-descriptions
        class="margin-top"
        title=""
        :column="3"
        border
        style="width: 1300px"
      >
        <template v-for="(item, index) in tableHeader" :key="index">
          <el-descriptions-item width="120px" label-align="center">
            <template #label>
              <div class="cell-item">{{ item.label }}</div>
            </template>
            {{ item.value }}
          </el-descriptions-item>
        </template>
      </el-descriptions>
      <div class="img">
        <!-- <img src="@/assets/user.jpg" alt="" /> -->
        <el-image
          :src="url"
          :zoom-rate="1.2"
          :max-scale="7"
          :min-scale="0.2"
          :preview-src-list="srcList"
          show-progress
          :initial-index="4"
          :hide-on-click-modal="true"
          fit="cover"
          class="img-pic"
        />
      </div>
      <div class="tabtn">
        <!-- <el-button type="primary" @click="turnfile">资质文件</el-button> -->
        <el-button type="primary" @click="editinfo">编辑基本信息</el-button>
      </div>
    </div>
    <div v-if="infoShow" class="info-table">
      <!-- tab切换 -->
      <!-- <TabTitle :tabTitle="tabTitle" @tab-data="tabInfoEvt" /> -->
      <!-- 切换信息 -->
      <div class="tab-info">
        <!-- <AllEvalute /> -->
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.examine-detail {
  // width: calc(100% - 48px);
  // height: calc(100vh - 48px);
  // height: calc(100vh - 150px);
  // overflow-y: auto;
  .curse-table {
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 24px 20px;
    // height: 250px;
    // margin-bottom: 30px;
    // width: calc(100% - 48px);
    background-color: #fff;

    .img {
      width: 145px;
      height: 76px;
      margin: 0 25px;

      .img-pic {
        width: 145px;
        height: 80px;
        // object-fit: cover;
      }
    }

    .tabtn {
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      height: 80px;
      //   margin-right: 20px;
    }
  }

  .info-table {
    width: 100%;
    height: 436px;

    .tab-info {
      box-sizing: border-box;
      width: 100%;
      height: 580px;
      padding: 20px;
      background-color: #fff;
      // border-top:2px solid red;
    }
  }
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}
</style>
