import { defineStore } from "pinia";
import {
  ascending,
  constantMenus,
  debounce,
  filterNoPermissionTree,
  filterTree,
  formatFlatteningRoutes,
  getKeyList,
  store
} from "../utils";
import { useMultiTagsStoreHook } from "./multiTags";
function filterData(data, filterFn) {
  return data.filter(item => {
    if (item.children) {
      const filteredChildren = filterData(item.children, filterFn);
      item.children = filteredChildren; // 更新子元素
      return filteredChildren.length > 0; // 如果子元素有符合条件的，保留当前元素
    }
    return filterFn(item); // 使用自定义过滤函数检查当前元素
  });
}
function getAllIdCodes(data) {
  const idCodes = new Set();
  function traverse(node) {
    if (node.idCode) {
      idCodes.add(node.idCode);
    }
    if (node.children) {
      node.children.forEach(traverse);
    }
  }
  data.forEach(traverse);
  return Array.from(idCodes);
}

export const usePermissionStore = defineStore({
  id: "pure-permission",
  state: () => ({
    // 静态路由生成的菜单
    constantMenus,
    // 整体路由生成的菜单（静态、动态）
    wholeMenus: [],
    // 整体路由（一维数组格式）
    flatteningRoutes: [],
    // 缓存页面keepAlive
    cachePageList: []
  }),
  actions: {
    /** 组装整体路由生成的菜单 */
    handleWholeMenus(routes) {
      const isTestModel = false;
      if (isTestModel) {
        this.wholeMenus = filterNoPermissionTree(
          filterTree(ascending(this.constantMenus.concat(routes)))
        );
      } else {
        const authorityDTOS = localStorage.getItem("authorityDTOS")
          ? JSON.parse(localStorage.getItem("authorityDTOS"))?.authorityDTOS
          : [];
        console.log("🎁-----authorityDTOS-----", authorityDTOS);

        const idCodeList = getAllIdCodes(authorityDTOS).map(Number => +Number);
        console.log("🌈-----idCodeList-----", idCodeList);

        const temp_wholeMenus = filterNoPermissionTree(
          filterTree(ascending(this.constantMenus.concat(routes)))
        );
        console.log("🐳-----temp_wholeMenus-----", temp_wholeMenus);

        const matchFunction = item => {
          // console.log('🌵-----item-----', item);
          return item.meta?.idCode && idCodeList.includes(item.meta.idCode);
        };
        const newTreeData = filterData(temp_wholeMenus, matchFunction);
        console.log("🌵-----newTreeData-----", newTreeData);
        this.wholeMenus = newTreeData;
      }

      this.flatteningRoutes = formatFlatteningRoutes(
        this.constantMenus.concat(routes)
      );
    },
    cacheOperate({ mode, name }) {
      const delIndex = this.cachePageList.findIndex(v => v === name);
      switch (mode) {
        case "refresh":
          this.cachePageList = this.cachePageList.filter(v => v !== name);
          break;
        case "add":
          this.cachePageList.push(name);
          break;
        case "delete":
          delIndex !== -1 && this.cachePageList.splice(delIndex, 1);
          break;
      }
      /** 监听缓存页面是否存在于标签页，不存在则删除 */
      debounce(() => {
        let cacheLength = this.cachePageList.length;
        const nameList = getKeyList(useMultiTagsStoreHook().multiTags, "name");
        while (cacheLength > 0) {
          nameList.findIndex(v => v === this.cachePageList[cacheLength - 1]) ===
          -1 &&
          this.cachePageList.splice(
              this.cachePageList.indexOf(this.cachePageList[cacheLength - 1]),
              1
            );
          cacheLength--;
        }
      })();
    },
    /** 清空缓存页面 */
    clearAllCachePage() {
      this.wholeMenus = [];
      this.cachePageList = [];
    }
  }
});

export function usePermissionStoreHook() {
  return usePermissionStore(store);
}
