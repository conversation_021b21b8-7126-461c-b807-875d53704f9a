<script setup>
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  emptyText: {
    type: String,
    default: "暂无数据"
  },
  emptyImg: {
    type: String,
    default: ""
  },
  tiomeOut: {
    //是否超过结束时间
    type: Boolean,
    default: false
  }
});
onMounted(() => {
  console.log("🌈----- props.tiomeOut----- ", props.tiomeOut); //  props.tiomeOut
});
</script>

<template>
  <div class="empty-content">
    <div v-if="data.length !== 0 && !tiomeOut" class="contents">
      <slot />
    </div>

    <div v-else class="data-null">
      <img src="@/assets/subject/null_bg.png">
      <div class="tip">{{ emptyText }}</div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.empty-content {
  height: 100%;
  width: 100%;
  .contents {
    padding-bottom: 1px;
  }
}
.data-null {
  // margin-top: 75px;
  margin: auto;
  width: 254px;
  //   height: 179px;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  img {
    width: 100%;
  }
  .tip {
    font-size: 34px;
    font-weight: normal;
    font-stretch: normal;
    line-height: 41px;
    letter-spacing: 3px;
    color: #c6c6c6;
  }
}
</style>
