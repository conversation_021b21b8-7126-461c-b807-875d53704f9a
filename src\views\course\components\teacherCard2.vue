<script setup>
import { ref } from "vue";
import { ElMessageBox } from "element-plus";
const props = defineProps({
  //   nameFather: {
  //     type: String,
  //     default: ""
  //   },
  //   nameChild: {
  //     type: String,
  //     default: ""
  //   }
  //   label: {
  //     type: String,
  //     default: ""
  //   }
  tableData: {
    type: Array,
    default: () => []
  }
});
const Reject = () => {
  ElMessageBox.confirm("确定拒绝该合作？", "拒绝申请", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(() => {
      console.log("确定拒绝");
    })
    .catch(() => {
      console.log("取消");
    });
};
const Agree = () => {
  ElMessageBox.confirm("确定同意该合作？", "同意申请", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(() => {
      console.log("确定同意");
    })
    .catch(() => {
      console.log("取消");
    });
};
</script>

<template>
  <div class="teacher-card-list">
    <div
      v-for="(item, index) in tableData"
      :key="index"
      class="teacher-card-item"
    >
      <div class="teacher-card-header">
        <div>
          <div class="img_title" />
          <div class="font_bold">{{ item.name }}</div>
        </div>
        <div class="font_colorccc">
          {{ item.time }}
        </div>
      </div>
      <div class="font_color">
        <span>申请课程：</span><span>{{ item.applyFor }}</span>
      </div>
      <div class="teacher-card-footer">
        <div v-if="item.type === 0" class="teacher-card-btn">
          <div>
            <el-button type="danger" @click="Reject">拒绝合作</el-button>
          </div>
          <div>
            <el-button type="primary" @click="Agree">同意合作</el-button>
          </div>
        </div>
        <div v-else class="color-red">已拒绝</div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.teacher-card-list {
  display: flex;
  flex-wrap: wrap !important;
  gap: 16px;
}
.teacher-card-item {
  flex: 0 0 calc(50% - 10px);
  box-sizing: border-box;
  background: #fff;
  border-radius: 8px;
  padding: 10px 16px;
  margin-bottom: 20px;
  min-width: 500px;
  border: 1px solid #e4e7ed;

  div {
    margin-bottom: 5px;
    &:last-child {
      margin-bottom: 0;
    }
  }
}
.teacher-card-header {
  height: 50px;
  display: flex;
  justify-content: space-between;
  line-height: 50px;
  div:nth-of-type(1) {
    width: 50%;
    display: flex;

    .img_title {
      width: 50px;
      height: 50px;
      border-radius: 25px;
      border: 1px solid #e4e7ed;
      background: #e4e7ed;
      margin-right: 10px;
    }
  }
}
.teacher-card-footer {
  display: flex;
  justify-content: right;
  height: 50px;
  border-top: 1px solid #e4e7ed;
  font-size: 16px;
  line-height: 50px;
  .teacher-card-btn {
    display: flex;
    gap: 10px;
  }
  div:nth-of-type(1) {
    .font_blue {
      color: rgb(3, 158, 219);
      cursor: pointer;
      margin-left: 10px;
    }
  }
}
.font_color {
  font-size: 14px;
  color: #464646;
  margin: 5px 0 15px 60px !important;
}
.font_bold {
  font-size: 18px;
  font-weight: 500;
}
.font_colorccc {
  font-size: 14px;
  color: #9b9b9b;
}
.color-red {
  color: rgb(255, 0, 0);
}
</style>
