let e = function () {
  return (e =
    Object.assign ||
    function (e) {
      for (var t, n = 1, r = arguments.length; n < r; n++)
        for (let i in (t = arguments[n]))
          Object.prototype.hasOwnProperty.call(t, i) && (e[i] = t[i]);
      return e;
    }).apply(this, arguments);
};
function t(e, t, n, r) {
  return new (n || (n = Promise))(function (i, o) {
    function a(e) {
      try {
        s(r.next(e));
      } catch (e) {
        o(e);
      }
    }
    function c(e) {
      try {
        s(r.throw(e));
      } catch (e) {
        o(e);
      }
    }
    function s(e) {
      let t;
      e.done
        ? i(e.value)
        : ((t = e.value),
          t instanceof n
            ? t
            : new n(function (e) {
                e(t);
              })).then(a, c);
    }
    s((r = r.apply(e, t || [])).next());
  });
}
function n(e, t) {
  let n,
    r,
    i,
    o,
    a = {
      label: 0,
      sent: function () {
        if (1 & i[0]) throw i[1];
        return i[1];
      },
      trys: [],
      ops: []
    };
  return (
    (o = { next: c(0), throw: c(1), return: c(2) }),
    typeof Symbol == "function" &&
      (o[Symbol.iterator] = function () {
        return this;
      }),
    o
  );
  function c(o) {
    return function (c) {
      return (function (o) {
        if (n) throw new TypeError("Generator is already executing.");
        for (; a; )
          try {
            if (
              ((n = 1),
              r &&
                (i =
                  2 & o[0]
                    ? r.return
                    : o[0]
                      ? r.throw || ((i = r.return) && i.call(r), 0)
                      : r.next) &&
                !(i = i.call(r, o[1])).done)
            )
              return i;
            switch (((r = 0), i && (o = [2 & o[0], i.value]), o[0])) {
              case 0:
              case 1:
                i = o;
                break;
              case 4:
                return a.label++, { value: o[1], done: !1 };
              case 5:
                a.label++, (r = o[1]), (o = [0]);
                continue;
              case 7:
                (o = a.ops.pop()), a.trys.pop();
                continue;
              default:
                if (
                  !(i = (i = a.trys).length > 0 && i[i.length - 1]) &&
                  (o[0] === 6 || o[0] === 2)
                ) {
                  a = 0;
                  continue;
                }
                if (o[0] === 3 && (!i || (o[1] > i[0] && o[1] < i[3]))) {
                  a.label = o[1];
                  break;
                }
                if (o[0] === 6 && a.label < i[1]) {
                  (a.label = i[1]), (i = o);
                  break;
                }
                if (i && a.label < i[2]) {
                  (a.label = i[2]), a.ops.push(o);
                  break;
                }
                i[2] && a.ops.pop(), a.trys.pop();
                continue;
            }
            o = t.call(e, a);
          } catch (e) {
            (o = [6, e]), (r = 0);
          } finally {
            n = i = 0;
          }
        if (5 & o[0]) throw o[1];
        return { value: o[0] ? o[1] : void 0, done: !0 };
      })([o, c]);
    };
  }
}
let r = (function () {
  function e() {}
  return (
    (e.add = function (t) {
      e.HANDLE_LIST.push(t), window.addEventListener("message", t, !1);
    }),
    (e.remove = function (t) {
      let n = e.HANDLE_LIST.indexOf(t);
      n >= 0 && e.HANDLE_LIST.splice(n, 1),
        window.removeEventListener("message", t, !1);
    }),
    (e.empty = function () {
      for (; e.HANDLE_LIST.length; )
        window.removeEventListener("message", e.HANDLE_LIST.shift(), !1);
    }),
    (e.parse = function (e) {
      try {
        return typeof e == "object" ? e : e ? JSON.parse(e) : e;
      } catch (t) {
        return console.log("Message.parse Error:", t), e;
      }
    }),
    (e.HANDLE_LIST = []),
    e
  );
})();
function i(e) {
  if (!e) return !1;
  for (var t = e; Object.getPrototypeOf(t) !== null; )
    t = Object.getPrototypeOf(t);
  return Object.getPrototypeOf(e) === t;
}
function o(e) {
  return {}.toString.call(e) === "[object Function]";
}
let a,
  c,
  s,
  u,
  l,
  f = { origin: "" };
function d(e, t) {
  f[e] = t;
}
function p(e) {
  return f[e];
}
function v(e) {
  return !!(function (e, t) {
    return (
      e !== t &&
      (e.replace(/www\./i, "").toLowerCase() !==
        t.replace(/www\./i, "").toLowerCase() ||
        (e.match("www.") ? void 0 : (d("origin", t), !1)))
    );
  })(p("origin"), e.origin);
}
function h(e, t, n) {
  void 0 === n && (n = !1);
  let r = null,
    i = function (n, r) {
      void 0 === n && (n = 0), void 0 === r && (r = 0);
      let i = t.clientHeight,
        o = t.clientWidth;
      e.style.cssText +=
        "height: " + (r || i) + "px; width: " + (n || o) + "px";
    };
  return (
    n &&
      (i = (function (e, t) {
        void 0 === t && (t = 1e3);
        let n = null;
        return function () {
          n && clearTimeout(n),
            (n = setTimeout(function () {
              return e();
            }, t));
        };
      })(i, 200)),
    window.ResizeObserver
      ? (r = new ResizeObserver(function (e) {
          for (let t = 0, n = e; t < n.length; t++) {
            let r = n[t];
            if (r.contentRect) {
              let o = r.contentRect,
                a = o.width,
                c = o.height;
              i.call(void 0, a, c);
            }
          }
        })).observe(t)
      : window.addEventListener("resize", function () {
          return i();
        }),
    function () {
      r == null || r.disconnect(),
        window.removeEventListener("resize", function () {
          return i();
        });
    }
  );
}
!(function (e) {
  (e.Spreadsheet = "s"),
    (e.Writer = "w"),
    (e.Presentation = "p"),
    (e.Pdf = "f"),
    (e.Outline = "o"),
    (e.Dbt = "d");
})(a || (a = {})),
  (function (e) {
    (e.unknown = "unknown"),
      (e.spreadsheet = "s"),
      (e.writer = "w"),
      (e.presentation = "p"),
      (e.pdf = "f");
  })(c || (c = {})),
  (function (e) {
    (e.wps = "w"), (e.et = "s"), (e.presentation = "p"), (e.pdf = "f");
  })(s || (s = {})),
  (function (e) {
    (e.nomal = "nomal"), (e.simple = "simple");
  })(u || (u = {})),
  (function (e) {
    (e[(e.requestFullscreen = 1)] = "requestFullscreen"),
      (e[(e.exitFullscreen = 0)] = "exitFullscreen");
  })(l || (l = {}));
let b,
  m,
  w =
    ((b = 0),
    function () {
      return (b += 1);
    }),
  g = function (e, t, n, r) {
    void 0 === n && (n = !0), void 0 === r && (r = {});
    let i = typeof t == "string" ? document.querySelector(t) : t;
    if (!m) {
      (m = document.createElement("iframe")).classList.add("web-office-iframe");
      let o = {
        id: "office-iframe",
        src: e,
        scrolling: "no",
        frameborder: "0",
        allowfullscreen: "allowfullscreen",
        webkitallowfullscreen: "true",
        mozallowfullscreen: "true",
        allow: "clipboard-read; clipboard-write"
      };
      if (r && Object.keys(r).length && r.allow) {
        let a = r.allow instanceof Array ? r.allow : r.allow.split(";");
        a.unshift("clipboard-write"),
          a.unshift("clipboard-read"),
          (r.allow = a.join(";")),
          Object.assign(o, r);
      }
      for (let c in (i
        ? (o.style =
            "width: " + i.clientWidth + "px; height: " + i.clientHeight + "px;")
        : ((i = document.createElement("div")).classList.add(
            "web-office-default-container"
          ),
          (function (e) {
            let t = document.createElement("style");
            document.head.appendChild(t);
            let n = t.sheet;
            n.insertRule(e, n.cssRules.length);
          })(
            ".web-office-default-container {position: absolute; padding: 0;  margin: 0; width: 100%; height: 100%; left: 0; top: 0;}"
          ),
          document.body.appendChild(i),
          (o.style =
            "position: fixed; top: 0; right: 0; bottom: 0; left: 0; width: 100%; height: 100%;")),
      o))
        m.setAttribute(c, o[c]);
      let s = document.createElement("style");
      (s.textContent =
        "\n      .web-office-iframe {\n        display: block;\n      }\n    "),
        document.head.appendChild(s);
      let u = n ? h(m, i) : Function.prototype;
      i.appendChild(m),
        (m.destroy = function () {
          m.parentNode.removeChild(m), (m = null), u();
        });
    }
    return m;
  },
  y = function (e) {
    g().contentWindow &&
      g().contentWindow.postMessage(JSON.stringify(e), p("origin"));
  };
function k(e, t, n) {
  return new Promise(function (i) {
    let o = w(),
      a = function (e) {
        if (!v(e)) {
          let t = r.parse(e.data);
          t.eventName === n && t.msgId === o && (i(t.data), r.remove(a));
        }
      };
    r.add(a), y({ data: e, msgId: o, eventName: t });
  });
}
let O = function (e) {
    return k(e, "wps.jssdk.api", "wps.api.reply");
  },
  j = function (e) {
    return k(e, "api.basic", "api.basic.reply");
  },
  I = { idMap: {} };
function E(e) {
  return t(this, void 0, void 0, function () {
    let t, i, o, a, c, s, u, l, f, d;
    return n(this, function (n) {
      switch (n.label) {
        case 0:
          return v(e)
            ? [2]
            : ((t = r.parse(e.data)),
              (i = t.eventName),
              (o = t.callbackId),
              (a = t.data),
              o && (c = I.idMap[o])
                ? ((s = c.split(":")),
                  (u = s[0]),
                  (l = s[1]),
                  i === "api.callback" && I[u] && I[u][l]
                    ? [4, (d = I[u][l]).callback.apply(d, a.args)]
                    : [3, 2])
                : [3, 2]);
        case 1:
          (f = n.sent()),
            y({ result: f, callbackId: o, eventName: "api.callback.reply" }),
            (n.label = 2);
        case 2:
          return [2];
      }
    });
  });
}
let S = function (e) {
    return t(void 0, void 0, void 0, function () {
      function t() {
        return Object.keys(I.idMap).find(function (e) {
          return I.idMap[e] === o + ":" + i;
        });
      }
      let i, o, a, c, s, u, l, f, d;
      return n(this, function (n) {
        switch (n.label) {
          case 0:
            return (i = e.prop), (o = e.parentObjId), [4, x([(a = e.value)])];
          case 1:
            return (
              (c = n.sent()),
              (s = c[0]),
              (u = c[1]),
              (e.value = s[0]),
              (l = Object.keys(u)[0]),
              (f = I[o]),
              a === null &&
                f &&
                f[i] &&
                ((d = t()) && delete I.idMap[d],
                delete f[i],
                Object.keys(f).length || delete I[o],
                Object.keys(I.idMap).length || r.remove(E)),
              l &&
                (Object.keys(I.idMap).length || r.add(E),
                I[o] || (I[o] = {}),
                (I[o][i] = { callbackId: l, callback: u[l] }),
                (d = t()) && delete I.idMap[d],
                (I.idMap[l] = o + ":" + i)),
              [2]
            );
        }
      });
    });
  },
  T = function (i, o, a, c) {
    return t(void 0, void 0, void 0, function () {
      let s, u, l, f, d, p, h, b;
      return n(this, function (m) {
        switch (m.label) {
          case 0:
            return (
              (s = w()),
              (f = new Promise(function (e, t) {
                (u = e), (l = t);
              })),
              (d = {}),
              o.args ? [4, x(o.args)] : [3, 2]
            );
          case 1:
            (p = m.sent()),
              (h = p[0]),
              (b = p[1]),
              (o.args = h),
              (d = b),
              (m.label = 2);
          case 2:
            return i !== "api.setter" ? [3, 4] : [4, S(o)];
          case 3:
            m.sent(), (m.label = 4);
          case 4:
            return (
              (function (t) {
                let n = t[0],
                  r = t[1];
                typeof (n = e({}, n)).data == "function" && (n.data = n.data());
                r(), y(n);
              })([
                { eventName: i, data: o, msgId: s },
                function () {
                  let e = this,
                    o = function (f) {
                      return t(e, void 0, void 0, function () {
                        let e, t, p;
                        return n(this, function (n) {
                          switch (n.label) {
                            case 0:
                              return v(f)
                                ? [2]
                                : (e = r.parse(f.data)).eventName ===
                                      "api.callback" &&
                                    e.callbackId &&
                                    d[e.callbackId]
                                  ? [4, d[e.callbackId].apply(d, e.data.args)]
                                  : [3, 2];
                            case 1:
                              (t = n.sent()),
                                y({
                                  result: t,
                                  eventName: "api.callback.reply",
                                  callbackId: e.callbackId
                                }),
                                (n.label = 2);
                            case 2:
                              return (
                                e.eventName === i + ".reply" &&
                                  e.msgId === s &&
                                  (e.error
                                    ? (((p = new Error("")).stack =
                                        e.error + "\n" + a),
                                      c && c(),
                                      l(p))
                                    : u(e.result),
                                  r.remove(o)),
                                [2]
                              );
                          }
                        });
                      });
                    };
                  return r.add(o), f;
                }
              ]),
              [2, f]
            );
        }
      });
    });
  };
function x(e) {
  return t(this, void 0, void 0, function () {
    let t, r, o, a, c, s, u, l, f, d, p;
    return n(this, function (n) {
      switch (n.label) {
        case 0:
          (t = {}), (r = []), (o = e.slice(0)), (n.label = 1);
        case 1:
          return o.length ? ((a = void 0), [4, o.shift()]) : [3, 13];
        case 2:
          return (c = n.sent()) && c.done ? [4, c.done()] : [3, 4];
        case 3:
          n.sent(), (n.label = 4);
        case 4:
          if (!i(a)) return [3, 11];
          for (u in ((a = {}), (s = []), c)) s.push(u);
          (l = 0), (n.label = 5);
        case 5:
          return l < s.length
            ? ((f = s[l]),
              (d = c[f]),
              /^[A-Z]/.test(f)
                ? d && d.done
                  ? [4, d.done()]
                  : [3, 7]
                : [3, 8])
            : [3, 10];
        case 6:
          n.sent(), (n.label = 7);
        case 7:
          d && d.objId
            ? (d = { objId: d.objId })
            : typeof d == "function" &&
              ((p = w()), (t[p] = d), (d = { callbackId: p })),
            (n.label = 8);
        case 8:
          (a[f] = d), (n.label = 9);
        case 9:
          return l++, [3, 5];
        case 10:
          return [3, 12];
        case 11:
          c && c.objId
            ? (a = { objId: c.objId })
            : typeof c == "function" && void 0 === c.objId
              ? ((p = w()), (t[p] = c), (a = { callbackId: p }))
              : (a = c),
            (n.label = 12);
        case 12:
          return r.push(a), [3, 1];
        case 13:
          return [2, [r, t]];
      }
    });
  });
}
let _ = function (t, n) {
    void 0 === n && (n = !0);
    let r = e({}, t),
      i = r.headers,
      o = void 0 === i ? {} : i,
      a = r.subscriptions,
      c = void 0 === a ? {} : a,
      s = r.mode,
      l = void 0 === s ? u.nomal : s,
      f = r.commonOptions,
      d = o.backBtn,
      p = void 0 === d ? {} : d,
      v = o.shareBtn,
      h = void 0 === v ? {} : v,
      b = o.otherMenuBtn,
      m = void 0 === b ? {} : b,
      w = function (e, t) {
        e.subscribe &&
          typeof e.subscribe == "function" &&
          ((e.callback = t), (c[t] = e.subscribe), n && delete e.subscribe);
      };
    if (
      (w(p, "wpsconfig_back_btn"),
      w(h, "wpsconfig_share_btn"),
      w(m, "wpsconfig_other_menu_btn"),
      m.items && Array.isArray(m.items))
    ) {
      let g = [];
      m.items.forEach(function (e, t) {
        switch ((void 0 === e && (e = {}), e.type)) {
          case "export_img":
            (e.type = 1), (e.callback = "export_img");
            break;
          case "export_pdf":
            (e.type = 1), (e.callback = "export_pdf");
            break;
          case "save_version":
            (e.type = 1), (e.callback = "save_version");
            break;
          case "about_wps":
            (e.type = 1), (e.callback = "about_wps");
            break;
          case "split_line":
            e.type = 2;
            break;
          case "custom":
            (e.type = 3), w(e, "wpsconfig_other_menu_btn_" + t), g.push(e);
        }
      }),
        g.length && (D || P) && (m.items = g);
    }
    r.url = r.url || r.wpsUrl;
    let y = [];
    if (
      ((l === u.simple || (f && !1 === f.isShowTopArea)) &&
        y.push("simple", "hidecmb"),
      r.debug && y.push("debugger"),
      r.url &&
        y.length &&
        (r.url = r.url + (r.url.indexOf("?") >= 0 ? "&" : "?") + y.join("&")),
      f &&
        (f.isParentFullscreen || f.isBrowserViewFullscreen) &&
        (document.addEventListener("fullscreenchange", F),
        document.addEventListener("webkitfullscreenchange", F),
        document.addEventListener("mozfullscreenchange", F)),
      r.wordOptions && (r.wpsOptions = r.wordOptions),
      r.excelOptions && (r.etOptions = r.excelOptions),
      r.pptOptions && (r.wppOptions = r.pptOptions),
      typeof c.print == "object")
    ) {
      var k = "wpsconfig_print";
      typeof c.print.subscribe == "function" &&
        ((c[k] = c.print.subscribe),
        (r.print = { callback: k }),
        void 0 !== c.print.custom && (r.print.custom = c.print.custom)),
        delete c.print;
    }
    typeof c.exportPdf == "function" &&
      ((c[(k = "wpsconfig_export_pdf")] = c.exportPdf),
      (r.exportPdf = { callback: k }),
      delete c.exportPdf);
    return (
      r.commandBars && N(r.commandBars, !1), e(e({}, r), { subscriptions: c })
    );
  },
  C = function (t) {
    let n = e({}, t);
    n.fileToken = n.token || n.fileToken;
    let r = n.appId,
      o = n.fileId,
      c = n.officeType,
      s = n.fileToken,
      u = n.endpoint,
      l = n.customArgs,
      f = void 0 === l ? "" : l;
    if (!Object.values(a).includes(c))
      throw new Error(
        "[WebOfficeSDK.init] officeType属性值错误，可选值参考WebOfficeSDK.OfficeType: " +
          JSON.stringify(a)
      );
    let d = s ? 1 : 0,
      p = { token: "" };
    if (
      (typeof s == "string"
        ? (p.token = s)
        : i(s) && (p = e({ tokenData: p }, s)),
      d && !p.token)
    )
      return console.error("[WebOfficeSDK.init] token设置无效"), n;
    if (typeof u != "string")
      throw new Error("[WebOfficeSDK.init] endpoint期望为字符串");
    if (u && !u.startsWith("http"))
      return (
        console.error("[WebOfficeSDK.init] endpoint仅支持http、https的地址"), n
      );
    if (
      ((n.url =
        u + "/office/" + c + "/" + o + "?_w_appid=" + r + "&_w_tokentype=" + d),
      i(f))
    ) {
      let v = Object.entries(f)
        .map(function (e) {
          let t = e[0],
            n = e[1];
          return t + "=" + encodeURIComponent(n);
        })
        .join("&");
      n.url = n.url + "&" + v;
    }
    return e({}, n);
  },
  A = function (e) {
    void 0 === e && (e = "");
    let t = "";
    if (!t && e) {
      let n = e.toLowerCase();
      n.indexOf("/office/s/") !== -1 && (t = c.spreadsheet),
        n.indexOf("/office/w/") !== -1 && (t = c.writer),
        n.indexOf("/office/p/") !== -1 && (t = c.presentation),
        n.indexOf("/office/f/") !== -1 && (t = c.pdf);
    }
    if (!t) {
      let r = e.match(/[?&]type=([a-z]+)/) || [];
      t = s[r[1]] || "";
    }
    return t;
  };
function N(e, t) {
  void 0 === t && (t = !0);
  let n = e.map(function (e) {
    let t = e.attributes;
    if (!Array.isArray(t)) {
      let n = [];
      for (let r in t)
        if (t.hasOwnProperty(r)) {
          let i = { name: r, value: t[r] };
          n.push(i);
        }
      e.attributes = n;
    }
    return e;
  });
  return t && y({ data: n, eventName: "setCommandBars" }), n;
}
var L = window.navigator.userAgent.toLowerCase(),
  D = /Android|webOS|iPhone|iPod|BlackBerry|iPad/i.test(L),
  P = (function () {
    try {
      return (
        window._parent.location.search.indexOf("from=wxminiprogram") !== -1
      );
    } catch (e) {
      return !1;
    }
  })();
function F() {
  let e = { status: l.requestFullscreen },
    t = document,
    n =
      t.fullscreenElement ||
      t.webkitFullscreenElement ||
      t.mozFullScreenElement;
  (e.status = n ? l.requestFullscreen : l.exitFullscreen),
    y({ data: e, eventName: "fullscreenchange" });
}
let W = function () {
  I.idMap = {};
};
function R() {
  console.group("JSSDK 事件机制调整说明"),
    console.warn(
      "jssdk.on、jssdk.off 和 jssdk.Application.Sub 将在后续版本中被弃用，建议使用改进后的 ApiEvent"
    ),
    console.warn(
      "具体请参考：https://wwo.wps.cn/docs/front-end/basic-usage/events/intro/"
    ),
    console.groupEnd();
}
let B = 0,
  V = !0,
  z = new Set();
function H(e) {
  return (
    (B += 1),
    !e &&
      (function (e) {
        z.forEach(function (t) {
          return t(e);
        });
      })(B),
    B
  );
}
function K() {
  let e = new Error("");
  return (e.stack || e.message || "").split("\n").slice(2).join("\n");
}
function M(i, o) {
  let a = this;
  V = !1;
  let s,
    u = o.Events,
    l = o.Enum,
    f = o.Props,
    d = f[0],
    p = f[1],
    h = { objId: B };
  switch (
    ((function t(n, r, i) {
      let o = r.slice(0);
      let a = function () {
        let r = o.shift();
        !r.alias &&
          ~q.indexOf(r.prop) &&
          o.push(e(e({}, r), { alias: r.prop + "Async" })),
          Object.defineProperty(n, r.alias || r.prop, {
            get: function () {
              let o = this;
              if (!V) {
                let a = r.cache === 1,
                  c = a && this["__" + r.prop + "CacheValue"];
                if (!c) {
                  let s = K(),
                    u = H(a),
                    l = function () {
                      for (var t, o = [], a = 0; a < arguments.length; a++)
                        o[a] = arguments[a];
                      void 0 !== r.caller
                        ? (function t(n, r, i) {
                            let o = r.slice(0);
                            let a = function () {
                              let r = o.shift();
                              !r.alias &&
                                ~q.indexOf(r.prop) &&
                                o.push(
                                  e(e({}, r), { alias: r.prop + "Async" })
                                ),
                                Object.defineProperty(n, r.alias || r.prop, {
                                  get: function () {
                                    let e = this;
                                    if (!V) {
                                      let o = r.cache === 1,
                                        a =
                                          o &&
                                          this["__" + r.prop + "CacheValue"];
                                      if (!a) {
                                        let c = K(),
                                          s = H(o),
                                          u = function () {
                                            for (
                                              var e, o = [], a = 0;
                                              a < arguments.length;
                                              a++
                                            )
                                              o[a] = arguments[a];
                                            void 0 !== r.caller
                                              ? t(
                                                  (e = { objId: H() }),
                                                  i[r.caller],
                                                  i
                                                )
                                              : (e = {});
                                            return (
                                              G(
                                                u,
                                                e,
                                                "api.caller",
                                                {
                                                  obj: u,
                                                  args: o,
                                                  parentObjId: n.objId,
                                                  objId: e.objId,
                                                  prop: r.prop
                                                },
                                                c
                                              ),
                                              e
                                            );
                                          };
                                        return (
                                          (u.objId = -1),
                                          void 0 !== r.getter &&
                                            ((u.objId = s),
                                            t(u, i[r.getter], i)),
                                          G(
                                            n,
                                            u,
                                            "api.getter",
                                            {
                                              parentObjId: n.objId,
                                              objId: u.objId,
                                              prop: r.prop
                                            },
                                            c,
                                            function () {
                                              delete e[
                                                "__" + r.prop + "CacheValue"
                                              ];
                                            }
                                          ),
                                          o &&
                                            (this[
                                              "__" + r.prop + "CacheValue"
                                            ] = u),
                                          u
                                        );
                                      }
                                      return a;
                                    }
                                  },
                                  set: function (e) {
                                    if (!V) {
                                      let t = K();
                                      return G(
                                        n,
                                        {},
                                        "api.setter",
                                        {
                                          value: e,
                                          parentObjId: n.objId,
                                          objId: -1,
                                          prop: r.prop
                                        },
                                        t
                                      );
                                    }
                                  }
                                });
                            };
                            for (; o.length; ) a();
                          })((t = { objId: H() }), i[r.caller], i)
                        : (t = {});
                      return (
                        G(
                          l,
                          t,
                          "api.caller",
                          {
                            obj: l,
                            args: o,
                            parentObjId: n.objId,
                            objId: t.objId,
                            prop: r.prop
                          },
                          s
                        ),
                        t
                      );
                    };
                  return (
                    (l.objId = -1),
                    void 0 !== r.getter &&
                      ((l.objId = u), t(l, i[r.getter], i)),
                    G(
                      n,
                      l,
                      "api.getter",
                      { parentObjId: n.objId, objId: l.objId, prop: r.prop },
                      s,
                      function () {
                        delete o["__" + r.prop + "CacheValue"];
                      }
                    ),
                    a && (this["__" + r.prop + "CacheValue"] = l),
                    l
                  );
                }
                return c;
              }
            },
            set: function (e) {
              if (!V) {
                let t = K();
                return G(
                  n,
                  {},
                  "api.setter",
                  { value: e, parentObjId: n.objId, objId: -1, prop: r.prop },
                  t
                );
              }
            }
          });
      };
      for (; o.length; ) a();
    })(h, d, p),
    (h.Events = u),
    (h.Enum = l),
    (i.Enum = h.Enum),
    (i.Events = h.Events),
    (i.Props = f),
    A(i.url))
  ) {
    case c.writer:
      i.WordApplication = i.WpsApplication = function () {
        return h;
      };
      break;
    case c.spreadsheet:
      i.ExcelApplication = i.EtApplication = function () {
        return h;
      };
      break;
    case c.presentation:
      i.PPTApplication = i.WppApplication = function () {
        return h;
      };
      break;
    case c.pdf:
      i.PDFApplication = function () {
        return h;
      };
  }
  (i.Application = h),
    (i.Free = function (e) {
      return T("api.free", { objId: e }, "");
    }),
    (i.Stack = h.Stack =
      ((s = function (e) {
        i && i.Free(e);
      }),
      function () {
        let e = [],
          t = function (t) {
            e.push(t);
          };
        return (
          z.add(t),
          {
            End: function () {
              s(e), z.delete(t);
            }
          }
        );
      }));
  let b = {};
  r.add(function (e) {
    return t(a, void 0, void 0, function () {
      let t, i, o, a, c;
      return n(this, function (n) {
        switch (n.label) {
          case 0:
            return v(e)
              ? [2]
              : (t = r.parse(e.data)).eventName === "api.event" && t.data
                ? ((i = t.data),
                  (o = i.eventName),
                  (a = i.data),
                  (c = b[o]) ? [4, c(a)] : [3, 2])
                : [3, 2];
          case 1:
            n.sent(), (n.label = 2);
          case 2:
            return [2];
        }
      });
    });
  }),
    (h.Sub = {});
  let m = function (e) {
    let t = u[e];
    Object.defineProperty(h.Sub, t, {
      set: function (e) {
        R(),
          (b[t] = e),
          y({
            eventName: "api.event.register",
            data: { eventName: t, register: !!e, objId: (B += 1) }
          });
      }
    });
  };
  for (let w in u) m(w);
}
var q = [
  "ExportAsFixedFormat",
  "GetOperatorsInfo",
  "ImportDataIntoFields",
  "ReplaceText",
  "ReplaceBookmark",
  "GetBookmarkText",
  "GetComments"
];
function J(t, n, r) {
  let i = n.slice(0);
  let o = function () {
    let n = i.shift();
    if (!n.alias && ~q.indexOf(n.prop)) {
      i.push(e(e({}, n), { alias: n.prop + "Async" }));
    }
    Object.defineProperty(t, n.alias || n.prop, {
      get: function () {
        let e = this;
        if (V) {
          return;
        }
        let i = n.cache === 1;
        let o = i && this["__" + n.prop + "CacheValue"];
        if (!o) {
          let a = K();
          let c = H(i);
          let s = function () {
            for (var e = [], i = 0, o; i < arguments.length; i++) {
              e[i] = arguments[i];
            }
            if (n.caller !== undefined) {
              o = { objId: H() };
              J(o, r[n.caller], r);
            } else {
              o = {};
            }
            return (
              G(
                u,
                o,
                "api.caller",
                {
                  obj: u,
                  args: e,
                  parentObjId: t.objId,
                  objId: o.objId,
                  prop: n.prop
                },
                a
              ),
              o
            );
          };
          var u = s;
          u.objId = -1;
          if (n.getter !== undefined) {
            u.objId = c;
            J(u, r[n.getter], r);
          }
          G(
            t,
            u,
            "api.getter",
            { parentObjId: t.objId, objId: u.objId, prop: n.prop },
            a,
            function () {
              delete e["__" + n.prop + "CacheValue"];
            }
          );
          if (i) {
            this["__" + n.prop + "CacheValue"] = u;
          }
          return u;
        }
        return o;
      },
      set: function (e) {
        if (V) {
          return;
        }
        let r = K();
        return G(
          t,
          {},
          "api.setter",
          { value: e, parentObjId: t.objId, objId: -1, prop: n.prop },
          r
        );
      }
    });
  };
  while (i.length) {
    o();
  }
}
function G(e, t, n, r, i, o) {
  let a,
    c = (e.done ? e.done() : Promise.resolve()).then(function () {
      return a || (a = T(n, r, i, o)), a;
    });
  (t.done = function () {
    return c;
  }),
    (t.then = function (e, n) {
      return r.objId >= 0
        ? ((t.then = null),
          (t.catch = null),
          c
            .then(function () {
              e(t);
            })
            .catch(function (e) {
              return n(e);
            }))
        : c.then(e, n);
    }),
    (t.catch = function (e) {
      return c.catch(e);
    }),
    (t.Destroy = function () {
      return T("api.free", { objId: t.objId }, "");
    });
}
let U = {};
let Z = null,
  Q = {
    fileOpen: "fileOpen",
    tabSwitch: "tabSwitch",
    fileSaved: "fileSaved",
    fileStatus: "fileStatus",
    fullscreenChange: "fullscreenChange",
    error: "error",
    stage: "stage"
  },
  X = {
    getToken: "api.getToken",
    onToast: "event.toast",
    onHyperLinkOpen: "event.hyperLinkOpen",
    getClipboardData: "api.getClipboardData"
  };
function Y(i, o, a, c, s, u, l) {
  let f = this;
  void 0 === a && (a = {});
  r.add(function (l) {
    return t(f, void 0, void 0, function () {
      let t, f, d, p, h, b, m, w, g, k, O, j, I, E, S, T, x, _, C;
      return n(this, function (n) {
        switch (n.label) {
          case 0:
            return v(l)
              ? [2]
              : ((t = r.parse(l.data)),
                (f = t.eventName),
                (d = void 0 === f ? "" : f),
                (p = t.data),
                (h = void 0 === p ? null : p),
                (b = t.url),
                (m = void 0 === b ? null : b),
                ["wps.jssdk.api"].indexOf(d) !== -1
                  ? [2]
                  : d !== "ready"
                    ? [3, 1]
                    : (s.apiReadySended &&
                        (function (e) {
                          let t = [];
                          Object.keys(U).forEach(function (n) {
                            U[n].forEach(function (r) {
                              let i = n;
                              e.off(i, r), t.push({ handle: r, eventName: i });
                            }),
                              delete U[n];
                          }),
                            t.forEach(function (e) {
                              let t = e.eventName,
                                n = e.handle;
                              Z == null || Z.ApiEvent.AddApiEventListener(t, n);
                            });
                        })(o),
                      y({
                        eventName: "setConfig",
                        data: e(e({}, a), { version: i.version })
                      }),
                      i.tokenData &&
                        i.setToken(
                          e(e({}, i.tokenData), {
                            hasRefreshTokenConfig: !!a.refreshToken
                          })
                        ),
                      (i.iframeReady = !0),
                      [3, 15]));
          case 1:
            return d !== "error" ? [3, 2] : (o.emit(Q.error, h), [3, 15]);
          case 2:
            return d !== "open.result"
              ? [3, 3]
              : (void 0 !==
                  ((x = h == null ? void 0 : h.fileInfo) === null ||
                  void 0 === x
                    ? void 0
                    : x.officeVersion) &&
                  ((i.mainVersion = h.fileInfo.officeVersion),
                  console.log("WebOfficeSDK Main Version: V" + i.mainVersion)),
                o.emit(Q.fileOpen, h),
                [3, 15]);
          case 3:
            return d !== "api.scroll"
              ? [3, 4]
              : (window.scrollTo(h.x, h.y), [3, 15]);
          case 4:
            if (d !== X.getToken) return [3, 9];
            (w = { token: !1 }), (n.label = 5);
          case 5:
            return n.trys.push([5, 7, , 8]), [4, s.refreshToken()];
          case 6:
            return (w = n.sent()), [3, 8];
          case 7:
            return (
              (g = n.sent()),
              console.error("refreshToken: " + (g || "fail to get")),
              [3, 8]
            );
          case 8:
            return y({ eventName: X.getToken + ".reply", data: w }), [3, 15];
          case 9:
            if (d !== X.getClipboardData) return [3, 14];
            (k = { text: "", html: "" }), (n.label = 10);
          case 10:
            return n.trys.push([10, 12, , 13]), [4, s.getClipboardData()];
          case 11:
            return (k = n.sent()), [3, 13];
          case 12:
            return (
              (O = n.sent()),
              console.error("getClipboardData: " + (O || "fail to get")),
              [3, 13]
            );
          case 13:
            return (
              y({ eventName: X.getClipboardData + ".reply", data: k }), [3, 15]
            );
          case 14:
            d === X.onToast
              ? s.onToast(h)
              : d === X.onHyperLinkOpen
                ? s.onHyperLinkOpen(h)
                : d === "stage"
                  ? o.emit(Q.stage, h)
                  : d === "event.callback"
                    ? ((j = h.eventName),
                      (I = h.data),
                      (E = j),
                      j === "fullScreenChange" && (E = Q.fullscreenChange),
                      j === "file.saved" && (E = Q.fileStatus),
                      (((_ = a.commonOptions) === null || void 0 === _
                        ? void 0
                        : _.isBrowserViewFullscreen) ||
                        ((C = a.commonOptions) === null || void 0 === C
                          ? void 0
                          : C.isParentFullscreen)) &&
                        E === "fullscreenchange" &&
                        ((S = I.status),
                        (T = I.isDispatchEvent),
                        a.commonOptions.isBrowserViewFullscreen
                          ? (function (e, t, n, r) {
                              if (e === 0) {
                                if (t.dataset.pastSize) {
                                  let i = JSON.parse(t.dataset.pastSize),
                                    o = i.width,
                                    a = i.height;
                                  delete t.dataset.pastSize,
                                    (t.style =
                                      "position: static; width: " +
                                      o +
                                      "; height: " +
                                      a);
                                }
                              } else
                                e === 1 &&
                                  ((t.dataset.pastSize = JSON.stringify({
                                    width: t.clientWidth + "px",
                                    height: t.clientHeight + "px"
                                  })),
                                  (t.style =
                                    "position: fixed; width: 100%; height: 100%; top: 0; left: 0; z-index:999;"));
                              r &&
                                (function (e) {
                                  ["fullscreen", "fullscreenElement"].forEach(
                                    function (t) {
                                      Object.defineProperty(document, t, {
                                        get: function () {
                                          return !!e.status;
                                        },
                                        configurable: !0
                                      });
                                    }
                                  );
                                  let t = new CustomEvent("fullscreenchange");
                                  document.dispatchEvent(t);
                                })({ status: e });
                            })(S, u, 0, T)
                          : a.commonOptions.isParentFullscreen &&
                            (function (e, t, n) {
                              let r = document.querySelector(n),
                                i = r && r.nodeType === 1 ? r : t;
                              if (e === 0) {
                                let o = document,
                                  a =
                                    o.exitFullscreen ||
                                    o.mozCancelFullScreen ||
                                    o.msExitFullscreen ||
                                    o.webkitCancelFullScreen ||
                                    o.webkitExitFullscreen;
                                a.call(document);
                              } else if (e === 1) {
                                let c =
                                  i.requestFullscreen ||
                                  i.mozRequestFullScreen ||
                                  i.msRequestFullscreen ||
                                  i.webkitRequestFullscreen;
                                c.call(i);
                              }
                            })(S, u, a.commonOptions.isParentFullscreen)),
                      o.emit(E, I))
                    : d === "api.ready" && M(i, h),
              (n.label = 15);
          case 15:
            return typeof c[d] == "function" && c[d](i, m || h), [2];
        }
      });
    });
  });
}
function $(e) {
  return new Promise(function (t) {
    let n = function (i) {
      v(i) || (r.parse(i.data).eventName === e && (t(), r.remove(n)));
    };
    r.add(n);
  });
}
function ee(a) {
  let c,
    s = this;
  void 0 === a && (a = {}), Z && Z.destroy();
  try {
    let u = _(a),
      l = u.subscriptions,
      f = void 0 === l ? {} : l,
      p = u.mount,
      v = void 0 === p ? null : p,
      h = u.url,
      b = u.refreshToken,
      m = u.onToast,
      w = u.onHyperLinkOpen,
      k = u.getClipboardData,
      I = u.attrAllow,
      E = u.isListenResize;
    d("origin", (h.match(/https*:\/\/[^/]+/g) || [])[0]);
    let S = g(h, v, E == null || E, { allow: I }),
      T = $("ready"),
      x = $("open.result"),
      C = $("api.ready");
    v && (v.clientWidth, v.clientHeight);
    delete u.mount, h && delete u.url, delete u.subscriptions;
    let A =
        ((c = c || Object.create(null)),
        {
          on: function (e, t) {
            (c[e] || (c[e] = [])).push(t);
          },
          off: function (e, t) {
            c[e] && c[e].splice(c[e].indexOf(t) >>> 0, 1);
          },
          emit: function (e, t) {
            (c[e] || []).slice().map(function (e) {
              e(t);
            }),
              (c["*"] || []).slice().map(function (n) {
                n(e, t);
              });
          }
        }),
      L = { apiReadySended: !1 },
      D = function (e, r, i) {
        return t(s, void 0, void 0, function () {
          return n(this, function (t) {
            switch (t.label) {
              case 0:
                return (function (e, t, n) {
                  if (U[e]) {
                    let r = !!U[e].find(function (e) {
                      return e === t;
                    });
                    return r && n === "off"
                      ? (A.off(e, t),
                        (U[e] = U[e].filter(function (e) {
                          return e !== t;
                        })),
                        !!U[e].length || ((U[e] = void 0), !1))
                      : (r || n !== "on" || (U[e].push(t), A.on(e, t)), !0);
                  }
                  return n === "on"
                    ? ((U[e] = []), U[e].push(t), !1)
                    : n === "off" || void 0;
                })(e, r, i)
                  ? [3, 2]
                  : [4, T];
              case 1:
                t.sent(),
                  (function (e, t) {
                    let n = e.eventName,
                      r = e.type,
                      i = e.handle;
                    t === "on" ? A.on(n, i) : A.off(n, i),
                      r === "base.event" &&
                        y({
                          eventName: "basic.event",
                          data: { eventName: n, action: t }
                        }),
                      R();
                  })(
                    (function (e, t) {
                      let n = e,
                        r = "base.event";
                      switch (n) {
                        case Q.fileSaved:
                          console.warn(
                            "fileSaved事件监听即将弃用， 推荐使用fileStatus进行文件状态的监听"
                          ),
                            (n = "fileStatus");
                          break;
                        case Q.fullscreenChange:
                          n = "fullscreenchange";
                          break;
                        case "error":
                        case "fileOpen":
                          r = "callback.event";
                      }
                      return { eventName: n, type: r, handle: t };
                    })(e, r),
                    i
                  ),
                  (t.label = 2);
              case 2:
                return [2];
            }
          });
        });
      };
    return (
      (Z = {
        url: h,
        iframe: S,
        version: "1.1.27",
        iframeReady: !1,
        tokenData: null,
        commandBars: null,
        tabs: {
          getTabs: function () {
            return t(this, void 0, void 0, function () {
              return n(this, function (e) {
                switch (e.label) {
                  case 0:
                    return [4, T];
                  case 1:
                    return e.sent(), [2, j({ api: "tab.getTabs" })];
                }
              });
            });
          },
          switchTab: function (e) {
            return t(this, void 0, void 0, function () {
              return n(this, function (t) {
                switch (t.label) {
                  case 0:
                    return [4, T];
                  case 1:
                    return (
                      t.sent(),
                      [2, j({ api: "tab.switchTab", args: { tabKey: e } })]
                    );
                }
              });
            });
          }
        },
        setCooperUserColor: function (e) {
          return t(this, void 0, void 0, function () {
            return n(this, function (t) {
              switch (t.label) {
                case 0:
                  return [4, T];
                case 1:
                  return (
                    t.sent(), [2, j({ api: "setCooperUserColor", args: e })]
                  );
              }
            });
          });
        },
        setToken: function (e) {
          return t(this, void 0, void 0, function () {
            return n(this, function (t) {
              switch (t.label) {
                case 0:
                  return [4, T];
                case 1:
                  return (
                    t.sent(),
                    (Z.tokenData = e),
                    y({ eventName: "setToken", data: e }),
                    [2]
                  );
              }
            });
          });
        },
        ready: function () {
          return t(this, void 0, void 0, function () {
            return n(this, function (e) {
              switch (e.label) {
                case 0:
                  return L.apiReadySended ? [3, 2] : [4, x];
                case 1:
                  e.sent(),
                    (L.apiReadySended = !0),
                    y({ eventName: "api.ready" }),
                    (e.label = 2);
                case 2:
                  return [4, C];
                case 3:
                  return (
                    e.sent(),
                    [
                      2,
                      new Promise(function (e) {
                        return setTimeout(function () {
                          return e(Z == null ? void 0 : Z.Application);
                        }, 0);
                      })
                    ]
                  );
              }
            });
          });
        },
        destroy: function () {
          (U = {}),
            S.destroy(),
            r.empty(),
            (Z = null),
            (z = new Set()),
            (B = 0),
            (V = !0),
            document.removeEventListener("fullscreenchange", F),
            document.removeEventListener("webkitfullscreenchange", F),
            document.removeEventListener("mozfullscreenchange", F),
            W();
        },
        save: function () {
          return t(this, void 0, void 0, function () {
            return n(this, function (e) {
              switch (e.label) {
                case 0:
                  return [4, T];
                case 1:
                  return e.sent(), [2, O({ api: "save" })];
              }
            });
          });
        },
        setCommandBars: function (e) {
          return t(this, void 0, void 0, function () {
            return n(this, function (t) {
              switch (t.label) {
                case 0:
                  return [4, T];
                case 1:
                  return t.sent(), N(e), [2];
              }
            });
          });
        },
        updateConfig: function (e) {
          return (
            void 0 === e && (e = {}),
            t(this, void 0, void 0, function () {
              return n(this, function (t) {
                switch (t.label) {
                  case 0:
                    return [4, T];
                  case 1:
                    return (
                      t.sent(),
                      e.commandBars
                        ? (console.warn(
                            "Deprecated: `updateConfig()` 方法即将废弃，请使用`setCommandBars()`代替`updateConfig()`更新`commandBars`配置。"
                          ),
                          [4, N(e.commandBars)])
                        : [3, 3]
                    );
                  case 2:
                    t.sent(), (t.label = 3);
                  case 3:
                    return [2];
                }
              });
            })
          );
        },
        executeCommandBar: function (e) {
          return t(this, void 0, void 0, function () {
            return n(this, function (t) {
              switch (t.label) {
                case 0:
                  return [4, T];
                case 1:
                  return (
                    t.sent(),
                    N([
                      { cmbId: e, attributes: [{ name: "click", value: !0 }] }
                    ]),
                    [2]
                  );
              }
            });
          });
        },
        on: function (e, r) {
          return t(this, void 0, void 0, function () {
            return n(this, function (t) {
              return [2, this.ApiEvent.AddApiEventListener(e, r)];
            });
          });
        },
        off: function (e, r) {
          return t(this, void 0, void 0, function () {
            return n(this, function (t) {
              return [2, this.ApiEvent.RemoveApiEventListener(e, r)];
            });
          });
        },
        ApiEvent: {
          AddApiEventListener: function (e, r) {
            return t(this, void 0, void 0, function () {
              return n(this, function (t) {
                switch (t.label) {
                  case 0:
                    return [4, D(e, r, "on")];
                  case 1:
                    return [2, t.sent()];
                }
              });
            });
          },
          RemoveApiEventListener: function (e, r) {
            return t(this, void 0, void 0, function () {
              return n(this, function (t) {
                switch (t.label) {
                  case 0:
                    return [4, D(e, r, "off")];
                  case 1:
                    return [2, t.sent()];
                }
              });
            });
          }
        }
      }),
      (function (e, t, n, r, i, a) {
        t &&
          o(t) &&
          ((i.refreshToken = t), (e.refreshToken = { eventName: X.getToken }));
        a &&
          o(a) &&
          ((i.getClipboardData = a),
          (e.getClipboardData = { eventName: X.getClipboardData }));
        n && o(n) && ((i.onToast = n), (e.onToast = { eventName: X.onToast }));
        r &&
          o(r) &&
          ((i.onHyperLinkOpen = r),
          (e.onHyperLinkOpen = { eventName: X.onHyperLinkOpen }));
      })(u, b, m, w, L, k),
      Y(Z, A, u, f, L, S),
      u.fileToken &&
        (i(u.fileToken)
          ? Z.setToken(e({}, u.fileToken))
          : Z.setToken({ token: u.fileToken })),
      Z
    );
  } catch (e) {
    console.error(e);
  }
}
function te(t) {
  let n,
    r,
    i = e({}, t);
  if (i.url)
    throw new Error(
      "[WebOfficeSDK.init] 不支持传递url，请使用appId、fileId、officeType、token等参数初始化！"
    );
  if (!i.appId || !i.fileId || !i.officeType)
    throw new Error("[WebOfficeSDK.init] appId、fileId、officeType为必选项！");
  if (
    void 0 !== i.token &&
    typeof i.token != "string" &&
    typeof ((n = i.token) === null || void 0 === n ? void 0 : n.token) !=
      "string"
  )
    throw new Error("[WebOfficeSDK.init] token类型必须为字符串！");
  if (
    void 0 !== i.fileToken &&
    typeof i.fileToken != "string" &&
    typeof ((r = i.fileToken) === null || void 0 === r ? void 0 : r.token) !=
      "string"
  )
    throw new Error("[WebOfficeSDK.init] fileToken类型必须为字符串！");
  return (i.endpoint = i.endpoint || "https://o.wpsgo.com"), ee(C(i));
}
console.log("WebOfficeSDK JS-SDK V1.1.27");
let ne = Object.freeze({
  __proto__: null,
  listener: Y,
  config: ee,
  createApp: te,
  get OfficeType() {
    return a;
  }
});
window.WPS = ne;
let re = ee,
  ie = te,
  oe = a;
export default { config: ee, init: te, OfficeType: a };
export { re as config, ie as init, oe as OfficeType };
