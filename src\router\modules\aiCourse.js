import { aiCourse } from "@/router/enums";
import { aiCodesList } from "@/router/accidCode.js";
import { aiNewPage } from "@/utils/aiTool.js";
import AIIcon from "@/assets/home/<USER>";
import AIIconActive from "@/assets/home/<USER>";

const Layout = () => import("@/layout/index.vue");
// 创建一个空白组件，用于路由配置
const EmptyComponent = {
  render() {
    return null;
  },
  beforeRouteEnter(to, from, next) {
    // 在导航进入该组件对应的路由时调用aiNewPage函数
    aiNewPage();
    // 阻止实际导航，保持在当前页面
    next(false);
  }
};

export default {
  path: "/ai-course",
  name: "AICourse",
  component: Layout,
  redirect: "/ai-course",
  meta: {
    icon: "ep:magic-stick", // 使用Element Plus的魔法棒图标
    imgIcon: AIIcon,
    imgIconActive: AIIconActive,
    title: "AI课程设计",
    rank: aiCourse,
    idCode: aiCodesList.baseCode,
    showLink: true
  },
  children: [
    {
      path: "/ai-course",
      name: "AICourseDesign",
      component: EmptyComponent,
      meta: {
        title: "AI课程设计",
        idCode: aiCodesList.pending,
        showLink: true
      }
    }
  ]
};
