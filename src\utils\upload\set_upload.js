import { http } from "@/utils/http/index";

// https://api.ebag-test.readboy.com/education-cube

//根据文件唯一标识符获取上传文件
export const getByIdentifier = params => {
  //   console.log('🍪-----params-----', params);
  return http.request(
    "get",
    "/file/getByIdentifier",
    { params },
    {
      isNeedFullRes: false, // 是否需要返回完整的响应对象
      isShowLoading: true, // 是否显示loading
      isNeedToken: false // 是否需要token
    }
  );
};

//初始化上传，生成uploadId
export const initUpload = data => {
  return http.request(
    "POST",
    "/file/initUpload",
    { data },
    {
      isNeedFullRes: false, // 是否需要返回完整的响应对象
      isShowLoading: true, // 是否显示loading
      isNeedToken: false // 是否需要token
    }
  );
};
//简单上传
export const isupload = data => {
  return http.request(
    "POST",
    "/file/upload",
    { data },
    {
      isNeedFullRes: false, // 是否需要返回完整的响应对象
      isShowLoading: true, // 是否显示loading
      isNeedToken: false // 是否需要token
    }
  );
};

//获取已上传的分片
export const findFilePart = params => {
  //   console.log('🍪-----params-----', params);
  return http.request(
    "get",
    "/file/findFilePart",
    { params },
    {
      isNeedFullRes: false, // 是否需要返回完整的响应对象
      isShowLoading: true, // 是否显示loading
      isNeedToken: false // 是否需要token
    }
  );
};

//分片上传
export const chunkUpload = data => {
  return http.request(
    "POST",
    `/file/chunkUpload`,
    { data },
    {
      isNeedFullRes: false, // 是否需要返回完整的响应对象
      isShowLoading: true, // 是否显示loading
      isNeedToken: false // 是否需要token
    }
  );
};
//合并分片
export const merge = data => {
  return http.request(
    "POST",
    "/file/merge",
    { data },
    {
      isNeedFullRes: false, // 是否需要返回完整的响应对象
      isShowLoading: true, // 是否显示loading
      isNeedToken: false // 是否需要token
    }
  );
};
