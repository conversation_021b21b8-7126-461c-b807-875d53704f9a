import { SM4 } from "gm-crypto";

const key = "61613430326161663362333462303666";

//sm4 加密
export const encryption = text => {
  const encryptedData = SM4.encrypt(text, key, {
    inputEncoding: "utf8",
    outputEncoding: "base64"
  });
  return encryptedData;
};

//sm4 解密
export const decrypt = ciphertext => {
  const decryptedData = SM4.decrypt(ciphertext, key, {
    inputEncoding: "base64",
    outputEncoding: "utf8"
  });
  return decryptedData;
};
