<script setup>
import { ref, onMounted } from "vue";
import WebOfficeSDK from "@/utils/filePreview/js/web-office/web-office-sdk-solution-v1.1.27.es";
import { officeUrl } from "@/api/upload";

const props = defineProps({
  type: {
    type: String,
    default: ""
  },
  fileUrl: {
    type: String,
    default: ""
  },
  name: {
    type: String,
    default: ""
  },
  showViewer: {
    type: Boolean,
    default: true
  }
});
const emit = defineEmits(["update:showViewer"]);
const isShow = defineModel("isShow");
const webofficeElref = ref();
const FileID = async val => {
  const lastIndex = val.name.lastIndexOf(".");
  const urlnew = val.name.substring(lastIndex);
  const url = val.url;
  const regex = /[^/]+$/;
  const match = url.match(regex);
  const result = match[0].split(".")[0];
  const params = {
    F_accesstoken: "CFIsGgvkonYEoVURomTZCk6HwshEQhRw",
    F_file_id: result,
    F_student_id: "12345",
    F_file_url: val.url,
    F_file_name: val.name,
    F_file_ext: urlnew,
    F_share: 4
  };
  const res = await officeUrl(params);
  // console.log("🐬-----res-----", params)
  if (res && res.F_responseNo === 10000) {
    const extractedString = res.F_url.match(/\/([^/?]+)\?/)[1];
    // console.log("🍧-----extractedString-----", extractedString);
    addsss(extractedString, urlnew);
  }
};
const addsss = (id, urlnew) => {
  const regex = /[a-z]/i; // 匹配英文字母的正则表达式
  const match = urlnew.match(regex); // 使用正则表达式匹配第一个英文字母
  const firstLetter = match ? match[0] : ""; // 如果匹配成功，则返回匹配到的字符，否则返回空字符串
  let offType = "";
  const fileTypeMapping = {
    ".pdf": "f",
    ".doc": "w",
    ".docx": "w",
    ".wps": "w",
    ".pptx": "p",
    ".ppt": "p",
    ".xls": "s",
    ".xlsx": "s",
    ".dbt": "d"
    // 可以继续添加其他文件类型
  };
  offType = fileTypeMapping[urlnew] || "o";
  WebOfficeSDK.init({
    officeType: offType,
    appId: "AK20230628COKMWP",
    fileId: id,
    mount: webofficeElref.value,
    mode: "simple"
  });
};
const onOpen = () => {
  FileID({
    name: props.name || props.fileUrl.split("/").pop(),
    url: props.fileUrl
  });
};
const closeDialog = () => {
  // console.log(334332232);
  // isShow.value = false
  emit("update:showViewer", false);
};
</script>

<template>
  <el-dialog
    v-model="isShow"
    append-to-body
    width="90%"
    top="5vh"
    class="content"
    style="padding: 0"
    :close-on-click-modal="false"
    @opened="onOpen"
    @close="closeDialog"
  >
    <div ref="webofficeElref" class="tip-box weboffice__wrapper" />
  </el-dialog>
</template>

<style lang="scss" scoped>
.tip-box {
  height: 85vh;
  overflow: auto;
  box-sizing: border-box;
  // padding: 30px 60px 50px 60px;
  margin-top: 20px;
}
</style>
