<script setup>
import { ref, onMounted, nextTick } from "vue";
import { decrypt, encryption } from "@/utils/SM4.js";
import { View, Hide } from "@element-plus/icons-vue";
const props = defineProps({
  data: {
    type: String,
    default: ""
  },
  dataCt: {
    type: String,
    default: ""
  },
  label: {
    type: String,
    default: ""
  }
});
const eyeValue = ref(true);
const eyePhone = () => {
  eyeValue.value = !eyeValue.value;
};
onMounted(async () => {});
</script>

<template>
  <div
    v-if="eyeValue"
    :class="label === '身份证号' ? 'eye_style' : 'eye_style1'"
    @click="eyePhone"
  >
    {{ dataCt ? dataCt : "--" }}
    <div class="eye">
      <el-icon><Hide /></el-icon>
    </div>
  </div>
  <div
    v-else
    :class="label === '身份证号' ? 'eye_style' : 'eye_style1'"
    @click="eyePhone"
  >
    {{ data ? data : "--" }}
    <div class="eye">
      <el-icon><View /></el-icon>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.eye_style {
  display: flex;
  // align-items: center;
  // justify-content: center;
  justify-content: space-between;
  width: 170px;
  .eye {
    // margin-left: 20px;
    cursor: pointer;
    :hover {
      color: #409eff;
    }
  }
}
.eye_style1 {
  display: flex;
  // align-items: center;
  // justify-content: center;
  justify-content: space-between;
  width: 120px;
  .eye {
    // margin-left: 20px;
    cursor: pointer;
    :hover {
      color: #409eff;
    }
  }
}
</style>
