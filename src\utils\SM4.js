import { SM4 } from "gm-crypto";

const key = "377a416b574e66504a70345242586459";

// sm4 加密
export function encryption(text) {
  if (!text) {
    return "";
  }
  const encryptedData = SM4.encrypt(text, key, {
    inputEncoding: "utf8",
    outputEncoding: "base64"
  });
  return encryptedData;
}

// sm4 解密
export function decrypt(ciphertext) {
  if (!ciphertext) {
    return "";
  }
  const decryptedData = SM4.decrypt(ciphertext, key, {
    inputEncoding: "base64",
    outputEncoding: "utf8"
  });
  return decryptedData;
}
