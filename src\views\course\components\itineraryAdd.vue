<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import RichEditor from "@/components/Base/RichEditor.vue";
import { itineraryCreate, itineraryUpdate, findById } from "@/api/period.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import DescriptionList from "./descriptionList.vue";
import dayjs from "dayjs";
import { to } from "@iceywu/utils";
const valueHtml = ref("");
const router = useRouter();
const route = useRoute();
const ruleFormRef = ref();
const openTime = ref(null);
const ruleForm = reactive({
  title: "",
  date: "",
  time: ""
});
const formData = ref([
  {
    label: "行程标题",
    type: "input",
    prop: "title",
    check: true,
    placeholder: "请输入行程标题",
    width: "240px"
  },
  {
    label: "日期",
    type: "date",
    prop: "date",
    check: true,
    placeholder: "请选择日期",
    width: "400px"
  },
  {
    label: "时间点",
    type: "time",
    prop: "time",
    check: true,
    placeholder: "请选择时间点",
    width: "400px",
    options: []
  }
]);
const validateDate = (rule, value, callback) => {
  if (!value) {
    callback(new Error("请选择日期"));
  } else {
    callback();
  }
};
const validateTime = (rule, value, callback) => {
  if (!value) {
    callback(new Error("请选择时间"));
  } else if (
    dayjs(
      dayjs(ruleForm.date).format("YYYY-MM-DD") +
      "" +
      dayjs(value).format("HH:mm:ss")
    ).valueOf() < dayjs(openTime.value).valueOf()
  ) {
    callback(new Error("时间不能早于开课时间"));
  } else {
    callback();
  }
};
const rules = reactive({
  title: [{ required: true, message: "请输入行程标题", trigger: "blur" }],
  // date: [{ required: true, message: "请选择日期", trigger: "blur" }],
  time: [{ validator: validateTime, trigger: "blur" }],
  date: [{ validator: validateDate, trigger: "blur" }]
});

// 根d查询行程点
const courseTypeFindApi = async () => {
  const params = {
    id: route.query.editId
  };
  let [err, res] = await requestTo(findById(params));
  if (res) {
    console.log("🐠-----res-----", res);
    ruleForm.title = res?.title;
    ruleForm.date = dayjs(res?.startTime).format("YYYY-MM-DD");
    ruleForm.time = dayjs(res?.startTime).format("YYYY-MM-DD HH:mm:ss");
    valueHtml.value = res?.content;
  } else {
    console.log("🍭-----err-----", err);
  }
};
const periodName = ref("");
const periodNameEvt = val => {
  periodName.value = val;
};
const operateLog = ref({});
const submitLoading = ref(false);
// 新增及编辑行程点
const addItineraryCreate = async formEl => {
  if (!formEl) return;
  if (submitLoading.value) return;
  submitLoading.value = true;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      const startDate = dayjs(ruleForm.date).format("YYYY-MM-DD");
      const startTImes = dayjs(ruleForm.time).format("HH:mm:ss");
      const startTime = startDate + " " + startTImes;
      const params = {
        content: valueHtml.value,
        title: ruleForm.title,
        startTime: dayjs(startTime).valueOf()
      };
      let api = itineraryCreate;
      operateLog.value = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `为“${periodName.value}”课期新增了“${ruleForm.title}”行程点`
        // operatorTarget: form.value.name,
      };
      if (route.query.type == "create") {
        params.coursePeriodId = route.query.periodId;
      } else {
        api = itineraryUpdate;
        params.id = route.query.editId;
        operateLog.value = {
          operateLogType: "COURSE_MANAGEMENT",
          operateType: `编辑了“${periodName.value}”课期中的“${ruleForm.title}”行程点`
          // operatorTarget: form.value.name,
        };
      }
      let [err, res] = await to(api(params, operateLog.value));
      if (res.code === 200) {
        ElMessage({
          type: "success",
          message: `${route.query.type == "create" ? "新增成功" : "编辑成功"}`
        });
        router.back();
      } else {
        ElMessage({
          type: "error",
          message:
            route.query.type == "create"
              ? `新增失败,${res.msg}`
              : `编辑失败，${res.msg}`
        });
        console.log("🌵-----err-----", err);
      }
    } else {
      console.log("校验不通过!", fields);
    }
    submitLoading.value = false;
  });
};

// 日期禁用逻辑：禁止选择小于开课时间的日期
const disabledDate = time => {
  if (!openTime.value) return false;
  return time.getTime() < dayjs(openTime.value).startOf("day").valueOf();
};

onMounted(() => {
  if (route.query.type !== "create") {
    courseTypeFindApi();
  }
});
</script>

<template>
  <div>
    <DescriptionList
      v-model:openTime="openTime"
      :periodId="Number(route.query.periodId)"
      @name="periodNameEvt"
    />
    <div class="itinerary-add">
      <div class="table-text">
        <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules">
          <el-descriptions title="" :column="1" border>
            <el-descriptions-item
              v-for="(item, index) in formData"
              :key="index"
              label-align="center"
            >
              <template #label>
                <span v-if="item.check" class="star">*</span>{{ item.label }}
              </template>
              <el-form-item
                :prop="item.prop"
                :inline-message="item.check"
                style="margin-bottom: 0"
                :show-message="true"
                error-placement="right"
              >
                <!-- input输入 -->
                <template v-if="item.type === 'input'">
                  <el-input
                    v-model="ruleForm[item.prop]"
                    :placeholder="item.placeholder"
                    :style="{ width: item.width }"
                  />
                </template>
                <!-- 日期选择 -->
                <template v-if="item.type === 'date'">
                  <el-date-picker
                    v-model="ruleForm[item.prop]"
                    :placeholder="item.placeholder"
                    style="width: 240px"
                    :disabled-date="disabledDate"
                  />
                </template>
                <!-- 时间选择 -->
                <template v-if="item.type === 'time'">
                  <el-time-picker
                    v-model="ruleForm[item.prop]"
                    :placeholder="item.placeholder"
                    style="width: 240px"
                  />
                </template>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </el-form>
      </div>
      <div class="editer"><RichEditor v-model="valueHtml" height="200" /></div>
      <div class="buttons">
        <!-- <div class="cancel" @click="router.go(-1)">取消</div>
      <div class="create" @click="addItineraryCreate(ruleFormRef)">
        {{ route.query.type === "create" ? "确认新增" : "保存" }}
      </div> -->
        <el-button @click="router.go(-1)">取消</el-button>
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="addItineraryCreate(ruleFormRef)"
        >
          {{ route.query.type === "create" ? "确定新增" : "保存" }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.itinerary-add {
  box-sizing: border-box;
  width: 100%;
  height: 650px;
  padding: 20px 20px;
  background-color: #fff;
  .table-text {
    margin-bottom: 10px;
  }
  .editer {
    height: 410px;
    overflow-y: auto;
    margin-bottom: 5px;
  }
  .buttons {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
    width: 100%;
    // margin: 0 auto;
    // margin-top: 28vh;

    .cancel {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100px;
      height: 36px;
      color: rgb(255 255 255 / 100%);
      cursor: pointer;
      background-color: rgb(230 152 58 / 100%);
      border-radius: 6px;
    }

    .create {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100px;
      height: 36px;
      color: rgb(255 255 255 / 100%);
      cursor: pointer;
      background-color: rgb(64 149 229 / 100%);
      border-radius: 6px;
    }
  }
  .star {
    margin-right: 3px;
    color: red;
  }
}
:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  width: 240px;
  background: #e1f5ff;
}
:deep(.w-e-text-container) {
  height: 280px;
}
:deep(.w-e-text-placeholder) {
  top: 6px;
  left: 14px;
}
</style>
