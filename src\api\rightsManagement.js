import { http } from "@/utils/http";

//  权限管理-角色设置-查询列表
export const getRoleListInterFace = params => {
  return http.request(
    "get",
    "/organization/role/findAll",
    { params },
    {
      isNeedFullRes: false, // 是否需要返回完整的响应对象
      isNeedToken: true, //
      isNeedEncrypt: true
    }
  );
};
//  权限管理-角色设置-查询详情
export const getRoleDetailsInterFace = params => {
  return http.request(
    "get",
    "/organization/role/findById",
    { params },
    {
      isNeedFullRes: false, // 是否需要返回完整的响应对象
      isNeedToken: true, //
      isNeedEncrypt: true
    }
  );
};

//  权限管理-角色设置-新增
export const addRoleListInterFace = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/role/save",
    { data },
    {
      isNeedFullRes: false, // 是否需要返回完整的响应对象
      isNeedToken: true, //
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

//  权限管理-角色设置-编辑
export const editRoleListInterFace = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/role/update",
    { data },
    {
      isNeedFullRes: false, // 是否需要返回完整的响应对象
      isNeedToken: true, //
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

//  权限管理-角色设置-删除
export const deleteRoleListInterFace = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/role/delete",
    { data },
    {
      isNeedFullRes: false, // 是否需要返回完整的响应对象
      isNeedToken: true, //
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

//  权限管理-获取列表
export const getAuthorityInterface = params => {
  return http.request(
    "get",
    "/organization/authority/findAll",
    { params },
    {
      isNeedFullRes: false, // 是否需要返回完整的响应对象
      isNeedToken: true, //
      isNeedEncrypt: true
    }
  );
};

//  权限管理-新增
export const addAuthorityInterface = data => {
  return http.request(
    "post",
    "/organization/authority/save",
    { data },
    {
      isNeedFullRes: false, // 是否需要返回完整的响应对象
      isNeedToken: true, //
      isNeedEncrypt: true
    }
  );
};
//  权限管理-修改
export const editAuthorityInterface = data => {
  return http.request(
    "post",
    "/organization/authority/update",
    { data },
    {
      isNeedFullRes: false, // 是否需要返回完整的响应对象
      isNeedToken: true, //
      isNeedEncrypt: true
    }
  );
};
//  权限管理-删除
export const deleteAuthorityInterface = data => {
  return http.request(
    "post",
    "/organization/authority/delete",
    { data },
    {
      isNeedFullRes: false, // 是否需要返回完整的响应对象
      isNeedToken: true, //
      isNeedEncrypt: true
    }
  );
};

//  权限管理-详情
export const detailsAuthorityInterface = params => {
  return http.request(
    "get",
    "/organization/authority/findById",
    { params },
    {
      isNeedFullRes: false, // 是否需要返回完整的响应对象
      isNeedToken: true, //
      isNeedEncrypt: true
    }
  );
};
