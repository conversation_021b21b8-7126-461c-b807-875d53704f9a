<script setup>
import { ref, onMounted, watchEffect } from "vue";
import { useRouter, useRoute } from "vue-router";
import { requestTo } from "@/utils/http/tool";
import {
  ledeterList,
  fileLeaderList,
  fileLeatureList
} from "@/api/leaderLecturer.js";
import { formatTime } from "@/utils/index";
import FileItem from "@/components/PreviewV2/FileItem.vue";
const router = useRouter();
const route = useRoute();
onMounted(() => {
  getacctable();
  getDifferentFile();
});
// 表头
const tableHeader = ref([
  { label: "机构", value: "", width: "107px", key: "organizationName" },
  { label: "领队ID", value: "", width: "107px", key: "id" },
  { label: "姓名", value: "", width: "107px", key: "name" },
  { label: "创建时间", value: "", width: "107px", key: "createdAt" },
  {
    label: "资质文件",
    value: "",
    width: "107px",
    prop: "qualificationDocuments",
    type: "upload"
  }
]);
watchEffect(() => {
  const title = route.query.title;
  tableHeader.value[1].label = title === "ldMang" ? "领队ID" : "讲师ID";
});
const getacctable = async () => {
  const [err, result] = await requestTo(ledeterList({ id: route.query?.id }));
  if (err) {
    console.error("获取数据失败:", err);
    return;
  }
  tableHeader.value = tableHeader.value.map(item => ({
    ...item,
    value:
      item.key === "createdAt"
        ? formatTime(result[item.key], "YYYY-MM-DD HH:mm")
        : (result[item.key] ?? "--")
  }));

  console.log("", tableHeader.value);
};
const formFile = ref({
  institutionLicense: [],
  qualificationDocuments: []
});

// 根据不同身份获取上传文件
const getDifferentFile = async () => {
  const params = {
    organizationAdminId: route.query?.id
  };
  let requestApi =
    route.query.title === "ldMang"
      ? fileLeaderList(params)
      : fileLeatureList(params);
  const { code, data, msg } = await requestApi;
  if (code == 200) {
    console.log("🐠-----data---/////////////--", data);
    formFile.value.institutionLicense = [];
    formFile.value.qualificationDocuments = [];
    if (data?.fileDTOS) {
      data.fileDTOS.forEach(item => {
        if (item.fileType == "BUSINESS_LICENSE") {
          formFile.value.institutionLicense.push(item.uploadFile);
        } else if (item.fileType == "QUALIFICATION_DOCUMENT") {
          formFile.value.qualificationDocuments.push(item.uploadFile);
        }
      });
    }
  }
};
</script>

<template>
  <div class="containers">
    <div class="tabHeastyle">
      <el-descriptions
        class="margin-top"
        title=""
        :column="2"
        border
        style="width: 100%"
      >
        <template v-for="(item, index) in tableHeader" :key="index">
          <el-descriptions-item label-align="center">
            <template #label>
              <div class="cell-item">{{ item.label }}</div>
            </template>
            <template #default>
              <!-- 示例：上传组件 -->
              <template v-if="item.type === 'upload'">
                <template v-if="formFile[item.prop].length > 0">
                  <template
                    v-for="(item2, index2) in formFile[item.prop]"
                    :key="index2"
                  >
                    <FileItem
                      :data="item2"
                      :index="index2"
                      style="width: 100%; min-width: 130px"
                    />
                  </template>
                </template>
                <template v-else> -- </template>
              </template>
              <template v-else>
                <div class="cell-item">{{ item.value }}</div>
              </template>
            </template>
          </el-descriptions-item>
        </template>
      </el-descriptions>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  box-sizing: border-box;
  width: calc(100% - 48px);
  height: 100%;
  padding: 24px;
  background: #fff;

  .tabHeastyle {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
  }
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}
</style>
