/**
 * 地图组件辅助工具函数
 */

// 常量定义
export const MAP_CONSTANTS = {
  DEFAULT_POINT: [116.3912757, 39.906217],
  DEFAULT_ZOOM: 17,
  DEFAULT_PITCH: 30,
  SEARCH_DEBOUNCE_DELAY: 300,
  TILT_ANIMATION_DELAY: 1000,
  ICON_SIZE: { width: 22, height: 25 },
  ICON_OFFSET: { x: 11, y: 12 }
};

/**
 * 创建百度地图点对象
 * @param {Array} coordinates - 坐标数组 [lng, lat]
 * @returns {BMapGL.Point} 百度地图点对象
 */
export const createBMapPoint = coordinates => {
  if (!coordinates || coordinates.length < 2) {
    throw new Error("Invalid coordinates");
  }
  return new BMapGL.Point(coordinates[0], coordinates[1]);
};

/**
 * 验证坐标是否有效
 * @param {Array} coordinates - 坐标数组
 * @returns {boolean} 是否有效
 */
export const isValidCoordinates = coordinates => {
  return (
    Array.isArray(coordinates) &&
    coordinates.length === 2 &&
    typeof coordinates[0] === "number" &&
    typeof coordinates[1] === "number" &&
    coordinates[0] >= -180 &&
    coordinates[0] <= 180 &&
    coordinates[1] >= -90 &&
    coordinates[1] <= 90
  );
};

/**
 * 创建地图标记图标
 * @param {string} iconUrl - 图标URL
 * @param {Object} options - 配置选项
 * @returns {BMapGL.Icon} 百度地图图标对象
 */
export const createMapIcon = (iconUrl, options = {}) => {
  const {
    width = MAP_CONSTANTS.ICON_SIZE.width,
    height = MAP_CONSTANTS.ICON_SIZE.height,
    offsetX = MAP_CONSTANTS.ICON_OFFSET.x,
    offsetY = MAP_CONSTANTS.ICON_OFFSET.y
  } = options;

  return new BMapGL.Icon(iconUrl, new BMapGL.Size(width, height), {
    offset: new BMapGL.Size(offsetX, offsetY),
    imageOffset: new BMapGL.Size(0, 0)
  });
};

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间
 * @returns {Function} 防抖后的函数
 */
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * 格式化地址标签
 * @param {Object} data - 地址数据
 * @returns {string} 格式化后的标签
 */
export const formatAddressLabel = data => {
  if (!data) return "";
  const { title = "", address = "" } = data;
  // return title ? `${address}(${title})` : address;
  return address || title;
};

/**
 * 生成信息窗口内容
 * @param {Object} data - 数据对象
 * @returns {string} HTML内容
 */
export const generateInfoWindowContent = (data = {}) => {
  const { pos = "读书郎教育科技有限公司" } = data;
  return `
    <div class="map-tip">
      <span>当前选中的地址：</span>
      <span class="pos-name">${pos}</span>
    </div>
  `;
};

/**
 * 地图配置选项生成器
 * @param {Object} props - 组件属性
 * @returns {Object} 地图配置对象
 */
export const generateMapConfig = props => {
  return {
    zoom: props.zoom || MAP_CONSTANTS.DEFAULT_ZOOM,
    pitch: props.pitch || MAP_CONSTANTS.DEFAULT_PITCH,
    rotation: props.rotation || 0,
    maxZoom: props.maxZoom || 19,
    minZoom: props.minZoom || 11,
    enableScrollWheelZoom: true
  };
};

/**
 * 错误处理工具
 */
export const ErrorHandler = {
  /**
   * 处理地图相关错误
   * @param {Error} error - 错误对象
   * @param {string} context - 错误上下文
   */
  handleMapError(error, context = "") {
    console.error(`地图错误${context ? ` (${context})` : ""}:`, error);

    // 可以在这里添加错误上报逻辑
    // this.reportError(error, context);
  },

  /**
   * 处理API调用错误
   * @param {Error} error - 错误对象
   * @param {string} apiName - API名称
   */
  handleApiError(error, apiName = "") {
    console.error(`API调用失败${apiName ? ` (${apiName})` : ""}:`, error);

    // 可以在这里添加用户友好的错误提示
    // this.showUserError('网络请求失败，请稍后重试');
  }
};

/**
 * 地图工具类
 */
export class MapUtils {
  /**
   * 计算两点之间的距离
   * @param {Array} point1 - 点1坐标
   * @param {Array} point2 - 点2坐标
   * @returns {number} 距离（米）
   */
  static calculateDistance(point1, point2) {
    if (!isValidCoordinates(point1) || !isValidCoordinates(point2)) {
      throw new Error("Invalid coordinates for distance calculation");
    }

    const map = new BMapGL.Map();
    const bPoint1 = createBMapPoint(point1);
    const bPoint2 = createBMapPoint(point2);
    return map.getDistance(bPoint1, bPoint2);
  }

  /**
   * 获取地图中心点
   * @param {Array} center - 中心点坐标
   * @param {Array} fallback - 备用坐标
   * @returns {Array} 有效的中心点坐标
   */
  static getValidCenter(center, fallback = MAP_CONSTANTS.DEFAULT_POINT) {
    return isValidCoordinates(center) ? center : fallback;
  }

  /**
   * 检查是否支持地理定位
   * @returns {boolean} 是否支持地理定位
   */
  static isGeolocationSupported() {
    return !!(
      navigator.geolocation &&
      window.BMapGL &&
      window.BMapGL.Geolocation
    );
  }

  /**
   * 格式化位置信息用于日志
   * @param {Object} position - 位置对象
   * @returns {string} 格式化的位置信息
   */
  static formatPositionLog(position) {
    if (!position || !position.lng || !position.lat) {
      return "无效位置";
    }
    return `位置: ${position.lng.toFixed(6)}, ${position.lat.toFixed(6)}`;
  }

  /**
   * 创建地图实例
   * @param {string} containerId - 容器ID
   * @param {Object} config - 配置对象
   * @returns {BMapGL.Map} 地图实例
   */
  static createMapInstance(containerId, config = {}) {
    if (!window.BMapGL) {
      throw new Error("百度地图API未加载");
    }

    const map = new BMapGL.Map(containerId);
    const { zoom, pitch, rotation, maxZoom, minZoom, enableScrollWheelZoom } =
      config;

    // 设置地图属性
    if (typeof zoom === "number") map.setZoom(zoom);
    if (typeof pitch === "number") map.setTilt(pitch);
    if (typeof rotation === "number") map.setHeading(rotation);
    if (typeof maxZoom === "number") map.setMaxZoom(maxZoom);
    if (typeof minZoom === "number") map.setMinZoom(minZoom);
    if (enableScrollWheelZoom) map.enableScrollWheelZoom(true);

    return map;
  }
}
