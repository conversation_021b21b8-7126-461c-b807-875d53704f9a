<script setup>
import { ref, onMounted } from "vue";
import { getFileFullUrl } from "@/utils/fileTools";
const props = defineProps({
  isShowDialog: {
    type: Boolean,
    default: true
  },
  type: {
    type: String,
    default: ""
  },
  fileUrl: {
    type: String,
    default: ""
  },
  title: {
    type: String,
    default: ""
  }
});

const emit = defineEmits(["update:isShowDialog"]);

const dialogVisible = ref(true);

const closeDialog = () => {
  emit("update:isShowDialog", false);
};
const baseUrl = ref("");
onMounted(async () => {
  baseUrl.value = await getFileFullUrl(props.fileUrl);
});
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    append-to-body
    width="70%"
    top="10vh"
    class="content"
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <div style="text-align: center">
      <!-- <h3>{{ title || "" }}</h3> -->
      <div class="content">
        <video
          v-if="type === 'video'"
          :src="baseUrl"
          controls
          autoplay
          style="
            height: 568px;
            width: 100%;
            margin: 0 40px 30px;
            object-fit: contain;
          "
        />
        <audio v-else :src="baseUrl" autoplay controls />
      </div>
    </div>
  </el-dialog>
</template>

<style lang="scss" scoped>
.el-dialog {
  background: none !important;
  border-radius: 6px;
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
  }
}
.content {
  width: 100%;
  // max-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  // margin-top: 20px;
  .video {
    width: 100%;
    // height: 350px;
    display: block;
  }
}
</style>
