import { http } from "@/utils/http";

/*  邀请管理相关接口  */
// 分页查询
export const teacherInvitationFindAll = params => {
  return http.request(
    "get",
    "/organization/teacherInvitation/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 根据教师id分页查询师资详情
export const findByTeacherId = params => {
  return http.request(
    "get",
    "/organization/teacherDatabase/findByTeacherId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 删除
export const draftDelete = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/draft/delete",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "保存了草稿箱基本信息"
      }
    }
  );
};
/*  师资库相关接口  */
