image: ccr.ccs.tencentyun.com/brpublic/node:22.14.0

stages:
- build
- image
- deploy
- notice

variables:
  TEST_NAMESPACE: ebag-test
  PROD_NAMESPACE: ebag-prod
  DEPLOY_NAME: research-learning-institution
  TEST_DEPLOY_NAME: research-learning-institution-test
  KUBECTL_IMAGE: registry.gitlab.dreamdev.cn/docker/build/kubectl:v1.14.1-3
  DOCKER_IMAGE: ccr.ccs.tencentyun.com/brpublic/docker:25.0.5-git
  DSL_PROJ_URL: https://gitlab.dreamdev.cn/chengdu/studyinxx/research-learning-institution
  BR_REGISTRY_HOST: "ccr.ccs.tencentyun.com"
  BR_REGISTRY_IMAGE_DEV: "ccr.ccs.tencentyun.com/brdev/research-learning-institution"
  BR_REGISTRY_IMAGE_PROD: "ccr.ccs.tencentyun.com/brprod/research-learning-institution"
  BR_PROJ_URL: http://***********/research-learning/research-learning-institution

cache:
  paths:
  - node_modules/

#柏然开发环境
brdev-build:
  stage: build
  script:
  - npm config set registry https://registry.npmmirror.com
  - npm ci
  - npm run build:brdev
  artifacts:
    expire_in: 1 week
    paths:
    - dist
  tags:
  - docker
  rules:
  - if: '$CI_COMMIT_REF_NAME == "main"'

brdev-image:
  stage: image
  image: $DOCKER_IMAGE
  script:
  - docker login -u $BR_REGISTRY_USER -p $BR_REGISTRY_PASSWORD $BR_REGISTRY_HOST
  - docker build -t $BR_REGISTRY_IMAGE_DEV:$CI_COMMIT_SHA -t $BR_REGISTRY_IMAGE_DEV:latest -f docker/Dockerfile .
  - docker push $BR_REGISTRY_IMAGE_DEV:$CI_COMMIT_SHA
  - docker push $BR_REGISTRY_IMAGE_DEV:latest
  tags:
  - docker
  rules:
  - if: '$CI_COMMIT_REF_NAME == "main"'

brdev-deploy:
  stage: deploy
  script:
  - |
    curl -X PUT \
    -H "content-type: application/json" \
    -H "Cookie: KuboardUsername=rk.yi; KuboardAccessKey=e8enncyef43k.pdhd3tbkmmhbtycspt87kzmkmsyah4pc" \
    -d '{"kind":"deployments","namespace":"br","name":"research-learning-institution-dev","images":{"ccr.ccs.tencentyun.com/brdev/research-learning-institution":"ccr.ccs.tencentyun.com/brdev/research-learning-institution:'$CI_COMMIT_SHA'"}}' \
    "http://***********:8080/kuboard-api/cluster/br-dev/kind/CICDApi/rk.yi/resource/updateImageTag"
  rules:
  - if: '$CI_COMMIT_REF_NAME == "main"'
  tags:
  - docker

# brdev-notice:
#   stage: notice
#   script:
#     - curl "https://git-ding-robot.boran-tech.com/tz/cidHPBGXoDpARWT6fmQH8pD5w==/%E5%8D%97%E6%B5%94%E9%97%A8%E6%88%B7%20%7C%20%E7%94%A8%E6%88%B7%E7%AB%AF%20%7C%20204%E7%8E%AF%E5%A2%83"
#   rules:
#     - if: '$CI_COMMIT_REF_NAME == "master" && $CI_PROJECT_URL == $BR_PROJ_URL'

# 测试环境

# test-build:
#   stage: build
#   script:
#   - npm config set registry https://registry.npmmirror.com
#   - npm ci
#   - npm run build:test
#   artifacts:
#     expire_in: 1 week
#     paths:
#     - dist
#   rules:
#   - if: '$CI_COMMIT_REF_NAME == "test" && $CI_PROJECT_URL == $DSL_PROJ_URL'

# test-image:
#   stage: image
#   variables:
#     IMAGE_NAME: $CI_REGISTRY_IMAGE/$CI_COMMIT_REF_NAME
#   image: $DOCKER_IMAGE
#   script:
#   - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
#   - docker build -t $IMAGE_NAME:$CI_COMMIT_SHA -t $IMAGE_NAME:latest -f docker/Dockerfile .
#   - docker push $IMAGE_NAME:$CI_COMMIT_SHA
#   - docker push $IMAGE_NAME:latest
#   rules:
#   - if: '$CI_COMMIT_REF_NAME == "test" && $CI_PROJECT_URL == $DSL_PROJ_URL'

# test-deploy:
#   stage: deploy
#   variables:
#     IMAGE_NAME: $CI_REGISTRY_IMAGE/$CI_COMMIT_REF_NAME:$CI_COMMIT_SHA
#   image: $KUBECTL_IMAGE
#   script:
#     - mkdir -p ~/.kube
#     - echo "$ZHUJI_KUBERNETES_CONFIG" > ~/.kube/config
#     # - echo "$TEST_KUBERNETES_CA" > ~/.kube/ca.crt
#     - kubectl -n $TEST_NAMESPACE set image deployment/$TEST_DEPLOY_NAME $TEST_DEPLOY_NAME=$IMAGE_NAME
#   rules:
#     - if: '$CI_COMMIT_REF_NAME == "test" && $CI_PROJECT_URL == $DSL_PROJ_URL'
#   tags:
#   - docker

# test-notice:
#   stage: notice
#   script:
#     - curl "https://git-ding-robot.boran-tech.com/tz/cidYrPlilbvWB+SQTBkmotFeg==/%E5%85%83%E5%9F%B9%E8%AF%BB%E4%B9%A6%E4%BC%9A%20%7C%20%E7%94%A8%E6%88%B7%E7%AB%AF%20%7C%20%E6%B5%8B%E8%AF%95%E7%8E%AF%E5%A2%83"
#   rules:
#     - if: '$CI_COMMIT_REF_NAME == "test" && $CI_PROJECT_URL == $DSL_PROJ_URL'

# # 正式环境

prod-build:
  stage: build
  script:
  - cat $DOMAIN_CONFIG
  - cp $DOMAIN_CONFIG src/utils/http/base.js
  - npm config set registry https://registry.npmmirror.com
  - npm ci
  - npm run build
  artifacts:
    expire_in: 1 week
    paths:
    - dist
  environment:
    name: prod
  rules:
  - if: '$CI_COMMIT_TAG'

prod-image:
  stage: image
  image: $DOCKER_IMAGE
  needs:
    - job: prod-build
      artifacts: true
  script:
    - docker login -u $BR_REGISTRY_USER -p $BR_REGISTRY_PASSWORD $BR_REGISTRY_HOST
    - docker build -t $BR_REGISTRY_IMAGE_PROD:$CI_COMMIT_TAG -f docker/Dockerfile .
    - docker push $BR_REGISTRY_IMAGE_PROD:$CI_COMMIT_TAG
  rules:
    - if: '$CI_COMMIT_TAG'

# 中山生产环境
prod-zs-build:
  stage: build
  script:
  - cat $DOMAIN_CONFIG
  - cp $DOMAIN_CONFIG src/utils/http/base.js
  - npm config set registry https://registry.npmmirror.com
  - npm ci
  - npm run build
  artifacts:
    expire_in: 1 week
    paths:
      - dist
  environment:
    name: zs
  rules:
    - if: '$CI_COMMIT_TAG'

prod-zs-image:
  stage: image
  image: $DOCKER_IMAGE
  needs:
    - job: prod-zs-build
      artifacts: true
  variables:
    DSL_REGISTRY_HOST: registry.gitlab.dreamdev.cn
    DSL_REGISTRY_IMAGE: registry.gitlab.dreamdev.cn/chengdu/research-learning/research-learning-img/institution-web
  script:
    - docker login -u $DSL_REGISTRY_USER -p $DSL_REGISTRY_PASSWORD $DSL_REGISTRY_HOST
    - docker build -t $DSL_REGISTRY_IMAGE:$CI_COMMIT_TAG -f docker/Dockerfile .
    - docker push $DSL_REGISTRY_IMAGE:$CI_COMMIT_TAG
  environment:
    name: zs
  rules:
    - if: '$CI_COMMIT_TAG'

# prod-deploy:
#   stage: deploy
#   variables:
#     IMAGE_NAME: $CI_REGISTRY_IMAGE/prod:$CI_COMMIT_TAG
#   image: $KUBECTL_IMAGE
#   script:
#   - mkdir -p ~/.kube
#   - echo "$ZHUJI_KUBERNETES_CONFIG" > ~/.kube/config
#   # - echo "$PROD_KUBERNETES_CA" > ~/.kube/ca.crt
#   - kubectl -n $PROD_NAMESPACE set image deployment/$DEPLOY_NAME $DEPLOY_NAME=$IMAGE_NAME
#   rules:
#   - if: '$CI_COMMIT_TAG && $CI_PROJECT_URL == $DSL_PROJ_URL'
