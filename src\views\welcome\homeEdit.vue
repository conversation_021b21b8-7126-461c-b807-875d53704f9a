<script setup>
import { ref, onMounted, computed, watch, onBeforeMount } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import uploadImg from "@/assets/login/upload1.png";
import { adminFindById } from "@/api/institution.js";
import { uploadFile } from "@/utils/upload/upload";
import { Hide, View, Loading } from "@element-plus/icons-vue";
import { decrypt, encryption } from "@/utils/SM4.js";
import { compareObjects, debounce } from "@iceywu/utils";
import {
  editteaInfo,
  leaderLecturerSaveLecturerFile,
  leaderLecturerSaveLeaderFile,
  verifyPhone,
  updatePhone,
  verifyUsername,
  getPhonecode
} from "@/api/institution";
import { getBindCode, unbindWxCode } from "@/api/user.js";
import qrcode from "qrcode";
import FileItem from "@/components/PreviewV2/FileItem.vue";
import WxQrCode from "@/components/WxQrCode/index.vue";

const router = useRouter();
const route = useRoute();
const userInfIdData = ref(null);
const richFlag = ref(false);
const qrCodeData = ref("");
const url = ref("https:/www.baidu.com/");

// 微信二维码组件
const wxQrCodeRef = ref(null);
const unbindWxLoading = ref(false);
// 微信回调处理状态
const isWxCallbackProcessed = ref(false);

// 解绑微信对话框显示状态
const showUnbindDialog = ref(false);
onMounted(() => {
  userInfIdData.value = route.query.id;
  getUserinfo();
  qrcode.toDataURL(url.value, (err, url) => {
    if (err) {
      console.error(err);
    } else {
      qrCodeData.value = url;
    }
  });
  // richFlag.value = true;
  // console.log("🎁-----useUserStoreHook()-----", userInfIdData.value);
});
const formFile = ref({
  institutionLicense: [],
  qualificationDocuments: []
});
// 表单
const form = ref({
  name: "",
  account: "",
  iphone: "",
  code: "",
  email: "",
  idNumber: "",
  Identity: "",
  fileUrl: "",
  files: [],
  phone: "",
  institutionLicense: [],
  qualificationDocuments: [],
  isBindWx: false
});
// 提交
const formRef = ref(null);

const formData = ref([
  {
    label: "姓名",
    type: "input",
    prop: "name",
    show: true,
    check: true,
    placeholder: "请输入姓名",
    width: "400px",
    maxLength: 10
  },
  {
    label: "账号",
    type: "input",
    prop: "account",
    show: true,
    check: true,
    placeholder: "请输入账号",
    width: "400px",
    maxLength: 20
  },
  {
    label: "手机号",
    type: "input",
    prop: "phone",
    show: true,
    isView: true,
    check: true,
    placeholder: "请输入手机号",
    width: "400px",
    maxLength: 11
  },
  {
    label: "邮箱",
    type: "input",
    show: true,
    check: true,
    isView: true,
    prop: "email",
    placeholder: "请输入邮箱",
    width: "400px",
    maxLength: 30
  },
  {
    label: "身份证号码",
    type: "input",
    prop: "idNumber",
    show: true,
    isView: true,
    check: true,
    placeholder: "请输入身份证号",
    width: "400px",
    maxLength: 18
  },
  {
    label: "微信绑定",
    type: "img",
    prop: "isBindWx",
    url: "",
    width: "400px",
    height: "120px"
  }
]);
// 身份证号验证
const validateIdNumber = (rule, value, callback) => {
  // console.log("🦄-----value-----", value);
  // return;
  // console.log("🦄-----value-----", value, newData.value.idNumber);
  // console.log(decrypt(newData.value.idNumberCt));
  if (
    newData.value.idNumberCt &&
    (value === newData.value.idNumber ||
      value === decrypt(newData.value.idNumberCt))
  ) {
    callback();
    return;
  }
  const idNumberPattern = /(^\d{15}$)|(^\d{18}$)|(^\d{17}([\dX])$)/i;
  if (value && !idNumberPattern.test(value)) {
    callback(new Error("请输入有效的身份证号"));
  } else {
    callback();
  }
};

// 邮箱验证
const validateEmail = (rule, value, callback) => {
  const emailPattern = /^[a-z0-9._-]+@[a-z0-9.-]+\.[a-z]{2,6}$/i;
  if (value && !emailPattern.test(value)) {
    callback(new Error("请输入有效的邮箱地址"));
  } else {
    callback();
  }
};
// 自定义电话号码校验方法
const validatePhoneNumber = async (rule, value, callback) => {
  if (
    newData.value.phoneCt &&
    (value == newData.value.phone || value == decrypt(newData.value.phoneCt))
  ) {
    if (formData.value[3].label === "验证码") {
      formData.value.splice(3, 1);
    }
    callback();
    return;
  }
  const phoneRegex = /^1[3-9]\d{9}$/; // 匹配中国大陆手机号码
  if (!value) {
    isCaptchaDisabled.value = true;
    return callback(new Error("手机号不能为空"));
  } else if (!phoneRegex.test(value)) {
    isCaptchaDisabled.value = true;
    return callback(new Error("请输入有效的手机号码"));
  } else {
    isCaptchaDisabled.value = false;

    if (formData.value[3].label !== "验证码") {
      // 往数组指定位置添加验证码字段
      formData.value.splice(3, 0, {
        label: "验证码",
        type: "input",
        prop: "code",
        span: 1,
        placeholder: "请输入验证码",
        width: "400px",
        check: true
      });

      // 如果form中没有code字段，初始化它
      if (!form.value.code) {
        form.value.code = "";
      }
    }

    if (value == decrypt(newData.value.phoneCt)) {
      callback();
    } else {
      try {
        const response = await verifyPhone({ phone: encryption(value) });
        if (response.code === 70008) {
          isCaptchaDisabled.value = true;
          callback(new Error("手机号已存在"));
        } else {
          callback();
        }
      } catch (error) {
        console.log("🌈-----error-----", error);
      }
    }
  }
};
// 验证码
const validateCode = async (rule, value, callback) => {
  if (
    form.value.phone === newData.value.phone ||
    form.value.phone === decrypt(newData.value.phoneCt)
  )
    return;
  if (!value) {
    callback(new Error("验证码不能为空"));
    return;
  }
  try {
    let { code, msg } = await updatePhone({
      code: form.value.code,
      phone: encryption(form.value.phone)
    });
    if (code == 30018) {
      ElMessage({
        message: msg,
        type: "error"
      });
    }
    // console.log("🐠-----code-----", code);
  } catch (error) {
    console.log("catch-----error-----", error);
  }
};
// 自定义账号校验方法
const validateAccount = async (rule, value, callback) => {
  if (value === newData.value.account) {
    callback();
    return;
  }
  if (!value) {
    callback(new Error("账号不能为空"));
  } else {
    try {
      const response = await verifyUsername({ username: value });
      console.log("🌈-----response-----", response);
      if (response.code === 10016) {
        callback(new Error("账号已存在"));
      } else {
        callback();
      }
    } catch (error) {
      console.log("🌈-----error-----", error);
    }
    // callback();
  }
};
// 校验规则
const rules = ref({
  name: [
    { required: true, message: "姓名不能为空", trigger: "blur" },
    { min: 2, max: 10, message: "姓名长度应在2-10个字符之间", trigger: "blur" }
  ],
  account: [{ required: true, validator: validateAccount, trigger: "blur" }],
  phone: [{ required: true, validator: validatePhoneNumber, trigger: "blur" }],
  code: [{ required: true, message: "验证码不能为空", trigger: "blur" }],
  idNumber: [{ required: true, validator: validateIdNumber, trigger: "blur" }],
  email: [{ required: true, validator: validateEmail, trigger: "blur" }]
});
const submitForm = () => {
  formRef.value.validate(async valid => {
    if (valid) {
      console.log("表单数据:", form.value);
      await addFile();
      await addbase();
    } else {
      console.log("表单校验失败");
    }
  });
};
const addbase = async () => {
  let paramsData = {};
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  paramsData = compareObjects(newData.value, paramsData);

  // 特殊处理邮箱和身份证号
  // 如果邮箱有值，则必须提交
  if (form.value.email) {
    paramsData.email = form.value.email;
  }

  // 处理身份证号 - 根据当前状态判断如何处理
  if (form.value.idNumber) {
    // 判断身份证号是否已经是掩码状态
    const idNumberIndex = formData.value.findIndex(
      item => item.prop === "idNumber"
    );

    if (idNumberIndex !== -1 && formData.value[idNumberIndex].isView) {
      // 如果是掩码状态，使用原始的加密数据
      if (newData.value.idNumberCt) {
        paramsData.idNumber = newData.value.idNumberCt;
      } else {
        paramsData.idNumber = encryption(form.value.idNumber);
      }
    } else if (form.value.idNumber === newData.value.idNumberDecrypted) {
      // 如果显示的是解密形式且值未变，使用原始的加密数据
      if (newData.value.idNumberCt) {
        paramsData.idNumber = newData.value.idNumberCt;
      }
    } else {
      // 如果是新输入的数据，进行加密
      paramsData.idNumber = encryption(form.value.idNumber);
    }
  }

  // 判断手机号是否修改;
  // 没修改
  if (
    form.value.phone === newData.value.phone ||
    form.value.phone === decrypt(newData.value.phoneCt)
  ) {
    delete paramsData.phone;
  }
  // 已修改
  if (paramsData.phone) {
    paramsData.phone = encryption(paramsData.phone);
  }

  paramsData.id = userInfIdData.value;
  const operateLog = {
    operateLogType: "ACCOUNT_MANAGEMENT",
    operateType: "编辑了首页信息"
  };
  console.log("🍪-----paramsData-----", paramsData);
  // return;
  const { code, msg } = await editteaInfo(paramsData, operateLog);
  if (code === 200) {
    ElMessage({
      type: "success",
      message: "编辑成功"
    });
    getLoading.value = false;
    cancelForm();
  } else {
    ElMessage({
      type: "error",
      message: msg
    });
    getLoading.value = false;
  }
};
const getLoading = ref(false);
const addFile = async () => {
  if (getLoading.value) return;
  getLoading.value = true;
  console.log("🌵-----getLoading.value-----", getLoading.value);
  fileDataSotr.value = [];
  fileDataSotrTow.value = [];
  fileData();
  // console.log("🍪-----paramsData-----", fileDataSotr.value);
  // return;
  // if (fileDataSotr.value.length > 0) {
  let paramsData = {
    organizationAdminId: userInfIdData.value,
    files: fileDataSotr.value
  };
  // console.log('🦄-----paramsData-----', paramsData);
  // return;
  //新增讲师资质文件
  const res = await leaderLecturerSaveLeaderFile(paramsData);
  // } leaderLecturerSaveLeaderFile
  // if (fileDataSotrTow.value.length > 0) {
  let paramsData2 = {
    organizationAdminId: userInfIdData.value,
    files: fileDataSotrTow.value
  };
  // console.log('🦄-----paramsData-----', paramsData);
  // return;
  // 新增领队资质文件
  const reset = await leaderLecturerSaveLecturerFile(paramsData2);
  // }
  // getLoading.value = false;
};
//文件处理
const fileData = () => {
  //领队资质文件
  let institutionLicense = formFile.value.institutionLicense;
  //讲师资质文件
  let qualificationDocuments = formFile.value.qualificationDocuments;
  if (institutionLicense.length > 0) {
    setFilesFn(institutionLicense, "BUSINESS_LICENSE");
  }
  if (qualificationDocuments.length > 0) {
    setFilesFnTow(qualificationDocuments, "BUSINESS_LICENSE");
  }
};
const fileDataSotr = ref([]);
const fileDataSotrTow = ref([]);
const setFilesFn = (val, type) => {
  for (let index = 0; index < val.length; index++) {
    const element = val[index].fileIdentifier;
    // console.log("🌵-----element-----", element);
    fileDataSotr.value.push({
      fileType: type,
      fileIdentifier: element,
      sortOrder: index + 1
    });
  }
};
const setFilesFnTow = (val, type) => {
  for (let index = 0; index < val.length; index++) {
    const element = val[index].fileIdentifier;
    // console.log("🌵-----element-----", element);
    fileDataSotrTow.value.push({
      fileType: type,
      fileIdentifier: element,
      sortOrder: index + 1
    });
  }
};
// 获取验证码
const isCaptchaLoading = ref(false);
const isCaptchaDisabled = ref(false);

const getCaptcha = async phone => {
  if (!phone) {
    isCaptchaDisabled.value = true;
    ElMessage({ message: "手机号不能为空" });
    return;
  }
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(phone)) {
    isCaptchaDisabled.value = true;
    ElMessage({ message: "电话号码格式不正确", type: "warning" });
    return;
  }

  isCaptchaLoading.value = true;
  try {
    const params = {
      phone: encryption(phone),
      codeType: "VERIFICATION_CODE"
    };
    const { code, msg } = await getPhonecode(params);
    if (code === 200) {
      ElMessage({
        message: "验证码已发送",
        type: "success"
      });
    } else {
      ElMessage({
        message: msg,
        type: "error"
      });
    }
  } catch (error) {
    console.error("获取验证码失败:", error);
    ElMessage({
      message: "获取验证码失败",
      type: "error"
    });
  } finally {
    isCaptchaLoading.value = false;
  }
};
// 取消
const cancelForm = () => {
  router.push({
    path: "/welcome"
  });
};
// 文件上传
const beforeUpload = async (file, item) => {
  console.log("🦄-----item-----", item);
  console.log("💗beforeUpload---------->", file);
  // let fileName = file.name.split(".");
  // let fileStyle = ["ppt", "pptx"];
  // if (!fileStyle.includes(fileName[1])) {
  //   ElMessage.error("请上传ppt文件");
  //   return;
  // }
  // console.log("💗beforeUpload---------->33333", file);
  let { code, data } = await uploadFile(file);
  console.log("🎁-----code, data-----", code, data);
  if (code === 200) {
    // console.log("🌳-----data-----", data);
    formFile.value[item].push(data);
    console.log("🎁----- form.fileData-----", formFile.value);
  }
};
//删除文件
const getDeleted = (item, index) => {
  // console.log("🌈-----item, index-----", item, index);
  // return
  formFile.value[item].splice(index, 1);
  // maxUpload.value.uploadFile = 0;
};
const newData = ref();
const userRole = ref([]);
// 获取用户信息
const getUserinfo = async data => {
  try {
    const { code, data, msg } = await adminFindById({
      id: userInfIdData.value
    });
    // console.log("🎁-----data-----", code, data, msg);
    if (code == 200 && data) {
      form.value = data;

      richFlag.value = true;
      newData.value = JSON.parse(JSON.stringify(data));
      // 存储解密后的身份证号，用于后续显示
      if (data.idNumberCt) {
        newData.value.idNumberDecrypted = decrypt(data.idNumberCt);
      }
      if (data?.roles?.length > 0) {
        data.roles.forEach(item => {
          if (!item?.files?.length) return;
          const roleConfig = {
            2: {
              label: "讲师资质文件",
              prop: "qualificationDocuments",
              formFileKey: "qualificationDocuments"
            },
            3: {
              label: "领队资质文件",
              prop: "institutionLicense",
              formFileKey: "institutionLicense"
            }
          };
          const config = roleConfig[item.idCode];
          if (!config) return;
          // 添加表单配置
          formData.value.push({
            label: config.label,
            idCode: item.idCode,
            type: "upload",
            prop: config.prop,
            width: "400px"
          });
          // 添加文件数据
          item.files.forEach(val => {
            formFile.value[config.formFileKey].push(val.uploadFile);
          });
        });
      }
    }
  } catch (error) {
    console.log("catch-----error-----", error);
  }
  console.log("🐳-----formFile.value-----", formFile.value);
};
const fileType = val => {
  console.log("🐠-----val-----", val);
  const imageTypes = ["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg"];
  // 获取文件扩展名（转换为小写以方便比较）
  const extension = val.split(".").pop().toLowerCase();
  if (imageTypes.includes(extension)) {
    return "image";
  } else {
    return "download";
  }
};
// const isView = ref(true);
const isViewFn = (val, index) => {
  console.log("🎁-----index-----", index);
  formData.value[index].isView = !formData.value[index].isView;
  if (val === "idNumber") {
    if (formData.value[index].isView) {
      // 切换为显示掩码数据
      form.value[val] = newData.value[val];
    } else {
      // 切换为显示解密数据
      form.value[val] =
        newData.value.idNumberDecrypted || decrypt(newData.value.idNumberCt);
    }
  } else if (val === "phone") {
    if (formData.value[index].isView) {
      // 切换为显示掩码数据
      form.value[val] = newData.value[val];
    } else {
      // 切换为显示解密数据
      form.value[val] = decrypt(newData.value.phoneCt);
    }
  }
};

// 处理微信回调
const handleWxCallback = (code, state) => {
  // 检查是否已经处理过此次绑定请求
  const processedKey = `wx_bind_processed_${code}_${state}`;
  if (sessionStorage.getItem(processedKey)) {
    console.log("已处理过此绑定请求，不再重复处理");
    isWxCallbackProcessed.value = true;
    return;
  }

  const storedState = sessionStorage.getItem("wx_login_state");

  // 状态码是否一致
  if (state !== storedState) {
    ElMessage.error("微信状态不一致，请重新扫码");
    isWxCallbackProcessed.value = true; // 标记为已处理
    return;
  }

  console.log(form.value.isBindWx, "isbind");

  // 状态码一致，处理微信绑定
  if (form.value.isBindWx) {
    // 如果当前已绑定微信，这里可以处理解绑逻辑（如果需要的话）
    ElMessage.info("当前账号已绑定微信");
    isWxCallbackProcessed.value = true; // 标记为已处理
  } else {
    // 如果当前未绑定微信，处理绑定逻辑
    bindCode(code, processedKey);
  }
};

// 绑定账号
const bindCode = async (code, processedKey) => {
  console.log(code, "code");
  const params = {
    code: code,
    userId: route.query.id,
    userType: "ORGANIZATION_ADMIN"
  };

  try {
    const res = await getBindCode(params, {
      operateLogType: "ACCOUNT_MANAGEMENT",
      operateType: `对账号“${form.value.account}”完成微信绑定操作`
    });
    if (res.code === 200) {
      ElMessage.success("绑定成功");

      // 记录已处理状态到会话存储
      sessionStorage.setItem(processedKey, "true");

      // 重新获取用户信息
      await getUserinfo();

      // 重置二维码组件状态
      if (wxQrCodeRef.value) {
        wxQrCodeRef.value.resetInit();
      }

      // 标记为已处理
      isWxCallbackProcessed.value = true;
    } else {
      ElMessage.warning(res.msg);
      isWxCallbackProcessed.value = true; // 标记为已处理
    }
  } catch (err) {
    isWxCallbackProcessed.value = true; // 标记为已处理
    throw new Error(err);
  }
};

// 解绑微信
const handleChangeWx = debounce(
  async () => {
    if (unbindWxLoading.value) return;
    unbindWxLoading.value = true;
    try {
      const params = {
        userId: userInfIdData.value,
        userType: "ORGANIZATION_ADMIN"
      };

      const res = await unbindWxCode(params, {
        operateLogType: "ACCOUNT_MANAGEMENT",
        operateType: `对账号"${form.value.account}"完成微信解绑操作`
      });

      if (res.code === 200) {
        ElMessage.success("微信解绑成功");

        // 关闭对话框
        showUnbindDialog.value = false;

        // 重新获取用户信息
        await getUserinfo();
      }
      console.log("微信解绑完成");
    } catch (error) {
      ElMessage.error("微信解绑失败，请重试");
    } finally {
      unbindWxLoading.value = false;
    }
  },
  1000,
  { immediate: true }
);

// 构建包含当前 query 参数的重定向路径
const redirectPathWithQuery = computed(() => {
  const currentPath = route.path;
  const queryParams = new URLSearchParams();

  // 将当前的 query 参数添加到 URLSearchParams 中，排除微信回调参数
  Object.keys(route.query).forEach(key => {
    // 排除微信回调相关的参数
    if (key !== "code" && key !== "state") {
      if (route.query[key] !== null && route.query[key] !== undefined) {
        queryParams.append(key, route.query[key]);
      }
    }
  });

  // 如果有 query 参数，则构建完整路径
  const queryString = queryParams.toString();
  return queryString ? `${currentPath}?${queryString}` : currentPath;
});

// 检测是否有微信回调参数且未处理
const hasWxCallbackParams = computed(() => {
  return !!(
    route.query.code &&
    route.query.state &&
    !isWxCallbackProcessed.value
  );
});

// 监听路由参数变化，处理微信回调
watch(
  () => route.query,
  newQuery => {
    const { code, state } = newQuery;
    console.log(code, "code");
    console.log(state, "state");

    if (code && state && !isWxCallbackProcessed.value) {
      handleWxCallback(code, state);
    }
  },
  { immediate: true }
);

//身份证输入筛选
const filterChineseInput = (value, prop) => {
  const filteredValue = value.replace(/[^\dX]/gi, "");
  form.value[prop] = filteredValue.toUpperCase();
};
</script>

<template>
  <div class="containers">
    <div class="table_content">
      <el-form ref="formRef" :rules="rules" :model="form" style="flex-grow: 1">
        <el-descriptions title="" :column="2" border>
          <el-descriptions-item
            v-for="(item, index) in formData"
            :key="index"
            label-width="180"
            label-align="center"
            label-class-name="my-label"
            :span="
              item.prop === 'phone'
                ? formData.some(i => i.prop === 'code')
                  ? 1
                  : 2
                : item.prop === 'code'
                  ? 1
                  : 2
            "
          >
            <template #label>
              <span v-if="item.prop === 'idNumber' || item.prop === 'email'" />
              <span v-else>
                <span v-if="item.check" class="star">*</span>
              </span>
              {{ item.label }}
            </template>
            <el-form-item
              v-if="richFlag"
              :prop="item.prop"
              :inline-message="item.check"
              style="margin-bottom: 0"
              :show-message="true"
              error-placement="right"
            >
              <!-- input输入 -->
              <template v-if="item.type === 'input'">
                <el-input
                  v-model="form[item.prop]"
                  :placeholder="item.placeholder"
                  :style="{ width: item.width }"
                  :disabled="
                    (item.prop === 'phone' || item.prop === 'idNumber') &&
                    item.isView &&
                    newData[item.prop]?.length > 0
                  "
                  :maxlength="item.maxLength"
                  :show-word-limit="item.maxLength"
                  @input="
                    item.prop === 'idNumber'
                      ? filterChineseInput($event, item.prop)
                      : ''
                  "
                >
                  <template
                    v-if="
                      (item.prop === 'phone' || item.prop === 'idNumber') &&
                      form[item.prop]?.length > 0
                    "
                    #suffix
                  >
                    <span v-if="form[item.prop]?.length > 0">
                      <el-icon
                        v-if="item.isView"
                        style="cursor: pointer"
                        @click="isViewFn(item.prop, index)"
                      >
                        <Hide />
                      </el-icon>
                      <el-icon
                        v-else
                        style="cursor: pointer"
                        @click="isViewFn(item.prop, index)"
                      >
                        <View />
                      </el-icon>
                    </span>
                  </template>
                </el-input>
                <!-- 获取验证码 -->
                <div v-if="item.prop === 'code'" class="Vacode">
                  <el-button
                    v-countdown="{
                      value: 60,
                      callback: () => getCaptcha(form.phone),
                      countdownText: 's后重新获取',
                      loadingText: '发送中...'
                    }"
                  >
                    获取验证码
                  </el-button>
                </div>
              </template>

              <!-- 二维码展示 -->
              <template v-else-if="item.type === 'img'">
                <span v-if="!form.isBindWx" class="isQR">当前未绑定</span>
                <div v-else>
                  <span>已绑定,
                    <el-link
                      type="primary"
                      underline="hover"
                      @click="showUnbindDialog = true"
                    >
                      解绑
                    </el-link>
                  </span>
                </div>
                <div v-if="!form.isBindWx" class="codeQR">
                  <!-- 只有在没有微信回调参数时才显示二维码 -->
                  <WxQrCode
                    v-if="!hasWxCallbackParams"
                    ref="wxQrCodeRef"
                    :redirectPath="redirectPathWithQuery"
                  />
                  <!-- 有微信回调参数时显示处理中状态 -->
                  <div v-else class="processing-status">
                    <el-icon class="is-loading">
                      <Loading />
                    </el-icon>
                    <span>正在处理微信绑定...</span>
                  </div>
                </div>
              </template>

              <!-- 示例：上传组件 -->
              <template v-else-if="item.type === 'upload'">
                <div class="upload_box">
                  <el-upload
                    action="#"
                    :show-file-list="false"
                    class="upload-demo"
                    accept=".pdf,.jpg,.jpeg,.png,.docx,.xlsx"
                    :http-request="() => {}"
                    :before-upload="file => beforeUpload(file, item.prop)"
                  >
                    <img :src="uploadImg" alt="">
                  </el-upload>

                  <template
                    v-for="(item2, index2) in formFile[item.prop]"
                    :key="index2"
                  >
                    <FileItem
                      isNeedDelte
                      :data="item2"
                      :index="index2"
                      style="width: 33%; min-width: 130px"
                      @delete="getDeleted(item.prop, index2)"
                    />
                  </template>
                </div>
              </template>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>
      <div class="table_bottom">
        <el-button type="default" @click="cancelForm"> 取消 </el-button>
        <el-button type="primary" :loading="getLoading" @click="submitForm">
          保存
        </el-button>
      </div>
    </div>

    <!-- 解绑微信确认对话框 -->
    <el-dialog
      v-model="showUnbindDialog"
      title="解绑微信"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="dialog-content">
        <span>确定要解绑当前微信账号吗？</span>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showUnbindDialog = false">取消</el-button>
          <el-button
            type="primary"
            :loading="unbindWxLoading"
            @click="handleChangeWx"
            >
确定
</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  box-sizing: border-box;
  width: calc(100% - 48px);
  height: 100%;
  background: #f0f2f5;

  .table_content {
    display: flex;
    flex-direction: column;
    height: 88vh;
    box-sizing: border-box;
    padding: 20px;
    margin-top: 20px;
    background-color: rgb(255 255 255);

    .star {
      margin-right: 3px;
      color: red;
    }

    .el-descriptions-item {
      display: flex;
      align-items: center;
    }

    .Vacode {
      margin-left: 20px;
      display: inline-block;
    }

    .isQR {
      margin-right: 240px;
    }

    .codeQR {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .processing-status {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 10px;
      padding: 20px;
      color: #666;
      font-size: 14px;
    }
  }

  .table_bottom {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
  }
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}

:deep(.my-label) {
  background: #e1f5ff !important;
}

:deep(.my-content) {
  background: var(--el-color-danger-light-9);
}
.upload-demo {
  display: flex;
  align-items: center;
}
.fileOther {
  margin-left: 20px;
  display: flex;
  align-items: center;
  line-height: 22px;
  width: 420px;
  .title {
    color: #0e1832;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    white-space: nowrap;
  }
  .link {
    width: 20px;
    height: 20px;
    margin: 0 10px;
    cursor: pointer;
  }
  .fileName {
    color: #3876fd;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .linkDetele {
    width: 20px;
    height: 20px;
    margin-left: 10px;
    cursor: pointer;
  }
}
:deep(.fileOther) {
  width: auto;
}
.upload_box {
  display: flex;
  width: 100%;
  flex-direction: column;
}

:deep(.el-dialog) {
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  margin: 0px !important;
}
.el-link {
  line-height: 1.2;
  margin-bottom: 2px;
}
</style>
