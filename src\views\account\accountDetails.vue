<script setup>
import { ref, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getaccInfo, isaccFreeze } from "@/api/leaderLecturer.js";
import { requestTo } from "@/utils/http/tool";
import { formatTime } from "@/utils/index";
import { decrypt, encryption } from "@/utils/SM4.js";
import { usePopup } from "vue-hooks-pure";
import Popup from "../institution/components/popup.vue";
import { resetPassword } from "@/api/institution";
import { ElMessage, ElMessageBox } from "element-plus";

const newData = ref();
const router = useRouter();
const route = useRoute();
const richFlag = ref(false);
const dialogFormVisible = ref(false);
// 冻结状态
const isFrozen = ref(false);

onMounted(() => {
  getacctable();
});
const reset = () => {
  dialogFormVisible.value = false;
  // usePopup(popup, {
  //   title: "重置密码确认",
  //   api: resetPassword,
  //   id: newData.value.organizationAdmin.id,
  //   name: newData.value.organizationAdmin.name,
  //   phone: decrypt(newData.value.organizationAdmin.phoneCt),
  //   account: newData.value.organizationAdmin.account
  // });
};

// 表头
const tableHeader = ref([
  { label: "账号ID", value: "", width: "107px", key: "id" },
  { label: "姓名", value: "", width: "107px", key: "name" },
  { label: "创建时间", value: "", width: "107px", key: "createdAt" },
  { label: "账号", value: "", width: "107px", key: "account" },
  { label: "手机号", value: "", width: "107px", key: "phoneCt" },
  { label: "微信绑定", value: "", width: "107px", key: "" },
  { label: "身份证号", value: "", width: "107px", key: "idNumberCt" },
  { label: "邮箱", value: "", width: "107px", key: "email" },
  { label: "角色", value: "", width: "107px", key: "roles" }
]);
// const roleMap = {
//   1: "管理员",
//   2: "讲师",
//   3: "领队"
// };

const getacctable = async () => {
  const [err, result] = await requestTo(getaccInfo({ id: route.query?.id }));
  newData.value = JSON.parse(JSON.stringify(result));
  richFlag.value = true;
  if (err) {
    console.error("获取数据失败:", err);
    return;
  }

  // 获取冻结状态
  isFrozen.value = result?.freeze || false;

  tableHeader.value = tableHeader.value.map(item => {
    let value = result[item.key] ?? "--";

    // 处理时间格式
    if (item.key === "createdAt" && result[item.key]) {
      value = formatTime(result[item.key], "YYYY-MM-DD HH:mm");
    }

    // 处理角色
    if (item.key === "roles" && Array.isArray(result[item.key])) {
      value = result[item.key].map(role => role.name).join("、") || "--";
    }

    // 处理手机号
    if (item.key === "phoneCt") {
      // value = result.phoneCt ? decrypt(result.phoneCt) : "--";
      value = result?.phone;
    }

    // 处理身份证号
    if (item.key === "idNumberCt") {
      // value = result.idNumberCt ? decrypt(result.idNumberCt) : "--";
      value = result?.idNumber || "--";
    }

    return { ...item, value };
  });

  console.log("🌵账号管理详情", tableHeader.value);
};

// 编辑信息
const editInfor = () => {
  router.push({
    path: "/account/accountEdit",
    query: { id: route.query?.id, title: true }
  });
};

const isFrozenLoading = ref(false);
// 获取冻结/解冻按钮文本
const getButtonText = isFrozen => {
  return isFrozen ? "解冻" : "冻结";
};

// 冻结/解冻操作
const isSubmitting = ref(false);
const handleFreeze = async () => {
  const willFreeze = !isFrozen.value;
  const confirmText = willFreeze
    ? "你确定要冻结该账户吗?"
    : "你确定要解冻该账户吗?";
  const confirmTitle = willFreeze ? "确认冻结" : "确认解冻";
  const successMessage = willFreeze ? "已冻结" : "已解冻";

  try {
    await ElMessageBox.confirm(confirmText, confirmTitle, {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: ""
    });

    // 如果已经在提交中，则不再重复发起请求
    if (isSubmitting.value) return;
    isSubmitting.value = true;
    isFrozenLoading.value = true;
    const params = {
      id: route.query?.id,
      freeze: willFreeze
    };
    const operateLog = {
      operateLogType: "ACCOUNT_MANAGEMENT",
      operateType: willFreeze ? "冻结了" : "解冻了",
      operatorTarget: tableHeader.value[1].value // 姓名
    };
    const { code, msg } = await isaccFreeze(params, operateLog);
    if (code === 200) {
      ElMessage({
        message: successMessage,
        type: "success"
      });
      isFrozen.value = willFreeze;
    } else {
      ElMessage.error(msg);
    }
  } catch (error) {
    // 操作取消
  } finally {
    // 无论请求成功或失败，都重置提交状态
    isSubmitting.value = false;
    isFrozenLoading.value = false;
  }
};
</script>

<template>
  <div class="containers">
    <div class="tabHeastyle">
      <el-descriptions class="description-table" title="" :column="3" border>
        <template v-for="(item, index) in tableHeader" :key="index">
          <el-descriptions-item
            width="120px"
            :label="item.label"
            label-align="center"
          >
            {{ item.value }}
          </el-descriptions-item>
        </template>
      </el-descriptions>
    </div>
    <div class="btn_bottom">
      <div class="left-area">
        <el-button type="danger" @click="dialogFormVisible = true">
          重置密码
        </el-button>
      </div>
      <div class="right-area">
        <el-text v-if="isFrozen" type="danger" class="frozen-tip">
          本账号已冻结
        </el-text>
        <el-button
          type="primary"
          plain
          :loading="isFrozenLoading"
          @click="handleFreeze"
        >
          {{ getButtonText(isFrozen) }}
        </el-button>
        <el-button type="primary" @click="editInfor">编辑信息</el-button>
      </div>
    </div>
    <Popup
      v-if="richFlag"
      :id="newData?.id"
      v-model:dialogFormVisible="dialogFormVisible"
      :api="resetPassword"
      :name="newData?.name"
      :phone="decrypt(newData?.phoneCt)"
      :account="newData?.account"
      :logOut="false"
      operateLogType="ACCOUNT_MANAGEMENT"
      @reset="reset"
    />
  </div>
</template>

<style lang="scss" scoped>
.containers {
  box-sizing: border-box;
  width: calc(100% - 48px);
  height: 100%;
  padding: 24px;
  background: #fff;

  .tabHeastyle {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
  }

  .btn_bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 50px;
    width: 100%;

    .left-area {
      display: flex;
      align-items: center;
    }

    .right-area {
      display: flex;
      align-items: center;
      gap: 15px;
    }
  }

  .frozen-tip {
    margin-right: 8px;
    font-weight: bold;
  }

  .description-table {
    width: 100%;
  }
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}
</style>
