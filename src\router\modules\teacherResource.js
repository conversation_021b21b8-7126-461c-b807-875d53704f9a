// import { $t } from "@/plugins/i18n";
import { teacherResource } from "@/router/enums.js";
import {
  acCodes<PERSON>ist,
  reCodesList,
  teacherCodesList
} from "@/router/accidCode.js";
import AccountIcon from "@/assets/home/<USER>";
import AccountIconActive from "@/assets/home/<USER>";
import { KeepAlive } from "vue";
export default {
  path: "/teacher",
  redirect: "/invitation/management",
  meta: {
    icon: "ri:information-line",
    title: "师资",
    imgIcon: AccountIcon,
    imgIconActive: AccountIconActive,
    rank: teacherResource,
    idCode: teacherCodesList.baseCode
  },
  children: [
    // {
    //   path: "/invitation/management",
    //   name: "invitation",
    //   redirect: "/invitation/management/index",
    //   // component: () => import("@/views/account/teamManage.vue"),
    //   meta: {
    //     title: "邀请管理"
    //     // idCode: acCodesList.teamManage
    //   },
    //   children: [
    //     {
    //       path: "/invitation/management/index",
    //       name: "InvitationManagement",
    //       component: () =>
    //         import("@/views/teacherResource/invitationManagement.vue"),
    //       meta: {
    //         title: "邀请管理",
    //         keepAlive: true,
    //         idCode: teacherCodesList.invitationManagement
    //       }
    //     },
    //     {
    //       path: "/course/current/detail",
    //       name: "courseCurrent",
    //       component: () => import("@/views/course/currentDetails.vue"),
    //       meta: {
    //         title: "当期详情",
    //         idCode: reCodesList.currentDetail,
    //         showLink: false
    //       }
    //     },
    //     {
    //       path: "/invitation/details",
    //       name: "invitationDetails",
    //       component: () =>
    //         import("@/views/teacherResource/teachersResourceDetail.vue"),
    //       meta: {
    //         title: "师资详情",
    //         showLink: false
    //         // idCode: reCodesList.joinCourse
    //       }
    //     }
    //   ]
    // },
    // {
    //   path: "/teacher/faculty",
    //   // path: "/institutional/faculty/index",
    //   name: "teacherFaculty",
    //   redirect: "/teacher/institutional/faculty",
    //   // component: () => import("@/views/teacherResource/institutionalFaculty.vue"),
    //   meta: {
    //     title: "机构师资"
    //   },
    //   children: [
    //     {
    //       path: "/teacher/institutional/faculty",
    //       name: "InstitutionalFacultyIndex",
    //       component: () =>
    //         import("@/views/teacherResource/institutionalFaculty.vue"),
    //       meta: {
    //         title: "机构师资",
    //         keepAlive: true,
    //         idCode: teacherCodesList.institutionalFaculty
    //       }
    //     },
    //     {
    //       path: "/teacher/institution/join/course",
    //       name: "institutionJoinCourse",
    //       component: () =>
    //         import("@/views/teacherResource/joinCourseInstitution.vue"),
    //       meta: {
    //         title: "参与课程",
    //         showLink: false,
    //         idCode: reCodesList.joinCourse
    //       },
    //       children: [
    //         {
    //           path: "/institution/course/current/detail",
    //           name: "institutionCourseCurrent",
    //           component: () => import("@/views/course/currentDetails.vue"),
    //           meta: {
    //             title: "当期详情",
    //             idCode: reCodesList.currentDetail,
    //             showLink: false
    //           }
    //         },
    //         {
    //           path: "/institution/invite/course",
    //           name: "institutionInviteCourse",
    //           component: () =>
    //             import("@/views/teacherResource/components/selectCourse.vue"),
    //           meta: {
    //             title: "邀请参课",
    //             showLink: false,
    //             keepAlive: true
    //             // idCode: reCodesList.joinCourse
    //           },
    //           children: [
    //             {
    //               path: "/institution/invite/period",
    //               name: "institutionInvitePeriod",
    //               component: () =>
    //                 import(
    //                   "@/views/teacherResource/components/selectPeriod.vue"
    //                 ),
    //               meta: {
    //                 title: "选择课期",
    //                 showLink: false
    //                 // idCode: reCodesList.joinCourse
    //               }
    //             }
    //           ]
    //         }
    //       ]
    //     },
    //     {
    //       path: "/institution/expert/evaluation",
    //       name: "institutionEvaluation",
    //       component: () =>
    //         import("@/views/teacherResource/expertEvaluation.vue"),
    //       meta: {
    //         title: "专家评价",
    //         showLink: false
    //         // idCode: reCodesList.joinCourse
    //       },
    //       children: [
    //         {
    //           path: "/expert/evaluation/current/detail",
    //           name: "expertEvaluationCurrent",
    //           component: () => import("@/views/course/currentDetails.vue"),
    //           meta: {
    //             title: "当期详情",
    //             idCode: reCodesList.currentDetail,
    //             showLink: false
    //           }
    //         }
    //       ]
    //     },
    //     {
    //       path: "/institution/details",
    //       name: "institutionDetails",
    //       component: () =>
    //         import("@/views/teacherResource/teachersResourceDetail.vue"),
    //       meta: {
    //         title: "详情",
    //         showLink: false
    //         // idCode: reCodesList.joinCourse
    //       }
    //     }
    //   ]
    // },
    // {
    //   path: "/teacher/resource",
    //   name: "teacherResourcePool",
    //   redirect: "/teacher/resource/pool/index",
    //   meta: {
    //     title: "师资库"
    //   },
    //   children: [
    //     {
    //       path: "/teacher/resource/pool/index",
    //       name: "TeacherResourcePoolIndex",
    //       component: () =>
    //         import("@/views/teacherResource/teacherResourcePool.vue"),
    //       meta: {
    //         title: "师资库",
    //         keepAlive: true,
    //         idCode: teacherCodesList.teacherResourcePool
    //       }
    //     },
    //     {
    //       path: "/teacher/join/course",
    //       name: "teacherJoinCourse",
    //       component: () =>
    //         import("@/views/teacherResource/teachersjoinCourse.vue"),
    //       meta: {
    //         title: "参与课程",
    //         showLink: false
    //         // idCode: reCodesList.joinCourse
    //       },
    //       children: [
    //         {
    //           path: "/teacher/course/current/detail",
    //           name: "teacherCourseCurrent",
    //           component: () => import("@/views/course/currentDetails.vue"),
    //           meta: {
    //             title: "当期详情",
    //             idCode: reCodesList.currentDetail,
    //             showLink: false
    //           }
    //         },
    //         {
    //           path: "/teachers/invite/course",
    //           name: "teachersInviteCourse",
    //           component: () =>
    //             import("@/views/teacherResource/components/selectCourse.vue"),
    //           meta: {
    //             title: "邀请参课",
    //             showLink: false,
    //             keepAlive: true
    //             // idCode: reCodesList.joinCourse
    //           },
    //           children: [
    //             {
    //               path: "/teachers/invite/period",
    //               name: "teachersInvitePeriod",
    //               component: () =>
    //                 import(
    //                   "@/views/teacherResource/components/selectPeriod.vue"
    //                 ),
    //               meta: {
    //                 title: "选择课期",
    //                 showLink: false
    //                 // idCode: reCodesList.joinCourse
    //               }
    //             }
    //           ]
    //         }
    //       ]
    //     },
    //     {
    //       path: "/teacher/invite/course",
    //       name: "TeacherInviteCourse",
    //       component: () =>
    //         import("@/views/teacherResource/components/selectCourse.vue"),
    //       meta: {
    //         title: "邀请参课",
    //         showLink: false,
    //         keepAlive: true
    //         // idCode: reCodesList.joinCourse
    //       },
    //       children: [
    //         {
    //           path: "/teacher/invite/period",
    //           name: "teacherInvitePeriod",
    //           component: () =>
    //             import("@/views/teacherResource/components/selectPeriod.vue"),
    //           meta: {
    //             title: "选择课期",
    //             showLink: false
    //             // idCode: reCodesList.joinCourse
    //           }
    //         }
    //       ]
    //     },
    //     {
    //       path: "/teacher/expert/evaluation",
    //       name: "teacherEvaluation",
    //       component: () =>
    //         import("@/views/teacherResource/expertEvaluation.vue"),
    //       meta: {
    //         title: "专家评价",
    //         showLink: false
    //         // idCode: reCodesList.joinCourse
    //       },
    //       children: [
    //         {
    //           path: "/expert/evaluation/current/detail",
    //           name: "expertEvaluationCurrent",
    //           component: () => import("@/views/course/currentDetails.vue"),
    //           meta: {
    //             title: "当期详情",
    //             idCode: reCodesList.currentDetail,
    //             showLink: false
    //           }
    //         }
    //       ]
    //     },
    //     {
    //       path: "/teacher/details",
    //       name: "teacherDetails",
    //       component: () =>
    //         import("@/views/teacherResource/teachersResourceDetail.vue"),
    //       meta: {
    //         title: "详情",
    //         showLink: false
    //         // idCode: reCodesList.joinCourse
    //       }
    //     }
    //   ]
    // }
  ]
};
