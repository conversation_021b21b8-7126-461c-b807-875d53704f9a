<script setup>
import { onMounted, ref } from "vue";
import { formatTime } from "@/utils/index";
import { coursePeriodAll } from "@/api/period.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage } from "element-plus";
import { useRouter, useRoute } from "vue-router";
onMounted(() => {
  getTableList();
});
const route = useRoute();
const router = useRouter();
// 搜索表单
const form = ref({
  startTime: "",
  endTime: "",
  name: "",
  organizationName: "",
  courseTypeName: ""
});
// 表格数据
const tableData = ref([]);
const params = ref({
  page: 1,
  size: 10,
  sort: "createdAt,desc",
  totalElements: 0
});
const courseTypeoptions = ref([
  {
    value: 0,
    label: "全部"
  }
]);
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort,
    courseId: route.query.courseId,
    copy: true
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  // console.log("🍧-----paramsData-----", paramsData);
  const [err, result] = await requestTo(coursePeriodAll(paramsData));
  console.log("🎁-----result--555---", result);
  if (result) {
    tableData.value = result?.content;

    params.value.totalElements = result.totalElements;
  } else {
    ElMessage.error(err);
  }
  getListLoading.value = false;
};
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTableList();
};
// 复制当前课期
const getId = val => {
  router.push({
    path: "/course/periodCopy/create",
    query: {
      copyId: val.id,
      id: route.query.courseId,
      name: val.name,
      termNumber: val.termNumber,
      type: "copy"
    }
  });
};
// 课程状态
const stateOptions = [
  {
    value: "1",
    label: "全部"
  },
  {
    value: "ONLINE",
    label: "上架"
  },
  {
    value: "NOT_LISTED",
    label: "未上架"
  },
  {
    value: "ONLINE_UNDER_REVIEW",
    label: "上架审核"
  },
  {
    value: "OFFLINE",
    label: "下架"
  },
  {
    value: "OFFLINE_UNDER_REVIEW",
    label: "下架审核"
  },
  {
    value: "COMPLETED",
    label: "已完成"
  }
];
// 获取领队讲师姓名
const getName = val => {
  let res = [];
  if (!val?.length) return;
  val.map(item => {
    res.push(item.name);
  });
  return res.join("、");
};
// 获取课程状态
const getSatte = val => {
  let res = "";
  stateOptions?.map(item => {
    if (item.value === val) {
      res = item.label;
    }
  });
  return res;
};
</script>

<template>
  <div class="containers">
    <div class="con_table">
      <el-table
        :data="tableData"
        height="550"
        table-layout="fixed"
        :header-cell-style="{ backgroundColor: '#fafafa', color: '#565353' }"
        highlight-current-row
      >
        <el-table-column prop="termNumber" label="期号" min-width="120">
          <template #default="scope">
            <el-text>
              {{ scope.row.termNumber || "0" }}
            </el-text>
          </template>
        </el-table-column>
        <el-table-column prop="openTime" label="上课时间" align="left">
          <template #default="scope">
            <div>
              {{
                formatTime(scope.row.openTime, "YYYY-MM-DD HH:mm:ss") || "--"
              }}
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="createdAt" label="创建时间" align="left">
          <template #default="scope">
            <div>
              {{
                formatTime(scope.row.createdAt, "YYYY-MM-DD HH:mm:ss") || "--"
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="buyType" label="购买类型" align="left">
          <template #default="scope">
            <div>
              {{
                scope.row.buyType === "ORDINARY" ? "普通单" : "团购单" || "--"
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="leaders" label="领队" align="left">
          <template #default="scope">
            <div>
              {{ getName(scope.row.leaders) || "--" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="lecturers" label="讲师" align="left">
          <template #default="scope">
            <div>
              {{ getName(scope.row.lecturers) || "--" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          width="200px"
          prop="coursePeriodState"
          label="课程状态"
        >
          <template #default="scope">
            <div>
              {{ getSatte(scope.row.coursePeriodState) || "--" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="center">
          <template #default="scope">
            <div class="btnse" @click="getId(scope.row)">复制当前期</div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="con_pagination">
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="params.page"
        v-model:page-size="params.size"
        class="pagination"
        background
        layout="total, prev, pager, next, jumper"
        :total="params.totalElements"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  box-sizing: border-box;
  width: calc(100% - 48px);
  height: 100%;
  padding: 24px;
  background: #fff;

  .con_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: fit-content;
    margin-bottom: 24px;

    .titles {
      font-size: 20px;
      font-weight: bold;
      color: #606266;
    }
  }

  .con_search {
    display: flex;
    align-items: center;
    width: 100%;
    height: fit-content;
    margin-bottom: 24px;
  }

  .con_table {
    width: calc(100% - 25px);
    margin-bottom: 24px;
    margin-left: 25px;

    .btnse {
      color: #409eff;
      cursor: pointer;
    }
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }
}
</style>
