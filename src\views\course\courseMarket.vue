<script setup>
import { MarketHook } from "./utils/courseMarket.jsx";
import TeacherCard from "./components/teacherCard.vue";
import TeacherCard2 from "./components/teacherCard2.vue";
import { useTable, PlusPagination } from "plus-pro-components";
import { ref, onMounted } from "vue";
import { findAllCourseType } from "@/api/period.js";
import { requestTo } from "@/utils/http/tool";
import router from "@/router/index";
const {
  tableData1,
  tableData2,
  params,
  activeName,
  handleClick,
  paramsChild,
  handleClickChild,
  pagination,
  handlePageChange,
  handleSizeChange
} = MarketHook();

// 课程类型选项
const courseTypeoptions = ref([]);

// 获取课程类型数据
const courseTypeFindApi = async () => {
  try {
    const res = await requestTo(findAllCourseType());
    if (res.code === 200) {
      courseTypeoptions.value = transformArray(res.data);
    }
  } catch (error) {
    console.error("获取课程类型失败", error);
  }
};

// 转换数据格式
const transformArray = arr => {
  return arr.map(item => {
    const obj = {
      value: item.id,
      label: item.name
    };
    if (item.children && item.children.length > 0) {
      obj.children = transformArray(item.children);
    }
    return obj;
  });
};

// 在组件挂载时获取课程类型数据
onMounted(() => {
  courseTypeFindApi();
});

// 课程类型选择变化处理
const courseTypeChange = val => {
  if (val && val.length > 0) {
    form.value.courseTypeId = val;
  } else {
    form.value.courseTypeId = ["all"];
  }
};

// 搜索信息
const form = ref({
  name: "",
  courseTypeId: [],
  freeze: "",
  startTime: "",
  endTime: ""
});
const timeChange = val => {
  // console.log("时间变化", val);
};
const value1 = ref([]);
//搜索
const searchData = () => {
  params.value.page = 1;
  // getTableList();
};
// 重置
const setData = () => {
  form.value = {};
  form.value.courseTypeId = ["all"];
  form.value.freeze = 0;
  params.value.page = 1;
  value1.value = [];
  // getTableList();
};
// 清除数据
const clearEvt = val => {
  if (val === "name") {
    form.value.name = "";
  } else if (val === "time") {
    form.value.startTime = "";
    form.value.endTime = "";
  } else if (val === "courseTypeId") {
    form.value.courseTypeId = ["all"];
  }
};
const demo_tabs = ref(activeName.value[0].name);

// 发布课程
const PublishCourses = () => {
  router.push({
    path: "/course/market/PublishCourses",
    query: { type: "coursePublish" }
  });
};
</script>

<template>
  <div class="course-market">
    <div class="con_search">
      <el-form :model="form" :inline="true">
        <el-form-item label="课程">
          <el-input
            v-model.trim="form.name"
            placeholder="请输入"
            clearable
            @clear="clearEvt('name')"
          />
        </el-form-item>
        <el-form-item label="课程分类">
          <el-cascader
            v-model="form.courseTypeId"
            :options="courseTypeoptions"
            :show-all-levels="false"
            @change="courseTypeChange"
            @clear="clearEvt('courseTypeId')"
          />
        </el-form-item>
        <el-form-item label="开课时间">
          <el-date-picker
            v-model="value1"
            type="daterange"
            start-placeholder="请选择开始时间"
            end-placeholder="请选择结束时间"
            @change="timeChange"
            @clear="clearEvt('time')"
          />
        </el-form-item>
        <el-form-item label=" ">
          <div class="flex">
            <el-button type="primary" @click="searchData">搜索</el-button>
            <el-button @click="setData">重置</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div class="con_tabs">
      <div class="con_tabs_title">
        <el-button type="primary" @click="PublishCourses">发布课程</el-button>
      </div>
      <el-tabs v-model="demo_tabs" @tab-click="handleClick">
        <el-tab-pane
          v-for="item in activeName"
          :key="item.id"
          :label="item.name"
          :name="item.name"
        >
          <el-tabs @tab-click="handleClickChild">
            <el-tab-pane
              v-for="items in paramsChild"
              :key="items.label"
              :label="items.name"
              class="teacher-card"
            >
              <div v-if="items.name !== '收到申请'" class="teacher-card-scroll">
                <TeacherCard
                  :nameFather="item.name"
                  :nameChild="items.name"
                  :tableData="tableData1"
                />
              </div>
              <div v-else>
                <TeacherCard2 :tableData="tableData2" />
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>
      </el-tabs>
      <!-- 分页组件 -->
      <div class="pagination-container">
        <PlusPagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          class="pagination-container-index"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.course-market {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  // overflow: hidden;
  .con_search {
    height: fit-content;
    background-color: #fff;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    width: 100%;
    padding: 15px 20px 0 20px;
  }
}
.con_tabs {
  background-color: #fff;
  padding: 10px 20px;
  position: relative;
  .con_tabs_title {
    text-align: right;
  }
}

.teacher-card {
  height: calc(100vh - 37vh);
  display: flex;
  flex-direction: column;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none !important;
}

.pagination-container {
  // background-color: #fff;
  width: 100%;
  position: absolute;
  bottom: 10px;
  right: 0;
  padding: 10px 20px;
}
.pagination-container-index {
  display: flex;
  justify-content: flex-end;
}

:deep(.teacher-card-scroll) {
  overflow-y: auto;
  width: 100%;
  height: calc(100vh - 43vh);

  &::-webkit-scrollbar {
    width: 8px;
    background: #f1f1f1;
  }
  &::-webkit-scrollbar-track {
    border-radius: 10px;
    background: #f1f1f1;
  }
  &::-webkit-scrollbar-thumb {
    background: #afafaf;
  }
  &::-webkit-scrollbar-thumb:hover {
    background: #7c7c7c;
  }
}
</style>
