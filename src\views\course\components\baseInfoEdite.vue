<script setup>
import { ref, onMounted, reactive, computed, onActivated, watch } from "vue";
import {
  courseAdd,
  complexId,
  courseTypeFind,
  coursePeriodEdite,
  courseEdite,
  courseFindId,
  leaderLecturerFind,
  coursePeriodFind
} from "@/api/course.js";
import { findAllCourseType } from "@/api/period.js";
import { useRouter, useRoute } from "vue-router";
import { requestTo } from "@/utils/http/tool";
import { ElIcon, ElMessage, ElMessageBox, ElLoading } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import { uploadFile } from "@/utils/upload/upload.js";
import dayjs from "dayjs";
import weekday from "dayjs/plugin/weekday";
import { courseStore } from "@/store/modules/course.js";
import { deepClone, isEmpty, to, isNumber } from "@iceywu/utils";
const emites = defineEmits(["baseInfoPeriod"]);
const useCourseStore = courseStore();
const { courseInfo } = useCourseStore;
const router = useRouter();
const route = useRoute();
const ruleFormRef = ref();
const termPeriodNumber = ref(); //课期数
const periodName = ref(""); //课期命名
const prefixRule = ref(""); //前缀规则
const suffixRule = ref("");
const ruleForm = reactive({
  complexId: "", //实践点
  leaderIds: "", //领队
  maxPeopleNumber: "", //人数上限
  minPeopleNumber: "", //人数下限
  lecturerIds: "", //讲师
  cover: [], //课期图片
  name: "", //课期名
  openDate: "", //开课日期
  openTime: "", //开课时间
  signUpDeadline: "", //停止报名时间
  colseTime: "",
  coursePeriodTags: [],
  prefix: "无", //命名前缀
  suffix: "无" //命名后缀
  // teacherTags: [] //师资
});
const courseForm = reactive({
  courseName: "", //课程名
  courseTypeId: "", //课程类型
  courseTags: [],
  courseFiles: [], //课程图片
  age: "",
  introduction: "" //课程简介
});
// 课期前缀后缀选项
const periodPrefixOptions = ref([
  { value: 1, label: "无" },
  { value: 2, label: "课期数" },
  { value: 3, label: "开课日期" }
]);
const periodSuffixOptions = ref([
  { value: 1, label: "无" },
  { value: 2, label: "课期数" },
  { value: 3, label: "开课日期" }
]);
const validateMaxPeopleNumber = (rule, value, callback) => {
  let num = Number(value);
  if (num && num < 0) {
    ruleForm.maxPeopleNumber = 1;
    return callback(new Error("人数上限不能为负数"));
  }
  if (num && !Number.isInteger(num)) {
    ruleForm.maxPeopleNumber = 1;
    return callback(new Error("人数上限必须为整数"));
  }

  if (num && num > 99999) {
    ruleForm.maxPeopleNumber = 99999;
    return callback(new Error("人数上限不能超过五位数"));
  }
  if (num && num < Number(ruleForm.minPeopleNumber)) {
    return callback(new Error("人数上限不能低于人数下限"));
  }
  callback();
};
const validateMinPeopleNumber = (rule, value, callback) => {
  let num = Number(value);
  if (num && num < 0) {
    ruleForm.minPeopleNumber = "";
    return callback(new Error("人数下限不能为负数"));
  }
  if (num && !Number.isInteger(num)) {
    ruleForm.minPeopleNumber = "";
    return callback(new Error("人数下限必须为整数"));
  }

  if (num && num > 99999) {
    ruleForm.minPeopleNumber = 99999;
    return callback(new Error("人数下限不能超过五位数"));
  }
  callback();
};
// 课期规则
const rules = reactive({
  name: [{ required: true, message: "请输入课期名", trigger: "blur" }],
  maxPeopleNumber: [{ validator: validateMaxPeopleNumber, trigger: "blur" }],
  minPeopleNumber: [{ validator: validateMinPeopleNumber, trigger: "blur" }],
  complexId: [{ required: true, message: "请选择实践点", trigger: "blur" }],
  leaderIds: [{ required: true, message: "请选择领队", trigger: "blur" }],
  lecturerIds: [{ required: true, message: "请选择讲师", trigger: "blur" }],
  openDate: [{ required: true, message: "请选择开课日期", trigger: "blur" }],
  openTime: [{ required: true, message: "请选择开课时间", trigger: "blur" }],
  signUpDeadline: [
    { required: true, message: "请选择关闭报名日期", trigger: "blur" }
  ],
  colseTime: [
    { required: true, message: "请选择关闭报名时间", trigger: "blur" }
  ]
});
const rule = reactive({
  courseName: [{ required: true, message: "请输入课程名", trigger: "blur" }],
  courseTypeId: [{ required: true, message: "请选择课程类型", trigger: "blur" }]
});
const formData = ref([
  {
    label: "课程名",
    type: "input",
    prop: "courseName",
    check: true,
    placeholder: "请输入课程名",
    width: "400px"
  },
  {
    label: "课程类型",
    type: "input",
    prop: "courseTypeId",
    check: true,
    placeholder: "请选择课程类型",
    width: "400px",
    options: []
  },

  {
    label: "课程年龄段",
    type: "input",
    prop: "age",
    check: false,
    placeholder: "请输入人数上限",
    width: "400px"
  },
  {
    label: "课程简介",
    type: "input",
    prop: "introduction",
    check: true,
    placeholder: "一句话描述",
    width: "400px",
    maxLength: 100
  },
  {
    label: "课程亮点标签",
    type: "input",
    prop: "courseTags",
    check: false,
    placeholder: "请输入人数上限",
    width: "350px",
    tags: ""
  }
]);
const formPeriodData = ref([
  {
    label: "课期名",
    type: "input",
    typeInput: "text",
    prop: "name",
    check: true,
    placeholder: "请输入课期名",
    width: "56%",
    maxLength: 30
  },
  {
    label: "实践点",
    type: "select",
    prop: "complexId",
    check: true,
    placeholder: "请选择实践点",
    width: "50%",
    options: [],
    text: "新建实践点"
  },
  {
    label: "人数下限",
    type: "input",
    typeInput: "number",
    prop: "minPeopleNumber",
    check: false,
    placeholder: "请输入人数下限",
    width: "40%"
  },
  {
    label: "人数上限",
    type: "input",
    typeInput: "number",
    prop: "maxPeopleNumber",
    check: false,
    placeholder: "请输入人数上限",
    width: "40%"
  },
  // {
  //   label: "师资",
  //   type: "tagTab",
  //   prop: "teachers",
  //   check: false,
  //   placeholder: "请输入师资",
  //   width: "350px",
  //   tags: [],
  //   text: "添加师资"
  // },
  {
    label: "领队",
    type: "selectMultiple",
    prop: "leaderIds",
    check: true,
    placeholder: "请选择领队",
    width: "50%",
    options: [],
    text: "新建领队"
  },
  {
    label: "讲师",
    type: "selectMultiple",
    prop: "lecturerIds",
    check: true,
    placeholder: "请选择讲师",
    width: "50%",
    options: [],
    text: "新建讲师"
  },
  {
    label: "课期标签",
    type: "tag",
    prop: "coursePeriodTags",
    check: false,
    placeholder: "请输入人数上限",
    width: "350px",
    tags: []
  },
  {
    label: "开课日期",
    type: "pickerDate",
    prop: "openDate",
    timeType: "date",
    check: true,
    placeholder: "请选择日期",
    width: "400px",
    options: []
  },
  {
    label: "开课时间",
    type: "pickerTime",
    timeType: "date",
    prop: "openTime",
    check: true,
    placeholder: "请选择开课时间",
    width: "400px",
    options: []
  },
  {
    label: "关闭报名日期",
    type: "pickerDate",
    prop: "signUpDeadline",
    timeType: "date",
    check: true,
    placeholder: "请选择日期",
    width: "400px",
    options: []
  },

  {
    label: "关闭报名时间",
    type: "pickerTime",
    prop: "colseTime",
    timeType: "date",
    check: true,
    placeholder: "请选择时间",
    width: "400px",
    options: []
  }
]);
const imgText = ref(
  "支持上传png、jpg、jpeg图片格式，最多上传9张，最佳尺寸：750*750px，单张大小不超过10MB"
);
const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const submitLoading = ref(false);
const inputValue = ref("");
const inputCourseValue = ref("");
const inputCourseVisible = ref(false);
const inputVisible = ref(false);
const InputRef = ref();
const handleClose = tag => {
  formPeriodData.value[6].tags.splice(
    formPeriodData.value[6].tags.indexOf(tag),
    1
  );
};

const showInput = () => {
  inputVisible.value = true;
  nextTick(() => {
    InputRef.value.input.focus();
  });
};

const handleInputConfirm = () => {
  if (inputValue.value) {
    if (inputValue.value.length > 12) {
      ElMessage.error("输入的字数不能超过12个");
    }
    const item = formPeriodData.value[6].tags.find(
      it => it === inputValue.value
    );
    if (item) {
      ElMessage.error("标签名称不可重复，请重新输入");
    }
    if (!item && inputValue.value.length <= 12) {
      formPeriodData.value[6].tags.push(inputValue.value);
    }
  }
  inputVisible.value = false;
  inputValue.value = "";
};
const openCourseImg = ref(false); //控制是否打开使用课程图片开关
// 打开使用课程图片开关
const openImgChange = val => {
  if (val) {
    // 获取当前课程封面图片中不在课期封面中的部分
    const newCourseFilesToAdd = fileList.value.filter(
      courseFile =>
        !fileListCoursePeriod.value.some(
          periodFile => periodFile.uid === courseFile.uid
        )
    );

    // 添加新的课程封面图片到课期封面
    fileListCoursePeriod.value = [
      ...fileListCoursePeriod.value,
      ...newCourseFilesToAdd
    ];

    ruleForm.cover = [...ruleForm.cover, ...newCourseFilesToAdd];
  } else {
    // 移除所有与课程封面相关的图片
    fileListCoursePeriod.value = fileListCoursePeriod.value.filter(
      periodFile =>
        !fileList.value.some(
          courseFile =>
            courseFile.uid === periodFile.uid ||
            courseFile.uploadId === periodFile.uploadId
        )
    );

    ruleForm.cover = ruleForm.cover.filter(
      periodFile =>
        !fileList.value.some(
          courseFile =>
            courseFile.uid === periodFile.uid ||
            courseFile.uploadId === periodFile.uploadId
        )
    );
  }
};
const fileFilterFn = (periodFile, courseFile) => {
  periodFile = periodFile.map(it => {
    return {
      fileName: it.name || it.fileName
    };
  });
  const diffFile = courseFile.filter(
    file => !periodFile.some(existFile => existFile.fileName === file.fileName)
  );
  const sameFile = periodFile.filter(
    file => !courseFile.some(existFile => existFile.fileName === file.fileName)
  );
  return [diffFile, sameFile];
};
const fileFilterFn1 = (periodFile, courseFile) => {
  const diffFile = courseFile.filter(
    file => !periodFile.some(existFile => existFile.uid === file.uid)
  );
  const sameFile = periodFile.filter(
    file => !courseFile.some(existFile => existFile.uid === file.uid)
  );
  return [diffFile, sameFile];
};
const fileList = ref([]);
// 课期图片上传
const fileListCoursePeriod = ref([]);
const dialogPeroidImageUrl = ref("");
const dialogPeroidVisible = ref(false);
const handlePicturePeriodPreview = uploadFile => {
  dialogPeroidImageUrl.value = uploadFile.url;
  dialogPeroidVisible.value = true;
};
const beforeUploadCoursePeriod = async file => {
  let imgType = ["jpg", "jpeg", "png"];
  let fileType = file.type.split("/")[1];
  if (!imgType.includes(fileType)) {
    ElMessage.error("上传图片格式不支持，请上传png，jpg， jpeg格式的图片");
    return false;
  }
  if (ruleForm.cover?.length > 8) {
    ElMessage.error("上传图片数量过多，最多上传9张图片");
    return false;
  }
  try {
    if (file.type.startsWith("image/") === false) {
      ElMessage.error("上传类型不支持");
      fileListCoursePeriod.value = fileListCoursePeriod.value.filter(
        () => false
      );
    }
    const { data, code } = await uploadFile(file, progress => {
      // 构造用于 el-upload 展示的文件对象
      const currentFile = {
        name: file.name,
        uid: file.uid,
        status: progress.status || "uploading",
        percentage: progress.percent || 0
      };

      // 上传成功，补充 url 字段
      if (progress.status === "success" && progress.data?.url) {
        currentFile.url = progress.data.url;

        // 更新 ruleForm.cover，确保无 Proxy
        if (progress.data.fileIdentifier) {
          ruleForm.cover = ruleForm.cover.filter(item => item.uid !== file.uid);
          ruleForm.cover.push({
            fileIdentifier: progress.data.fileIdentifier,
            fileType: "PHOTO",
            uid: file.uid
          });
        }
      }

      // 失败时移除，成功/上传中则更新
      fileListCoursePeriod.value = fileListCoursePeriod.value.filter(
        f => f.uid !== file.uid
      );
      if (progress.status === "fail") {
        ElMessage.error(progress.errMessage || "上传失败，请重试");
      } else {
        // 用浅拷贝，避免 Proxy
        fileListCoursePeriod.value = [
          ...fileListCoursePeriod.value,
          { ...currentFile }
        ];
      }
    });
    // if (code === 200) {
    //   ruleForm.cover.push({
    //     fileIdentifier: data.fileIdentifier,
    //     fileType: "PHOTO",
    //     uid: file.uid,
    //     fileName: data.fileName
    //   });
    //   // ElLoading.service().close();
    // }
  } catch (error) {
    console.error("上传出错:", error);
    return false;
  }
};
//删除图片视频
const handleCoursePeriodRemove = (uploadFile, uploadFiles) => {
  ruleForm.cover = ruleForm.cover.filter(it => it.uid !== uploadFile.uid);
};
const submitBackLoading = ref(false);
const submitBackForm = async (formEl, val) => {
  if (!formEl) return;
  if (submitBackLoading.value) return;
  submitBackLoading.value = true;
  await submitFormApi(formEl, val);
  submitBackLoading.value = false;
};
const submitForm = async (formEl, val) => {
  if (!formEl) return;
  if (submitLoading.value) return;
  submitLoading.value = true;
  await submitFormApi(formEl, val);
  submitLoading.value = false;
};
const submitFormApi = async (formEl, bool) => {
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      const times =
        dayjs(ruleForm.openDate).format("YYYY-MM-DD") +
        "" +
        dayjs(ruleForm.openTime).format("HH:mm:ss");
      ruleForm.openTime = dayjs(times).valueOf();
      if (dayjs(ruleForm.openTime) < dayjs().valueOf()) {
        ElMessage.error("开课时间不能早于当前时间");
        submitLoading.value = false;
        return;
      }
      const times1 =
        dayjs(ruleForm.signUpDeadline).format("YYYY-MM-DD") +
        "" +
        dayjs(ruleForm.colseTime).format("HH:mm:ss");
      ruleForm.signUpDeadline = dayjs(times1).valueOf();

      ruleForm.lecturerIds = ruleForm.lecturerIds.map(it => {
        if (it.value && it.value !== it) {
          return it.value;
        } else {
          return it;
        }
      });
      ruleForm.leaderIds = ruleForm.leaderIds.map(it => {
        if (it.value && it.value !== it) {
          return it.value;
        } else {
          return it;
        }
      });
      const params = { ...ruleForm };
      params.id = Number(route.query.periodId);
      delete params.colseTime;
      delete params.openDate;
      if (!params?.cover?.length) {
        delete params?.cover;
      } else {
        params.cover = ruleForm.cover.map((it, index) => {
          return {
            sortOrder: index + 1,
            fileIdentifier: it.fileIdentifier,
            fileType: it.fileType
          };
        });
      }
      if (formPeriodData.value[6].tags && formPeriodData.value[6].tags.length) {
        params.coursePeriodTags = formPeriodData.value[6].tags;
      } else {
        delete params.coursePeriodTags;
      }
      params.isCourseCover = openCourseImg.value;
      if (ruleForm.maxPeopleNumber) {
        params.maxPeopleNumber = ruleForm.maxPeopleNumber;
      } else {
        delete params.maxPeopleNumber;
      }
      if (ruleForm.minPeopleNumber) {
        params.minPeopleNumber = +ruleForm.minPeopleNumber;
      } else {
        delete params.minPeopleNumber;
      }
      if (Number(ruleForm.minPeopleNumber) > Number(ruleForm.maxPeopleNumber)) {
        ElMessage.error("人数上限不能低于人数下限");
        return;
      }
      if (!isEmpty(ruleForm.complexId)) {
        if (
          typeof ruleForm.complexId === "number" &&
          !isNaN(ruleForm.complexId)
        ) {
          params.complexId = ruleForm.complexId;
        } else {
          params.complexId = complexObj.value?.id;
        }
      } else {
        delete params.complexId;
      }
      if (ruleForm.prefix) {
        if (ruleForm.prefix === "课期数") {
          params.prefixRule = "TERM_NUMBER";
          delete params?.prefix;
        } else if (ruleForm.prefix === "无") {
          delete params?.prefix;
        } else if (ruleForm.prefix === "开课日期") {
          params.prefixRule = "OPEN_TIME";
          delete params?.prefix;
        } else {
          params.prefixRule = "OTHER";
        }
      }
      if (ruleForm.suffix) {
        if (ruleForm.suffix === "课期数") {
          params.suffixRule = "TERM_NUMBER";
          delete params?.suffix;
        } else if (ruleForm.suffix === "无") {
          delete params?.suffix;
        } else if (ruleForm.suffix === "开课日期") {
          params.suffixRule = "OPEN_TIME";
          delete params?.suffix;
        } else {
          params.suffixRule = "OTHER";
        }
      }
      // console.log("🍭params--------55---------------------->", params);
      // return
      const operateLog = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `编辑了"${ruleForm.name}"课期中的基础信息`
      };
      let [err, res] = await to(coursePeriodEdite(params, operateLog));
      if (res.code === 200) {
        ElMessage.success("编辑成功");
        useCourseStore.saveLeaderInfo([]);
        useCourseStore.saveLecturerInfo([]);
        useCourseStore.saveBaseInfo({});
        clearPeriodEditSpecificStoreData();
        if (bool) {
          if (route.query.fromPage === "courseDetail") {
            router.replace({
              path: "/course/courseDetails",
              query: { id: route.query.courseId }
            });
          } else if (route.query.fromPage === "currentDetail") {
            router.replace({
              path: "/course/courseDetails/currentDetails",
              query: {
                periodId: route.query.periodId,
                courseId: route.query.courseId
              }
            });
          }
        }
      } else {
        let text = route.query.type === "create" ? "创建失败" : "编辑失败";
        ElMessage.error(`${text},${res?.msg}`);
      }
    } else {
      console.log("error submit!", fields);
    }
  });
};
const courseTypeFindApi = async () => {
  let [err, res] = await requestTo(findAllCourseType());
  if (res) {
    formData.value[1].options = transformArray(res);
  }
};
function transformArray(inputArray) {
  return inputArray.map(item => {
    const newItem = {
      value: item.id,
      label: item.name
    };
    // 如果有 children，则递归转换
    if (item.children && item.children.length > 0) {
      newItem.children = transformArray(item.children);
    }
    return newItem;
  });
}
// 领队讲师查询 type为2 讲师 type为3 领队
const leaderFindApi = async type => {
  const params = {
    roleId: type
  };
  let [err, res] = await requestTo(leaderLecturerFind(params));
  if (res) {
    if (type == 2) {
      formPeriodData.value[5].options = res.map(it => {
        return {
          label: it.name,
          value: it.id
        };
      });
    } else {
      formPeriodData.value[4].options = res.map(it => {
        return {
          label: it.name,
          value: it.id
        };
      });
    }
  } else {
    console.log("🌵-----err-----", err);
  }
};
const courseTypeChange = val => {
  ruleForm.type = val[val.length - 1];
};
const srcList = ref([]);
// 获取课程信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    id: Number(route.query.courseId) || 0
  };
  const [err, result] = await requestTo(courseFindId(paramsData));
  if (result) {
    // console.log("🌳result-----course---3333---------------------->", result);
    if (result.tags) {
      formData.value[4].tags = result.tags?.join("、");
      courseForm.courseTags = result.tags?.join("、");
    } else {
      formData.value[4].tags = "--";
      courseForm.courseTags = "--";
    }
    courseForm.maxAge = result.maxAge || "--";
    if (result.minAge && result.maxAge) {
      courseForm.age = `${result.minAge}到${result.maxAge}岁`;
    } else if (result.minAge && !result.maxAge) {
      courseForm.age = `${result.minAge}岁`;
    } else if (!result.minAge && result.maxAge) {
      courseForm.age = `${result.maxAge}岁`;
    } else {
      courseForm.age = `无年龄限制`;
    }
    courseForm.courseName = result.name || "--";
    courseForm.introduction = result.introduction || "--";
    courseForm.courseTypeId = result.courseType?.name || "--";
    if (result?.files?.length) {
      result?.files.map(item => {
        courseForm.courseFiles.push(item.uploadFile);
        fileList.value.push(item.uploadFile);
        srcList.value.push(item?.uploadFile?.url);
      });
    }
  } else {
    ElMessage.error(err);
  }
  getListLoading.value = false;
};
// 获取课期信息
const getLoading = ref(false);
const complexObj = ref({}); //课期查询的实践点对象(解决回显的实践点已被删除问题)
const leaderList = ref([]);
const lecturerList = ref([]);
const getPeriodList = async data => {
  if (getLoading.value) {
    return;
  }
  getLoading.value = true;
  let paramsData = {
    id: Number(route.query.periodId) || 0
  };
  const [err, result] = await requestTo(coursePeriodFind(paramsData));
  if (result) {
    // console.log("🌳result-----period------------------------->", result);
    openCourseImg.value = !!result.isCourseCover;
    if (result.tags) {
      ruleForm.coursePeriodTags = result.tags;
      formPeriodData.value[6].tags = result.tags;
    }
    ruleForm.name = result.originalName;
    emites("baseInfoPeriod", result.name);
    ruleForm.complexId = result.complex?.name;
    complexObj.value = result.complex;
    ruleForm.maxPeopleNumber = result.maxPeopleNumber;
    ruleForm.minPeopleNumber = result.minPeopleNumber;
    ruleForm.leaderIds = result.leaders?.map(it => it.id);
    ruleForm.lecturerIds = result.lecturers?.map(it => it.id);
    leaderList.value = result.leaders;
    lecturerList.value = result.lecturers;
    ruleForm.openDate = result?.openTime;
    ruleForm.openTime = result?.openTime;
    ruleForm.signUpDeadline = result?.signUpDeadline;
    ruleForm.colseTime = result?.signUpDeadline;
    if (result?.cover?.length) {
      result?.cover.map(item => {
        ruleForm.cover.push(item.uploadFile);
        fileListCoursePeriod.value.push(item.uploadFile);
      });
    }
    if (result.prefix) {
      ruleForm.prefix = result.prefix;
    } else {
      ruleForm.prefix = suffFn(result.prefixRule);
      prefixRule.value = result.prefixRule;
    }
    if (result.suffix) {
      ruleForm.suffix = result.suffix;
    } else {
      ruleForm.suffix = suffFn(result.suffixRule);
      suffixRule.value = result.suffixRule;
    }
    if (result.prefix && result.suffix && result.name) {
      periodName.value = `${result.prefix}-${result.name}-${result.suffix}`;
    }
  } else {
    ElMessage.error(err);
  }
  getLoading.value = false;
};
// 回显前后缀
const suffFn = val => {
  if (val === "TERM_NUMBER") {
    return "课期数";
  } else if (val === "OPEN_TIME") {
    return "开课日期";
  } else {
    return "无";
  }
};
// 课期命名示意
watch(
  () => [ruleForm.prefix, ruleForm.suffix, ruleForm.openDate, ruleForm.name],
  ([newPrefix, newSuffix, newData, newName]) => {
    let prefixLabel =
      periodPrefixOptions.value.find(i => i.label === newPrefix)?.label || "";
    let suffixLabel =
      periodSuffixOptions.value.find(i => i.label === newSuffix)?.label || "";
    let prefixText = "";
    let suffixText = "";
    if (prefixLabel === "课期数") {
      if (termPeriodNumber.value) {
        prefixText = `第${termPeriodNumber.value + 1}期`;
      } else {
        prefixText = `第1期`;
      }
    } else if (prefixLabel === "无") {
      prefixText = ``;
    } else if (prefixLabel === "开课日期") {
      if (newData) {
        let openDate = dayjs(newData).format("MMDD");
        prefixText = `${openDate}`;
      } else {
        prefixText = ``;
      }
    } else {
      prefixText = `${prefixLabel}`;
    }
    if (suffixLabel === "课期数") {
      if (termPeriodNumber.value) {
        suffixText = `第${termPeriodNumber.value + 1}期`;
      } else {
        suffixText = `第1期`;
      }
    } else if (suffixLabel === "无") {
      suffixText = ``;
    } else if (suffixLabel === "开课日期") {
      if (newData) {
        let openDate = dayjs(newData).format("MMDD");
        suffixText = `${openDate}`;
      } else {
        suffixText = ``;
      }
    } else {
      suffixText = `${suffixLabel}`;
    }
    // 拼接新的课期名
    if (prefixText && ruleForm.name && suffixText) {
      periodName.value = `${prefixText}-${ruleForm.name}-${suffixText}`;
    } else if (prefixText && ruleForm.name) {
      periodName.value = `${prefixText}-${ruleForm.name}`;
    } else if (ruleForm.name && suffixText) {
      periodName.value = `${ruleForm.name}-${suffixText}`;
    } else if (ruleForm.name) {
      periodName.value = `${ruleForm.name}`;
    } else {
      periodName.value = "";
    }
  },
  { immediate: true, deep: true }
);
// 保存当前表单数据到store
const saveCurrentFormToStore = () => {
  // 保存课期基本信息
  useCourseStore.savePeriodName(ruleForm.name);
  useCourseStore.savePeriodCourseTypeId(ruleForm.courseTypeId);
  if (ruleForm.complexId) {
    if (isNumber(ruleForm.complexId)) {
      useCourseStore.saveBaseInfo({ id: ruleForm.complexId });
    } else {
      useCourseStore.saveBaseInfo({ id: complexObj.value.id });
    }
  }
  if (ruleForm.leaderIds && ruleForm.leaderIds.length) {
    let ids = ruleForm.leaderIds.map(it => {
      if (typeof it === "object" && it !== null) {
        return { id: it.value || it.id };
      }
      return { id: it };
    });
    useCourseStore.saveLeaderInfo(ids);
  } else {
    useCourseStore.saveLeaderInfo([]);
  }
  if (ruleForm.lecturerIds && ruleForm.lecturerIds.length) {
    let ids = ruleForm.lecturerIds.map(it => {
      if (typeof it === "object" && it !== null) {
        return { id: it.value || it.id };
      }
      return { id: it };
    });
    useCourseStore.saveLecturerInfo(ids);
  } else {
    useCourseStore.saveLecturerInfo([]); // 如果为空则清空store中的信息
  }
  // 保存人数下限和上限
  useCourseStore.savePeriodMaxPeopleNumber(ruleForm.maxPeopleNumber);
  useCourseStore.savePeriodMinPeopleNumber(ruleForm.minPeopleNumber);

  // 保存开课日期、开课时间、停止报名日期、停止报名时间
  useCourseStore.savePeriodOpenDate(ruleForm.openDate);
  useCourseStore.savePeriodOpenTimeValue(ruleForm.openTime);
  useCourseStore.savePeriodSignUpDeadlineDate(ruleForm.signUpDeadline);
  useCourseStore.savePeriodColseTimeValue(ruleForm.colseTime);
  useCourseStore.savePeriodTags(ruleForm.coursePeriodTags);
  // 保存封面图信息
  useCourseStore.saveCoverInfo(ruleForm.filePeriodList);
  // 保存前后缀信息（
  useCourseStore.savePeriodEditePreInfo({
    prefix: ruleForm.prefix,
    suffix: ruleForm.suffix,
    prefixRule: prefixRule.value,
    suffixRule: suffixRule.value
  });
  useCourseStore.saveCoverInfo(ruleForm);
  //  保存课程基本信息
  useCourseStore.saveCourseBaseDraft({ ...courseForm });
};
// 点击新建
const clickEvt = item => {
  const currentPath = router.currentRoute.value.path;
  const isFromCopy = currentPath.includes("periodCopy");

  // 无论点击哪个类型的新建按钮，都保存当前表单数据
  saveCurrentFormToStore();

  if (item.text === "新建实践点") {
    router.push({
      path: "/coursePeriodEdite/baseAdd",
      query: {
        type: route.query.type,
        periodId: route.query.periodId,
        courseId: route.query.courseId,
        fromPage: route.query.fromPage,
        copyId: route.query.copyId,
        id: route.query.id,
        name: route.query.name,
        termNumber: route.query.termNumber,
        create: "create"
      }
    });
  } else if (item.text === "新建领队") {
    router.push({
      path: "/coursePeriodEdite/leaderCreate",
      query: {
        type: "new",
        text: "leader",
        roleId: 3,
        type: route.query.type,
        periodId: route.query.periodId,
        courseId: route.query.courseId,
        fromPage: route.query.fromPage,
        copyId: route.query.copyId,
        id: route.query.id,
        name: route.query.name,
        termNumber: route.query.termNumber,
        create: "create"
      }
    });
  } else if (item.text === "新建讲师") {
    router.push({
      path: "/coursePeriodEdite/lecturerCreate",
      query: {
        type: "new",
        text: "teacher",
        roleId: 2,
        type: route.query.type,
        periodId: route.query.periodId,
        courseId: route.query.courseId,
        fromPage: route.query.fromPage,
        copyId: route.query.copyId,
        id: route.query.id,
        name: route.query.name,
        termNumber: route.query.termNumber,
        create: "create"
      }
    });
  } else if (item.text === "添加师资") {
    router.push({
      path: "/coursePeriodEdite/teachersCreate",
      query: {
        type: route.query.type,
        periodId: route.query.periodId,
        courseId: route.query.courseId,
        fromPage: route.query.fromPage,
        copyId: route.query.copyId,
        id: route.query.id,
        name: route.query.name,
        termNumber: route.query.termNumber,
        create: "create"
      }
    });
  }
};
// 返回
const goBack = () => {
  useCourseStore.saveLeaderInfo([]);
  useCourseStore.saveLecturerInfo([]);
  useCourseStore.saveBaseInfo({});
  if (route.query.fromPage === "courseDetail") {
    router.replace({
      path: "/course/courseDetails",
      query: { id: route.query.courseId }
    });
  } else if (route.query.fromPage === "currentDetail") {
    router.replace({
      path: "/course/courseDetails/currentDetails",
      query: { periodId: route.query.periodId, courseId: route.query.courseId }
    });
  }
};
// 从store中获取并更新表单数据
const updateFormFromStore = () => {
  if (route.query.create === "create") {
    // 课期名
    if (useCourseStore.periodName) {
      ruleForm.name = useCourseStore.periodName;
    }
    // 课程类型ID
    if (useCourseStore.periodCourseTypeId) {
      ruleForm.courseTypeId = useCourseStore.periodCourseTypeId;
    }
    if (useCourseStore.baseInfo && !isEmpty(useCourseStore.baseInfo)) {
      ruleForm.complexId = useCourseStore.baseInfo?.id;
    }
    // 人数上限
    if (!isEmpty(useCourseStore.periodMaxPeopleNumber)) {
      ruleForm.maxPeopleNumber = useCourseStore.periodMaxPeopleNumber;
    }
    if (!isEmpty(useCourseStore.periodMinPeopleNumber)) {
      ruleForm.minPeopleNumber = useCourseStore.periodMinPeopleNumber;
    }
    // 领队
    if (useCourseStore.leaderInfo && useCourseStore.leaderInfo.length) {
      ruleForm.leaderIds = useCourseStore.leaderInfo?.map(it => it.id);
    }
    // 讲师
    if (useCourseStore.lecturerInfo && useCourseStore.lecturerInfo.length) {
      ruleForm.lecturerIds = useCourseStore.lecturerInfo?.map(it => it.id);
    }
    // 开课日期
    if (useCourseStore.periodOpenDate) {
      ruleForm.openDate = useCourseStore.periodOpenDate;
    }
    // 开课时间
    if (useCourseStore.periodOpenTimeValue) {
      ruleForm.openTime = useCourseStore.periodOpenTimeValue;
    }
    // 停止报名日期
    if (useCourseStore.periodSignUpDeadlineDate) {
      ruleForm.signUpDeadline = useCourseStore.periodSignUpDeadlineDate;
    }
    // 停止报名时间
    if (useCourseStore.periodColseTimeValue) {
      ruleForm.colseTime = useCourseStore.periodColseTimeValue;
    }
    formPeriodData.value[6].tags = useCourseStore.periodTags;
    ruleForm.coursePeriodTags = useCourseStore.periodTags;
    // 前后缀信息
    if (useCourseStore.periodEditePreInfo) {
      if (useCourseStore.periodEditePreInfo.prefix) {
        ruleForm.prefix = useCourseStore.periodEditePreInfo.prefix;
      } else {
        ruleForm.prefix = suffFn(useCourseStore.periodEditePreInfo.prefixRule);
      }
      if (useCourseStore.periodEditePreInfo.suffix) {
        ruleForm.suffix = useCourseStore.periodEditePreInfo.suffix;
      } else {
        ruleForm.suffix = suffFn(useCourseStore.periodEditePreInfo.prefixRule);
      }
    }
    // 封面图
    if (useCourseStore.coverInfo && useCourseStore.coverInfo.length > 0) {
      ruleForm.cover = useCourseStore.coverInfo;
      fileListCoursePeriod.value = useCourseStore.coverInfo;
    } else {
      ruleForm.cover = [];
      fileListCoursePeriod.value = [];
    }
    courseForm.courseName = useCourseStore.courseBaseDraft.courseName;
    courseForm.introduction = useCourseStore.courseBaseDraft.introduction;
    courseForm.courseTypeId = useCourseStore.courseBaseDraft.courseTypeId;
    courseForm.courseTags = useCourseStore.courseBaseDraft.courseTags;
    formData.value[4].tags = useCourseStore.courseBaseDraft.courseTags;
    courseForm.courseFiles = useCourseStore.courseBaseDraft.courseFiles;
    courseForm.age = useCourseStore.courseBaseDraft.age;
    useCourseStore.periodEditeInfo;
  }
};
// 清除为编辑页暂存的特定数据
const clearPeriodEditSpecificStoreData = () => {
  useCourseStore.savePeriodName("");
  useCourseStore.savePeriodCourseTypeId("");
  useCourseStore.savePeriodMinPeopleNumber("");
  useCourseStore.savePeriodMaxPeopleNumber("");
  useCourseStore.savePeriodMinPeopleNumber("");
  useCourseStore.savePeriodOpenDate(null);
  useCourseStore.savePeriodOpenTimeValue(null);
  useCourseStore.savePeriodSignUpDeadlineDate(null);
  useCourseStore.savePeriodColseTimeValue(null);
  useCourseStore.savePeriodEditePreInfo({});
  // baseInfo, leaderInfo, lecturerInfo 会在afterEach或cancelEvt中被统一处理，这里不再重复

  // 清除封面图信息
  useCourseStore.saveCoverInfo([]);
};
//  实践点查询不分页
const complexIdApi = async () => {
  let [err, res] = await requestTo(complexId());
  if (res) {
    formPeriodData.value[1].options = res.map(it => {
      return {
        ...it,
        label: it.name,
        value: it.id
      };
    });
  }
};
const mergelabel = (label, value) => {
  if (typeof label === "number") {
    if (value === "leaderIds") {
      const item = leaderList.value.find(it => it.id === label);
      if (item) {
        return item.name;
      } else {
        const itemOption = formPeriodData.value[4]?.options?.find(
          it => it.id === label
        );
        return itemOption?.name;
      }
    } else {
      const item = lecturerList.value.find(it => it.id === label);
      if (item) {
        return item.name;
      } else {
        const itemOption = formPeriodData.value[5]?.options?.find(
          it => it.id === label
        );
        return itemOption?.name;
      }
    }
  } else {
    return label;
  }
};
onMounted(async () => {
  courseTypeFindApi();
  leaderFindApi(3);
  leaderFindApi(2);
  complexIdApi();
  if (route.query.create !== "create") {
    getTableList();
    await getPeriodList();
  }
  updateFormFromStore();
});
</script>

<template>
  <div class="base-info">
    <div class="base-container">
      <el-divider content-position="left">课程基础信息</el-divider>
      <div class="course-info">
        <div class="course-des">
          <el-form
            ref="ruleFormRef"
            :model="courseForm"
            :rules="rule"
            label-width="auto"
            label-position="right"
            class="course-form"
          >
            <!-- 课程信息 -->

            <el-form-item
              v-for="(item, index) in formData"
              :key="index"
              :prop="item.prop"
              :label="item.label"
              :inline-message="false"
              :show-message="true"
              error-placement="right"
            >
              <!-- input输入 -->
              <template v-if="item.type === 'input'">
                <el-input
                  v-model.trim="courseForm[item.prop]"
                  :style="{ width: item.width }"
                  readonly
                />
              </template>
            </el-form-item>
          </el-form>
        </div>
        <div class="course-img">
          <div class="upload" style="margin-bottom: 10px">封面图</div>
          <div class="img">
            <el-image
              :src="srcList[0]"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="srcList"
              :hide-on-click-modal="true"
              show-progress
              :initial-index="0"
              fit="cover"
              class="img-pic"
            />
          </div>
        </div>
      </div>
      <el-divider content-position="left">课期基础信息</el-divider>
      <div class="course-period-info">
        <div class="course-des">
          <el-form
            ref="ruleFormRef"
            :model="ruleForm"
            :rules="rules"
            label-width="auto"
            label-position="right"
            style="margin-left: 30px"
            class="period-form"
          >
            <el-form-item
              v-for="(item, index) in formPeriodData"
              :key="index"
              :prop="item.prop"
              :inline-message="false"
              :label="item.label"
              :show-message="true"
              error-placement="right"
            >
              <!-- input输入 -->
              <template v-if="item.type === 'input'">
                <el-input
                  v-model="ruleForm[item.prop]"
                  :placeholder="item.placeholder"
                  :style="{ width: item.width }"
                  :type="item.typeInput"
                  :min="item.min"
                  :maxlength="item.maxLength"
                  :show-word-limit="item.maxLength"
                />
              </template>
              <!-- 多选 -->
              <template v-if="item.type === 'selectMultiple'">
                <el-select
                  v-model="ruleForm[item.prop]"
                  multiple
                  :placeholder="item.placeholder"
                  style="width: 240px"
                >
                  <template #label="{ label }">
                    <span>{{ mergelabel(label, item.prop) }} </span>
                    <!-- <span style="font-weight: bold">{{ value }}</span> -->
                  </template>
                  <el-option
                    v-for="it in item.options"
                    :key="it.value"
                    :label="it.label"
                    :value="it.value"
                  />
                </el-select>
                <el-button
                  type="primary"
                  style="margin-left: 10px"
                  @click="clickEvt(item)"
                >
                  {{ item.text }}
                </el-button>
              </template>
              <!-- 时间日期选择 -->
              <template v-if="item.type === 'pickerDate'">
                <el-date-picker
                  v-model="ruleForm[item.prop]"
                  :type="item.timeType"
                  :placeholder="item.placeholder"
                  style="width: 240px"
                  value-format="x"
                />
              </template>
              <template v-if="item.type === 'pickerTime'">
                <el-time-picker
                  v-model="ruleForm[item.prop]"
                  style="width: 240px"
                  :placeholder="item.placeholder"
                  value-format="x"
                />
              </template>
              <!-- 单选 -->
              <template v-if="item.type === 'select'">
                <el-select
                  v-model="ruleForm[item.prop]"
                  :placeholder="item.placeholder"
                  style="width: 240px"
                >
                  <el-option
                    v-for="it in item.options"
                    :key="it.value"
                    :label="it.label"
                    :value="it.value"
                  />
                </el-select>
                <el-button
                  type="primary"
                  style="margin-left: 10px"
                  @click="clickEvt(item)"
                >
                  {{ item.text }}
                </el-button>
              </template>
              <!-- 级联选择 -->
              <template v-if="item.type === 'cascader'">
                <el-cascader
                  v-model="ruleForm[item.prop]"
                  :options="item.options"
                  :show-all-levels="false"
                  style="width: 240px"
                  @change="courseTypeChange"
                />
              </template>
              <!-- 动态添加tag -->
              <template v-if="item.type === 'tag'">
                <div class="tag-container">
                  <div class="tags">
                    <el-tag
                      v-for="tag in item.tags"
                      :key="tag"
                      style="margin: 0 10px 4px 0"
                      closable
                      :disable-transitions="false"
                      @close="handleClose(tag)"
                    >
                      {{ tag }}
                    </el-tag>
                    <el-input
                      v-if="inputVisible"
                      ref="InputRef"
                      v-model.trim="inputValue"
                      class="w-20"
                      size="small"
                      @keyup.enter="handleInputConfirm"
                      @blur="handleInputConfirm"
                    />
                    <el-button
                      v-else
                      class="button-new-tag"
                      size="small"
                      @click="showInput"
                    >
                      +
                    </el-button>
                  </div>
                </div>
                <div class="descript-period">
                  课期标签是用于标识课期特点和属性的标签，旨在帮助家长和学生更快速地了解课期的相关信息。
                </div>
              </template>
              <template v-if="item.type === 'tagTab'">
                <el-tag v-for="it in tags" :key="it" closable type="primary">
                  {{ it.name }}
                </el-tag>
                <el-button
                  class="button-new-tag"
                  type="primary"
                  @click="clickEvt(item)"
                >
                  {{ item.text }}
                </el-button>
              </template>
            </el-form-item>
          </el-form>
        </div>
        <div class="course-img">
          <div class="upload">
            <div class="title">封面图</div>
            <div class="switch" style="margin-right: 50px">
              <el-switch v-model="openCourseImg" @change="openImgChange" /><span
                style="margin-left: 10px"
                >使用课程封面图</span>
            </div>
          </div>
          <el-upload
            v-model:file-list="fileListCoursePeriod"
            action="#"
            :http-request="() => {}"
            list-type="picture-card"
            accept="image/*"
            :before-upload="beforeUploadCoursePeriod"
            :on-remove="handleCoursePeriodRemove"
            :on-preview="handlePicturePeriodPreview"
            :class="{ hideUploadBtn: fileListCoursePeriod.length >= 9 }"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
          <el-dialog v-model="dialogPeroidVisible">
            <img
              class="w-full"
              :src="dialogPeroidImageUrl"
              alt="Preview Image"
            >
          </el-dialog>
          <div class="img-text">{{ imgText }}</div>
        </div>
      </div>
      <el-divider content-position="left">课期命名设置</el-divider>
      <div class="period-name-setting">
        <div class="select-period">
          <div class="name">前缀</div>
          <el-select
            v-model="ruleForm.prefix"
            placeholder="请选择"
            style="width: 200px"
          >
            <el-option
              v-for="item in periodPrefixOptions"
              :key="item.value"
              :label="item.label"
              :value="item.label"
            />
          </el-select>
        </div>
        <div class="select-period">
          <div class="name">后缀</div>
          <el-select
            v-model="ruleForm.suffix"
            placeholder="请选择"
            style="width: 200px"
          >
            <el-option
              v-for="item in periodSuffixOptions"
              :key="item.value"
              :label="item.label"
              :value="item.label"
            />
          </el-select>
        </div>
        <div class="select-period">
          <div class="name">课期名示意：</div>
          <div class="text">{{ periodName }}</div>
        </div>
      </div>
    </div>
    <div class="buttons">
      <el-button @click="goBack">返回</el-button>
      <el-button
        type="primary"
        class="create"
        :loading="submitBackLoading"
        @click="submitBackForm(ruleFormRef, true)"
      >
        {{ "保存并返回" }}
      </el-button>
      <el-button
        type="primary"
        class="create"
        :loading="submitLoading"
        @click="submitForm(ruleFormRef, false)"
      >
        {{ route.query.type === "create" ? "下一步" : "保存" }}
      </el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.base-info {
  width: 100%;
  height: 100%;
  position: relative;
  .base-container {
    width: 100%;
    height: calc(100% - 50px);
    overflow-y: auto;
  }
  .course-info {
    display: flex;
    width: 100%;
    :deep(.el-input__wrapper) {
      box-shadow: none;
    }
    .course-des {
      width: 50%;
      margin-right: 20px;
    }

    .course-form {
      margin-left: 42px;
    }
  }
  .course-period-info {
    display: flex;
    width: 100%;
    .course-des {
      width: 50%;
      margin-right: 20px;
    }
  }
  .tag-container {
    display: flex;
    flex-direction: column;
  }
  .descript-period {
    // margin: 0 0 0 -70px;
    line-height: 25px;
    font-size: 12px;
    color: #8c939d;
  }
  :deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
    width: 240px;
    background: #e1f5ff;
  }
  .star {
    margin-right: 3px;
    color: red;
  }
  .buttons {
    display: flex;
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
  }
  .range-input {
    display: flex;
    align-items: center;
  }
  .separator {
    margin: 0 10px;
    color: #606266;
  }
  .course-img {
    width: 50%;
  }
  .upload {
    width: 100%;
    display: flex;
    justify-content: space-between;
    .switch {
      display: flex;
      align-items: center;
    }
  }
  .img {
    height: 68px;
    width: 110px;
    flex-shrink: 0;
    transition:
      opacity 0.3s ease,
      width 0.3s ease,
      margin-left 0.3s ease;
    border-radius: 10px;
    border: 1px solid #dcdfe6;
    .img-pic {
      width: 100%;
      height: 100%;
      border-radius: 5px;
      object-fit: cover;
    }
  }
}
:deep(.el-form-item__label) {
  color: #303133;
  font-weight: normal;
}
:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 110px;
  height: 68px;
}
:deep(.el-upload--picture-card) {
  height: 68px;
  width: 110px;
}
.chooseDates {
  padding: 30px;
  box-sizing: border-box;
  background-color: #fff;
  height: 90%;
}
.demo-container {
  max-width: 100%;
  background-color: #ccc;
  box-shadow: 0px 4px 13px 0px #ccc;
  margin-bottom: 20px;
}

.cell-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s;
}

/* .cell-content:hover {
  background-color: #f5f7fa;
} */

/* 修改选中样式为勾选标志 */
.selected-date {
  color: #409eff;
  font-weight: bold;
}

.checkmark {
  position: absolute;
  right: 2px;
  bottom: 2px;
  font-size: 12px;
  color: #67c23a;
  font-weight: bold;
}

.selected-dates {
  margin-top: 20px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  white-space: pre-wrap; /* 允许空格换行 */
  font-weight: bold;
  min-height: 44px;
}
:deep(.el-calendar-table .el-calendar-day) {
  height: 50px;
}
:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  width: 100px;
  background: #e1f5ff;
}

.btnse {
  color: #409eff;
  cursor: pointer;
  min-width: fit-content;
  font-weight: bold;
  margin-right: 15px;
}
.c_red {
  color: #f56c6c;
}
//滚动条的宽度
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: #e4e4e4;
  border-radius: 3px;
}
//滚动条的滑块
::-webkit-scrollbar-thumb {
  background-color: #eee;
  border-radius: 3px;
}
.date_picker {
  width: 200px;
}
.m-b {
  margin-bottom: 40px;
}
.hour {
  min-width: fit-content;
  margin-left: 10px;
}
// :deep(.el-upload-list--picture-card .el-upload-list__item-thumbnail){
//     height: 62px;;
// }
.btns {
  margin-top: 20px;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  // justify-content: space-between;
}
.img-text {
  font-size: 12px;
  color: #8c939d;
}
.period-form {
  :deep(.el-form-item__label) {
    margin-right: 10px;
  }
}
:deep(.hideUploadBtn .el-upload--picture-card) {
  display: none;
}
.period-name-setting {
  width: 60%;
  display: flex;
  justify-content: space-between;
  white-space: nowrap;
  .select-period {
    display: flex;
    align-items: center;
    .name {
      margin-right: 10px;
    }
  }
}
</style>
