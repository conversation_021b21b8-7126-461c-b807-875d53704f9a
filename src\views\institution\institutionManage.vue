<script setup>
import { ref, onMounted, reactive, onBeforeMount, onBeforeUnmount } from "vue";
import { usePopup } from "vue-hooks-pure";
import Popup from "./components/popup.vue";
import { resetPassword, organizationFindById } from "@/api/institution";
import { useRouter, useRoute } from "vue-router";
import RichEditor from "@/components/Base/RichEditor.vue";
import { Hide, View } from "@element-plus/icons-vue";
import { decrypt, encryption } from "@/utils/SM4.js";
import { formatTime } from "@/utils/index";
import FileItem from "@/components/PreviewV2/FileItem.vue";
import PreviewFileDialog from "@/components/PreviewV2/PreviewFileDialog.vue";

const router = useRouter();
const route = useRoute();
const richFlag = ref(false);
onMounted(() => {
  getTableList();
  // richFlag.value = true;
});
const dialogFormVisible = ref(false);
const reset = () => {
  dialogFormVisible.value = false;
};
const form = ref({
  time: "2025-004-21 16:12:34",
  institutionID: "***************",
  name: "",
  alias: "",
  fileDTOs: [],
  organizationCode: "",
  customerServiceHotline: "",
  introduction: "",
  organizationAdmin: {
    name: "",
    account: "",
    phone: "",
    name: ""
  },
  organizationCategory: [],
  trainingCategory: []
});
const openEdit = () => {
  console.log("编辑");
  router.push({
    path: "/institution/institutionEdit",
    query: {
      data: form.value.id
    }
  });
};
const formFile = ref({
  institutionLicense: [],
  qualificationDocuments: [],
  video: [],
  environment: []
});
// const tableHeader = ref([
//   {
//     id: "1",
//     label: "创建时间",
//     prop: "createdAt"
//   },
//   {
//     id: "2",
//     label: "机构ID",
//     prop: "id"
//   },
//   {
//     id: "3",
//     label: "机构名称",
//     prop: "name"
//   },
//   {
//     id: "4",
//     label: "机构别名",
//     prop: "alias"
//   },
//   {
//     id: "5",
//     label: "机构营业执照",
//     type: "upload",
//     prop: "institutionLicense"
//   },
//   {
//     id: "6",
//     label: "组织机构代码",
//     prop: "organizationCode"
//   },
//   {
//     id: "7",
//     label: "资质文件",
//     type: "upload",
//     prop: "qualificationDocuments"
//   },
//   {
//     id: "8",
//     label: "客服热线",
//     prop: "customerServiceHotline"
//   },
//   {
//     label: "一句话简介",
//     type: "input",
//     // check: true,
//     prop: "oneSentenceIntroduction",
//     placeholder: "请输入简介",
//     span: 2
//   },
//   {
//     id: "9",
//     label: "机构简介",
//     type: "Editor",
//     prop: "introduction"
//   }
// ]);
// 基本信息
const tableHeatder = ref([
  {
    id: "1",
    label: "机构名称",
    prop: "name",
    show: true
  },
  {
    id: "2",
    label: "机构ID",
    prop: "id",
    show: true
  },
  {
    id: "3",
    label: "机构别名",
    prop: "alias"
  },
  {
    id: "4",
    label: "机构编号",
    prop: "code"
  },
  {
    id: "5",
    label: "客服热线",
    prop: "customerServiceHotline",
    show: true
  },
  {
    id: "6",
    label: "组织机构代码",
    prop: "organizationCode"
  },
  {
    id: "7",
    label: "法定代表人",
    prop: "legalPerson",
    show: true
  },
  {
    id: "8",
    label: "负责人联系方式",
    prop: "principalContact",
    show: true
  },
  {
    id: "9",
    label: "成立时间",
    prop: "establishmentTime",
    type: "time",
    show: true
  },
  {
    id: "10",
    label: "所属行政区域",
    prop: "administrativeDivision",
    show: true
  },
  {
    id: "11",
    label: "经营地址",
    prop: "operatingAddress",
    show: true
  },
  {
    id: "12",
    label: "统一社会信用代码",
    prop: "unifiedSocialCreditCode",
    show: true
  },
  {
    id: "13",
    label: "办学许可证",
    prop: "schoolPermitNumber"
  },
  {
    id: "14",
    label: "注册地址",
    prop: "registeredAddress"
  },
  {
    id: "15",
    label: "机构类别",
    prop: "organizationCategory"
  },
  {
    id: "16",
    label: "培训类别",
    prop: "trainingCategory"
  },
  {
    id: "17",
    label: "服务范围",
    prop: "serviceScope",
    show: true
  }
]);
// 机构介绍
const Introduction = ref([
  {
    id: "1",
    label: "机构logo",
    type: "img",
    prop: "logo"
  },
  {
    id: "2",
    label: "机构筒介",
    type: "input",
    prop: "oneSentenceIntroduction"
  },
  {
    id: "3",
    label: "机构介绍",
    type: "Editor",
    prop: "introduction"
  },
  {
    id: "4",
    label: "宣传视频",
    type: "upload",
    prop: "video"
  },
  {
    id: "5",
    label: "机构环境",
    type: "images",
    prop: "environment"
  }
]);
// 资质信息
const qualifications = ref([
  {
    id: "1",
    label: "营业执照",
    type: "upload",
    prop: "institutionLicense"
  },
  {
    id: "2",
    label: "资质文件",
    type: "upload",
    prop: "qualificationDocuments"
  }
]);

const tableFooter = ref([
  {
    id: "1",
    label: "姓名",
    // prop: form.value.organizationAdmin.name
    prop: "name"
  },
  {
    id: "2",
    label: "账号",
    prop: "account"
  },
  {
    id: "3",
    label: "手机号",
    prop: "phone"
  }
]);
const newData = ref();
const getTableList = async data => {
  // let params = { id: route.query.id };
  try {
    const { code, data, msg } = await organizationFindById();
    // console.log("🎁-----data-----", code, data, msg);
    if (code == 200) {
      form.value = data;
      if (data.organizationCategory.length > 0) {
        form.value.organizationCategory = data.organizationCategory.join("、");
      }
      if (data.trainingCategory.length > 0) {
        form.value.trainingCategory = data.trainingCategory.join("、");
      }
      formFile.value.institutionLicense = [];
      formFile.value.qualificationDocuments = [];
      if (data?.fileDTOS) {
        data.fileDTOS.forEach(item => {
          if (item.fileType == "BUSINESS_LICENSE") {
            formFile.value.institutionLicense.push(item.uploadFile);
          } else if (item.fileType == "QUALIFICATION_DOCUMENT") {
            formFile.value.qualificationDocuments.push(item.uploadFile);
          } else if (item.fileType == "PROMOTIONAL_VIDEO") {
            formFile.value.video.push(item.uploadFile);
          } else if (item.fileType == "ENVIRONMENT") {
            formFile.value.environment.push(item.uploadFile);
          }
        });
      }
      // form.value.organizationAdmin.phone = decrypt(
      //   data?.organizationAdmin.phoneCt
      // );
      newData.value = JSON.parse(JSON.stringify(data));
      richFlag.value = true;
      // form.value.organizationAdmin.phone = null;
      // console.log("🍭-----newData-----", newData.value);
    }
  } catch (error) {
    console.log("catch-----error-----", error);
  }
};
const isView = ref(true);
const isViewFn = () => {
  isView.value = !isView.value;
  if (isView.value) {
    form.value.organizationAdmin.phone = newData.value.organizationAdmin.phone;
  } else {
    form.value.organizationAdmin.phone = decrypt(
      newData.value.organizationAdmin.phoneCt
    );
  }
};
//删除文件
const getDeleted = (item, index) => {
  // form.value[item].splice(index, 1);
  formFile.value[item].splice(index, 1);
};

// 获取图片URL列表用于预览
const getImageUrlList = prop => {
  if (!formFile.value[prop] || formFile.value[prop].length === 0) {
    return [];
  }
  return formFile.value[prop].map(item => item.url);
};

// // 控制显示行
// const shouldShowLabel = item => {
//   return item.show;
// };
</script>

<template>
  <div class="main">
    <el-scrollbar class="scrollbar">
      <!-- <div class="header">
        <div class="title_left">
          <p>创建时间</p>
          <p>{{ formatTime(form.createdAt, "YYYY-MM-DD HH:mm:ss") }}</p>
        </div>
        <div class="title_rigth">
          <p>机构ID</p>
          <p>{{ form.id }}</p>
        </div>
      </div> -->

      <div>
        <div class="account_management">基本信息</div>
        <el-descriptions
          class="margin-top"
          title=""
          :column="1"
          border
          style="width: 100%"
          :label-width="'200px'"
        >
          <template v-for="(item, index) in tableHeatder" :key="index">
            <el-descriptions-item
              v-if="item.show || form[item.prop]"
              label-class-name="my-label"
              label-align="right"
              :span="item.span || 1"
            >
              <template #label>
                <div class="cell-item">
                  {{ item.label }}
                </div>
              </template>
              <div v-if="item.type !== 'time'">
                <div>{{ form[item.prop] || "--" }}</div>
              </div>
              <div v-else>
                {{
                  item.prop === "createdAt" || item.prop === "establishmentTime"
                    ? formatTime(form.createdAt, "YYYY-MM-DD HH:mm:ss")
                    : form[item.prop] || "--"
                }}
              </div>
            </el-descriptions-item>
          </template>
          <!-- <template v-for="(item, index) in tableHeader" :key="index">
            <el-descriptions-item
              label-class-name="my-label"
              label-align="center"
              :span="item.span || 1"
            >
              <template #label>
                <div class="cell-item">
                  {{ item.label }}
                </div>
              </template>
              <div v-if="item.type === 'Editor'" class="Editor">
                <RichEditor
                  v-model="form[item.prop]"
                  :readOnly="true"
                  height="250px"
                  :isOpen="false"
                />
              </div>
              <div v-else-if="item.type === 'upload'">
                <template
                  v-for="(item2, index2) in formFile[item.prop]"
                  :key="index2"
                >
                  <div v-if="item2?.fileName" class="fileOther">
                    <FileItem
                      :data="item2"
                      :index="index2"
                      style="width: 100%"
                      @delete="getDeleted(item2, index2)"
                    />
                  </div>
                </template>
                <div v-show="formFile[item.prop]?.length === 0">{{ "--" }}</div>
              </div>
              <div v-else>
                {{
                  item.prop === "createdAt"
                    ? formatTime(form.createdAt, "YYYY-MM-DD HH:mm:ss")
                    : form[item.prop] || "--"
                }}
              </div>
            </el-descriptions-item>
          </template> -->
        </el-descriptions>
        <div class="account_management">机构介绍</div>
        <el-descriptions
          class="margin-top"
          title=""
          :column="1"
          border
          style="width: 100%"
          :label-width="'200px'"
        >
          <template v-for="(item, index) in Introduction" :key="index">
            <el-descriptions-item
              label-class-name="my-label"
              label-align="right"
              :span="item.span || 1"
            >
              <template #label>
                <div class="cell-item">
                  {{ item.label }}
                </div>
              </template>
              <div v-if="item.type === 'images'">
                <text v-if="formFile[item.prop].length === 0">--</text>
                <template v-else>
                  <el-image
                    v-for="(item2, index2) in formFile[item.prop]"
                    :key="index2"
                    style="width: 200px; height: 200px; margin-right: 10px"
                    :src="item2.url"
                    fit="contain"
                    :preview-src-list="getImageUrlList(item.prop)"
                    :initial-index="index2"
                  />
                </template>
              </div>
              <div v-else-if="item.type === 'Editor'" class="Editor">
                <RichEditor
                  v-model="form[item.prop]"
                  :readOnly="true"
                  height="250px"
                  :isOpen="false"
                />
              </div>
              <div v-else-if="item.type === 'upload'">
                <template
                  v-for="(item2, index2) in formFile[item.prop]"
                  :key="index2"
                >
                  <div v-if="item2?.fileName" class="fileOther">
                    <FileItem
                      :data="item2"
                      :index="index2"
                      style="width: 50%; min-width: 130px"
                      @delete="getDeleted(item2, index2)"
                    />
                  </div>
                </template>
                <div v-show="formFile[item.prop]?.length === 0">{{ "--" }}</div>
              </div>
              <div v-else-if="item.type === 'img'">
                <text v-if="!form[item.prop]">--</text>
                <el-image
                  v-else
                  style="width: 200px; height: 200px"
                  :src="form[item.prop]"
                  fit="cover"
                  :preview-src-list="form[item.prop] ? [form[item.prop]] : []"
                />
              </div>
              <div v-else>
                {{ form[item.prop] || "--" }}
              </div>
            </el-descriptions-item>
          </template>
        </el-descriptions>

        <div class="account_management">资质信息</div>
        <el-descriptions
          class="margin-top"
          title=""
          :column="1"
          border
          style="width: 100%"
          :label-width="'200px'"
        >
          <template v-for="(item, index) in qualifications" :key="index">
            <el-descriptions-item
              label-class-name="my-label"
              label-align="right"
            >
              <template #label>
                <div class="cell-item">
                  {{ item.label }}
                </div>
              </template>
              <div v-if="item.type === 'upload'">
                <template
                  v-for="(item2, index2) in formFile[item.prop]"
                  :key="index2"
                >
                  <div v-if="item2?.fileName" class="fileOther">
                    <FileItem
                      :data="item2"
                      :index="index2"
                      style="width: 50%; min-width: 130px"
                      @delete="getDeleted(item2, index2)"
                    />
                  </div>
                </template>
                <div v-show="formFile[item.prop]?.length === 0">{{ "--" }}</div>
              </div>
            </el-descriptions-item>
          </template>
        </el-descriptions>
      </div>

      <div class="account_management">管理员账号</div>
      <el-descriptions
        class="margin-top"
        title=""
        :column="1"
        border
        style="width: 100%"
        :label-width="'200px'"
      >
        <template v-for="(item, index) in tableFooter" :key="index">
          <el-descriptions-item label-class-name="my-label" label-align="right">
            <template #label>
              <div class="cell-item">
                {{ item.label }}
              </div>
            </template>
            <div class="cell_item">
              {{ form?.organizationAdmin[item.prop] }}
              <span
                v-if="
                  item.prop === 'phone' &&
                  form?.organizationAdmin[item.prop]?.length > 0
                "
                class="icon"
              >
                <el-icon
                  v-if="isView"
                  style="cursor: pointer"
                  @click="isViewFn"
                >
                  <Hide />
                </el-icon>
                <el-icon v-else style="cursor: pointer" @click="isViewFn">
                  <View />
                </el-icon>
              </span>
            </div>
          </el-descriptions-item>
        </template>
      </el-descriptions>
    </el-scrollbar>
    <div class="footer">
      <el-button type="danger" @click="dialogFormVisible = true">
        重置管理员密码
      </el-button>
      <el-button type="primary" @click="openEdit">编辑信息</el-button>
    </div>
    <Popup
      v-if="richFlag"
      :id="newData?.organizationAdmin.id"
      v-model:dialogFormVisible="dialogFormVisible"
      :api="resetPassword"
      :name="newData?.organizationAdmin.name"
      :phone="decrypt(newData?.organizationAdmin.phoneCt)"
      :account="newData?.organizationAdmin.account"
      @reset="reset"
    />
  </div>
</template>

<style lang="scss" scoped>
.main {
  // padding: 20px;
  // padding-top: 0;
  // padding-bottom: 0px;
  background: #fff;
}
.scrollbar {
  // padding-top: 20px;
  padding: 20px;
  // height: calc(100vh - 201px);
  height: calc(100vh - 171px);
  background-color: #fff;
}
.Editor {
  // height: 200px;
  border: 1px solid #e4e4e4e3;
}

.header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 20px;

  .title_left {
    display: flex;

    :nth-child(2) {
      margin-left: 40px;
    }
  }

  .title_rigth {
    display: flex;

    :nth-child(2) {
      margin-left: 40px;
    }
  }
}

.account_management {
  color: #0e1832;
  font-family: Source Han Sans CN;
  font-size: 16px;
  display: flex;
  align-items: center;
  margin: 20px 0 20px 10px;
  font-weight: 500;

  &::after {
    content: "";
    flex: 1;
    height: 1px;
    background: #e4e7ed;
    margin-left: 16px;
  }
}

.footer {
  background: #fff;
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  justify-content: flex-end;

  .footer_left {
    padding: 8px 18px;
    cursor: pointer;
    background: red;
    border-radius: 8px;
  }

  .footer_right {
    padding: 8px 18px;
    cursor: pointer;
    background: #4095e5;
    border-radius: 8px;
  }
}

:deep(.my-label) {
  background: #fff !important;
}
.fileOther {
  // margin-top: 26px;
  margin-left: 20px;
  display: flex;
  align-items: center;
  line-height: 22px;
  width: 420px;
  // margin-bottom: 56px;
  .title {
    color: #0e1832;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    white-space: nowrap;
  }
  .link {
    width: 20px;
    height: 20px;
    margin: 0 10px;
    cursor: pointer;
  }
  .fileName {
    color: #3876fd;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    cursor: pointer;
    // width: 350px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .linkDetele {
    width: 20px;
    height: 20px;
    margin-left: 10px;
    cursor: pointer;
  }
}
.cell_item {
  display: flex;
  .icon {
    display: flex;
    align-items: center;
    margin-left: 5px;
  }
}
:deep(.fileOther) {
  margin-left: 0;
  width: auto;
}
// :deep(.el-descriptions__cell){
// width: auto;
// }
:deep(
  .el-descriptions__body
    .el-descriptions__table.is-bordered
    .el-descriptions__cell
) {
  border: 0px !important;
}
</style>
