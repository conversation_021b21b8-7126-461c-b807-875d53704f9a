import { getFileFullPath } from "@/api/upload";
// 获取文件名
export const getFileName = url => {
  if (!url) return "";
  const arr = url.split("/");
  const len = arr.length;
  return arr[len - 1];
};

// 获取文件完整路径
export const getFileUrl = async relativePath => {
  if (!relativePath) return "";
  let fullUrl = "";
  try {
    const {
      code,
      msg,
      result = []
    } = ({} = await getFileFullPath({ relativePath }));
    if (code === 0 && result) {
      fullUrl = result?.fullPath || "";
    }
  } catch (error) {
    console.log(error);
  }
  return fullUrl;
};

// 获取文件地址，如果不是http或者https开头的，就调用getFileFullUrl获取完整路径
export const getFileFullUrl = async url => {
  if (!url) return "";
  if (url.startsWith("http") || url.startsWith("https")) return url;
  return await getFileUrl(url);
};

// JSON数据处理
export const jsonParse = (data = []) => {
  const baseData = JSON.parse(JSON.stringify(data));
  baseData.forEach(item => {
    if (item.value == "upload") {
      // const { val = [] } = item;
      const backVal = item.backVal || [];
      const val = item.val || [];
      // backVal处理
      item.backVal = backVal.map(valT => {
        return {
          ...valT,
          url: valT.fileName
        };
      });
      // val处理
      item.val = val.map(valT2 => {
        return {
          ...valT2,
          url: valT2.fileName
        };
      });
    }
  });
  console.log("🌵-----baseData-----", baseData);
  return JSON.stringify(baseData);
};
