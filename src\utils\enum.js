// UNPAID,PAY,PAID,REFUNDING,REFUND,PARTIALLY_REFUNDED,CANCELLED,COMPLETED,CLOSED,ERROR
// 订单状态枚举
export const ORDER_ENUM = {
  UNPAID: {
    value: "UNPAID",
    label: "待支付"
  },
  PAY: {
    value: "PAY",
    label: "支付中"
  },
  PAID: {
    value: "PAID",
    label: "已支付"
  },
  REFUNDING: {
    value: "REFUNDING",
    label: "退款中"
  },
  REFUND: {
    value: "REFUND",
    label: "已退款"
  },
  PARTIALLY_REFUNDED: {
    value: "PARTIALLY_REFUNDED",
    label: "部分退款"
  },
  CANCELLED: {
    value: "CANCELLED",
    label: "已取消"
  },
  COMPLETED: {
    value: "COMPLETED",
    label: "已完成"
  },
  CLOSED: {
    value: "CLOSED",
    label: "已关闭"
  },
  ERROR: {
    value: "ERROR",
    label: "支付失败"
  }
};
// 课期审核状态枚举
export const AUDIT_ENUM = {
  ONLINE_UNDER_REVIEW: {
    value: "ONLINE_UNDER_REVIEW",
    label: "上架审核中"
  },
  ONLINE_PASS: {
    value: "ONLINE_PASS",
    label: "上架通过"
  },
  ONLINE_REJECT: {
    value: "ONLINE_REJECT",
    label: "上架驳回"
  },
  OFFLINE_UNDER_REVIEW: {
    value: "OFFLINE_UNDER_REVIEW",
    label: "下架审核中"
  },
  OFFLINE_PASS: {
    value: "OFFLINE_PASS",
    label: "下架通过"
  },
  OFFLINE_REJECT: {
    value: "OFFLINE_REJECT",
    label: "下架驳回"
  },
  NONE: {
    value: "NONE",
    label: "无"
  }
};
// 课期状态枚举
export const COURSE_PERIOD_ENUM = {
  ONLINE_UNDER_REVIEW: {
    value: "ONLINE_UNDER_REVIEW",
    label: "上架审核"
  },
  ONLINE: {
    value: "ONLINE",
    label: "上架"
  },
  OFFLINE_UNDER_REVIEW: {
    value: "OFFLINE_UNDER_REVIEW",
    label: "下架审核"
  },
  OFFLINE: {
    value: "OFFLINE",
    label: "下架"
  },
  COMPLETED: {
    value: "COMPLETED",
    label: "已完成"
  },
  NOT_LISTED: {
    value: "NOT_LISTED",
    label: "未上架"
  }
};
// 常驻状态枚举
export const RESIDENT_STATUS = {
  RESIDENT: {
    value: "RESIDENT",
    label: "常驻"
  },
  NON_RESIDENT: {
    value: "NON_RESIDENT",
    label: "--"
  },
  INVITING: {
    value: "INVITING",
    label: "邀请中"
  },
  REJECTED: {
    value: "REJECTED",
    label: "拒绝常驻"
  }
};
// 学历枚举
export const EDUCATION_LEVEL = {
  DOCTOR: {
    value: "DOCTOR",
    label: "博士"
  },
  MASTER: {
    value: "MASTER",
    label: "硕士"
  },
  BACHELOR: {
    value: "BACHELOR",
    label: "本科"
  },
  COLLEGE: {
    value: "COLLEGE",
    label: "大专"
  },
  HIGH_SCHOOL: {
    value: "HIGH_SCHOOL",
    label: "高中"
  },
  JUNIOR_HIGH_SCHOOL: {
    value: "JUNIOR_HIGH_SCHOOL",
    label: "初中"
  },
  PRIMARY_SCHOOL: {
    value: "PRIMARY_SCHOOL",
    label: "小学"
  }
};
// 邀请类型枚举
export const INVITE_TYPE = {
  INVITE_CLASS: {
    value: "INVITE_CLASS",
    label: "邀请参课"
  },
  INVITE_RESIDENT: {
    value: "INVITE_RESIDENT",
    label: "邀请常驻"
  }
};
// 邀请状态枚举
export const INVITE_STATE = {
  REJECTED: {
    value: "REJECTED",
    label: "已拒绝"
  },
  PENDING: {
    value: "PENDING",
    label: "邀请中"
  },
  ACCEPTED: {
    value: "ACCEPTED",
    label: "已同意"
  }
};
