<script setup>
import { onMounted, ref, nextTick, computed, onActivated } from "vue";
import { formatTime } from "@/utils/index";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { ledeterAll, isFreeze, accountAll } from "@/api/leaderLecturer.js";
import { decrypt, encryption } from "@/utils/SM4.js";
import { View, Hide } from "@element-plus/icons-vue";
import { to, isEmpty } from "@iceywu/utils";
import { courseStore } from "@/store/modules/course.js";
import {
  genderOptions,
  residentOptions
} from "@/views/teacherResource/utils/options.js";
import { getFindModelChildren } from "@/views/teacherResource/utils/dataHttpFanction.js";
import { teacherDataFindAll } from "@/api/teachers/teacherResourcePool.js";
defineOptions({
  name: "LeaderManagement"
});
const useCourseStore = courseStore();
const router = useRouter();
const route = useRoute();
const educationOptions = ref([]); //学历选项
const majorOptions = ref([]); //专业选项
onActivated(() => {
  getTableList();
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});
onMounted(async () => {
  getTableList();
  educationOptions.value = await getFindModelChildren("453"); //学历
  majorOptions.value = await getFindModelChildren("548"); //专业
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});

// 计算表格高度
const searchFormHeight = ref(0);
const tableHeight = ref("calc(100vh - 422px)");

const calculateTableHeight = async () => {
  await nextTick();
  const searchForm = document.querySelector(".con_search");
  if (searchForm) {
    searchFormHeight.value = searchForm.offsetHeight;
    // 动态计算表格高度，减去表单高度、页面其他元素高度和边距
    tableHeight.value = `calc(100vh - 332px - ${searchFormHeight.value}px)`;
  }
};

// 搜索表单
const form = ref({
  name: "",
  gender: "",
  educationLevel: "",
  major: ""
});
// 表格数据
const tableData = ref([]);
const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});
// 获取列表信息
const getTableList = async data => {
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort
    // roleId: 3
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        if (paramsDataKey === "freeze" && form.value[paramsDataKey] === "all") {
          continue;
        }
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }

  // console.log("领队 paramsData =", paramsData);
  // return;
  const [err, result] = await requestTo(teacherDataFindAll(paramsData));
  if (result) {
    result?.content.forEach(item => {
      item.show_phone = false;
      // item.show_card = false;
    });
    tableData.value = result?.content;
    // console.log(
    //   "🌵tableData.value------------------------------>",
    //   tableData.value
    // );
    params.value.totalElements = result.totalElements;
    await nextTick();
    calculateTableHeight();
  } else {
    ElMessage.error(err);
  }
};
const eye_phone = id => {
  const item = tableData.value.find(item => item.id === id);
  if (item) {
    item.type_phone = !item.type_phone;
  }
};
// const eye_card = id => {
//   const item = tableData.value.find(item => item.id === id);
//   if (item) {
//     item.show_card = !item.show_card;
//   }
// };
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTableList();
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getTableList();
};
//搜索
const searchData = () => {
  params.value.page = 1;
  getTableList();
};
// 重置
const setData = () => {
  form.value = {};
  form.value.roleId = 3;
  form.value.freeze = "all";
  params.value.page = 1;
  value1.value = [];
  getTableList();
};
let batchDeleteArr = ref([]);
const tableRef = ref(null);
const getRowKeys = row => {
  return row.id;
};
const handleSelectionChange = val => {
  batchDeleteArr.value = [];
  if (!val.length) return;
  if (val.length > 0) {
    val.forEach(item => {
      batchDeleteArr.value.push(item);
    });
  } else {
    batchDeleteArr.value = val[0];
  }
};
const batchDelete = async () => {
  if (batchDeleteArr.value.length === 0) {
    ElMessage.error("请选择要添加的师资");
    return;
  }
  const arrAdd = batchDeleteArr.value.map(it => {
    return {
      id: it.teacherId,
      name: it.name,
      label: it.name,
      value: it.teacherId
    };
  });
  const existTeachers = useCourseStore.teachersInfo || [];
  const teachersMap = new Map();
  if (existTeachers && existTeachers.length) {
    existTeachers.forEach(item => {
      teachersMap.set(item.id, item);
    });
  }
  arrAdd.forEach(item => {
    teachersMap.set(item.id, item);
  });
  const arr = Array.from(teachersMap.values());
  // console.log('🎁arr------------------------------>',arr);
  // return
  useCourseStore.saveteachersInfo(arr);
  backEvt();
  return;
  // let [err, res] = await to(deletIds({ ids: ids.value }, operateLog));
  // if (res.code === 200 && res.data.length === 0) {
  //   ElMessage.success("添加成功");
  //   batchDeleteArr.value = [];
  //   ids.value = [];
  //   course.value = [];
  //   tableRef.value.clearSelection();
  //   getTablePeriodList();
  // } else {
  //   let coursePeriodName = [];
  //   let reason = [];
  //   res.data.forEach(item => {
  //     coursePeriodName.push(item.coursePeriodName);
  //     reason.push(item.reason);
  //   });
  //   // ElMessage.error(`删除失败,${res.msg}`);
  //   ElMessageBox.confirm(
  //     `${coursePeriodName.map((item, i) => `${item}:${reason[i]}`).join("、")}`,
  //     "添加失败",
  //     {
  //       cancelButtonText: "取消"
  //     }
  //   );
  // }
  // if (err) {
  //   ElMessage.error("添加失败");
  // }
};
// 返回界面
const backEvt = () => {
  if (
    route.query.type === "create" ||
    route.query.type === "draft" ||
    route.query?.type === "createPeriod"
  ) {
    router.replace({
      path: "/course/courseCreate",
      query: {
        type: route.query.type,
        copyId: route.query.copyId,
        courseId: route.query.courseId,
        id: route.query.id,
        create: "create",
        draftId: route.query.draftId
      }
    });
  } else if (route.query.type === "edite") {
    router.replace({
      path: "/course/coursePeriodEdite",
      query: {
        type: route.query.type,
        periodId: route.query.periodId,
        courseId: route.query.courseId,
        fromPage: route.query.fromPage,
        create: "create"
      }
    });
  }
};
const value1 = ref([]);

// 领队详情
const getInfoid = id => {
  router.push({
    path: "/accountNumber/components/teacherDetails",
    query: { title: "ldMang", id: id }
  });
};
</script>

<template>
  <div class="page-container">
    <div class="common">
      <!-- <div class="con_top">
        <div class="titles">领队管理</div>
      </div> -->
      <div class="con_search">
        <el-form :model="form" :inline="true">
          <el-form-item label="姓名">
            <el-input
              v-model.trim="form.name"
              placeholder="请输入"
              clearable
              style="width: 180px"
            />
          </el-form-item>
          <el-form-item label="性别">
            <el-select
              v-model="form.gender"
              style="width: 180px"
              placeholder="请选择性别"
              clearable
              value-key="id"
            >
              <el-option
                v-for="item in genderOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="学历">
            <el-cascader
              v-model="form.educationLevel"
              :options="educationOptions"
              :props="{
                expandTrigger: 'hover',
                value: 'name',
                label: 'name',
                children: 'children',
                emitPath: false
              }"
              style="width: 200px"
              placeholder="请选择学历"
              clearable
              :show-all-levels="false"
            />
          </el-form-item>
          <el-form-item label="专业">
            <el-cascader
              v-model="form.major"
              :options="majorOptions"
              :props="{
                expandTrigger: 'hover',
                value: 'name',
                label: 'name',
                children: 'children',
                emitPath: false
              }"
              style="width: 200px"
              placeholder="请选择专业"
              clearable
              :show-all-levels="false"
            />
          </el-form-item>

          <el-form-item label=" ">
            <div class="flex">
              <el-button type="primary" @click="searchData">搜索</el-button>
              <el-button @click="setData">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div class="containers">
      <el-scrollbar :style="{ height: tableHeight }">
        <div class="con_table">
          <el-table
            ref="tableRef"
            :data="tableData"
            table-layout="fixed"
            :header-cell-style="{
              backgroundColor: '#fafafa',
              color: '#565353'
            }"
            highlight-current-row
            :max-height="tableHeight"
            width="100%"
            :row-key="getRowKeys"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              :reserve-selection="true"
              type="selection"
              width="55"
            />
            <el-table-column prop="name" label="教师姓名" align="left">
              <template #default="scope">
                <div>
                  {{ scope.row.name || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="gender" label="性别" align="left">
              <template #default="scope">
                <div>
                  {{ scope.row.gender === 1 ? "男" : "女" || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="phone"
              label="联系方式"
              align="left"
              min-width="140"
            >
              <template #default="scope">
                <div class="eye_style">
                  {{
                    scope.row.phone
                      ? scope.row.type_phone
                        ? decrypt(scope.row.phoneCt)
                        : scope.row.phone
                      : "-"
                  }}
                  <div
                    v-if="scope.row.phone"
                    class="eye"
                    @click="eye_phone(scope.row.id, scope.row.phoneCt)"
                  >
                    <el-icon v-if="!scope.row.type_phone">
                      <Hide />
                    </el-icon>
                    <el-icon v-else>
                      <View />
                    </el-icon>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="educationLevel" label="学历" align="left">
              <template #default="scope">
                <div>
                  {{ scope.row.educationLevel || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="major" label="专业" align="left">
              <template #default="scope">
                <div>
                  {{ scope.row.major || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column width="200px" prop="createdAt" label="创建时间">
              <template #default="scope">
                <div>
                  {{
                    formatTime(scope.row?.createdAt, "YYYY-MM-DD HH:mm") || "--"
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              align="left"
              min-width="104"
            >
              <template #default="{ row }">
                <div class="operate">
                  <el-button
                    type="primary"
                    link
                    @click="
                      router.push({
                        path: '/courseCreate/teachers/details',
                        query: { id: row.id }
                      })
                    "
                  >
                    详情
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-scrollbar>
      <div class="con_delete">
        <div class="delete-con">
          <div v-if="batchDeleteArr.length > 0">
            <span class="delete-tip">已选{{ batchDeleteArr.length }}个师资：</span>
            <span
              v-for="item in batchDeleteArr"
              :key="item.id"
              class="delete-tip"
              >{{ item.name }}&nbsp;&nbsp;</span>
          </div>
        </div>
        <el-button type="default" @click="batchInvite"> 取消 </el-button>
        <el-button type="primary" @click="batchDelete"> 确认选择 </el-button>
      </div>
      <div class="con_pagination">
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="params.page"
          v-model:page-size="params.size"
          class="pagination"
          background
          :page-sizes="[15, 30, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="params.totalElements"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  // height: 86vh;
  overflow: hidden;
}

.scrollbar {
  background-color: #fff;
}

.common {
  box-sizing: border-box;
  padding: 20px 20px 2px;
  background-color: #fff;
  margin-bottom: 20px;
  // width: calc(100% - 48px);
  .con_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: fit-content;
    margin-bottom: 24px;

    .titles {
      font-size: 20px;
      font-weight: bold;
      color: #606266;
    }
  }

  .con_search {
    display: flex;
    align-items: center;
    width: 100%;
    height: fit-content;
  }
}

.containers {
  box-sizing: border-box;
  // width: calc(100% - 48px);
  // display: flex;
  // flex-direction: column;
  // flex: 1;
  padding: 20px;
  background: #fff;
  // overflow: hidden;

  .con_table {
    // width: calc(100% - 25px);
    // margin-bottom: 24px;
    margin-left: 20px;

    .btnse {
      color: #409eff;
    }
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    padding: 10px 20px;
    background-color: #fff;
  }
  .eye_style {
    width: 120px;
    // display: flex;
    // align-items: center;
    // justify-content: center;
    .eye {
      float: right;
      cursor: pointer;
      :hover {
        color: #409eff;
      }
    }
  }
}
.con_delete {
  margin-top: 10px;
  min-height: 50px;
  .delete-con {
    margin-bottom: 2px;
    height: 40px;
    overflow-y: auto;
  }
}
</style>
