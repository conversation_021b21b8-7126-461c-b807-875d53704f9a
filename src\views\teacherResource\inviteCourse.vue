<script setup>
import { ref, onMounted, onActivated } from "vue";
import { useRouter, useRoute } from "vue-router";
import { complexDelete, findAllNotPage } from "@/api/institution.js";
import { ElMessage, ElMessageBox } from "element-plus";
import { requestTo } from "@/utils/http/tool";
import { formatTime } from "@/utils/index";
import SelectCourse from "./components/selectCourse.vue";
import SelectPeriod from "./components/selectPeriod.vue";
defineOptions({
  name: "BaseManage"
});
const router = useRouter();
const route = useRoute();
const isCourse = ref(true);
const sendCourse = ref({});
const selectCourse = val => {
  // console.log("🐬val------------------------------>", val);
  if (val) {
    isCourse.value = false;
    sendCourse.value = val;
  }
};
const selectIsCourse = val => {
  isCourse.value = val;
};
</script>

<template>
  <div class="containers">
    <div class="con_top">
      <div v-if="isCourse">请选择课程</div>
      <div v-else>请选择课期</div>
    </div>
    <el-scrollbar class="scrollbar">
      <SelectCourse v-if="isCourse" @select-course="selectCourse" />
      <SelectPeriod
        v-else
        :sendCourse="sendCourse"
        @select-is-course="selectIsCourse"
      />
    </el-scrollbar>
  </div>
</template>

<style lang="scss" scoped>
.scrollbar {
  // padding: 20px;
  // height: calc(100vh - 151px);

  background-color: #fff;
}
.containers {
  box-sizing: border-box;
  background: #fff;

  .con_top {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: fit-content;
    padding: 20px 20px 0 20px;
  }
}
</style>
