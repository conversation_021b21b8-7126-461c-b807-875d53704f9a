<script setup>
import { ref, reactive, onMounted } from "vue";
import TabTitle from "@/components/Base/tabInfo.vue";
import Scheduling from "./components/scheduling.vue";
import CoursePeriodTrip from "./components/coursePeriodTrip.vue";
import BaseInfo from "./components/baseInfoEdite.vue";
import PriceSetting from "./components/priceEdite.vue";
import courseIntroduction from "./components/introductionEdite.vue";
import JobDesign from "./components/homeWorkEdited.vue";
import Complete from "./components/complete.vue";
import InstructionText from "./components/instructionText.vue";
import { useRouter, useRoute } from "vue-router";
import KnowledgePoints from "./components/knowledgePoints.vue";
import {
  courseAdd,
  complexId,
  courseTypeFind,
  courseEdite,
  courseFindId
} from "@/api/course.js";
import { findAllCourseType } from "@/api/period.js";
import { requestTo } from "@/utils/http/tool";
import { ElIcon, ElMessage, ElMessageBox, ElLoading } from "element-plus";
import { Tickets } from "@element-plus/icons-vue";
import { uploadFile } from "@/utils/upload/upload.js";
import { courseStore } from "@/store/modules/course.js";

import { createCourse } from "@/utils/createTestData.js";

createCourse();

const useCourseStore = courseStore();
const { courseInfo } = useCourseStore;
const router = useRouter();
const route = useRoute();
const activeName = ref(0);
// tab切换
const tabTitle = ref([
  { id: 1, name: "基础信息", enName: "foundation" },
  { id: 2, name: "行程安排", enName: "trip" },
  { id: 3, name: "课期介绍", enName: "introduce" },
  { id: 4, name: "课期知识点", enName: "knowledge" },
  { id: 5, name: "材料说明", enName: "equipment" },
  { id: 6, name: "注意事项", enName: "matter" },
  { id: 7, name: "价格设置", enName: "price" },
  { id: 8, name: "实践感悟", enName: "task" },
  { id: 9, name: "用户协议", enName: "agreement" }
]);
const infoShow = ref("基础信息");
const infoShowEnName = ref("foundation");
const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const submitLoading = ref(false);
const periodName = ref("");
const text = ref("这里是该页面的信息填写说明以及填写建议");
const handleClick = (item, index) => {
  if (item.props.label === "基础信息") {
    infoShowEnName.value = "foundation";
  } else if (item.props.label === "行程安排") {
    infoShowEnName.value = "trip";
  } else if (item.props.label === "课期介绍") {
    infoShowEnName.value = "introduce";
  } else if (item.props.label === "课期知识点") {
    infoShowEnName.value = "knowledge";
  } else if (item.props.label === "材料说明") {
    infoShowEnName.value = "equipment";
  } else if (item.props.label === "注意事项") {
    infoShowEnName.value = "matter";
  } else if (item.props.label === "价格设置") {
    infoShowEnName.value = "price";
  } else if (item.props.label === "实践感悟") {
    infoShowEnName.value = "task";
  } else {
    infoShowEnName.value = "agreement";
  }
  activeName.value = item.props.name;
  infoShow.value = item.props.label;
  if (item.props.label === "课期介绍" || item.props.label === "课程介绍") {
    text.value =
      "课期介绍采用图文方式展示，建议在课期介绍中展示以下内容：课程的内容、课程的特点、课程的往期开展介绍和照片、当前课期的领队讲师介绍、当前课期的特殊之处";
  } else {
    text.value = "这里是该页面的信息填写说明以及填写建议";
  }
  // console.log('💗handleClick---------->');
};
const tabInfoEvt = val => {
  // console.log("🌵val------------------------------>", val);
  infoShow.value = val.name;
  if (val?.name === "课期介绍" || val?.name === "课程介绍") {
    text.value =
      "课期介绍采用图文方式展示，建议在课期介绍中展示以下内容：课程的内容、课程的特点、课程的往期开展介绍和照片、当前课期的领队讲师介绍、当前课期的特殊之处";
  } else {
    text.value = "这里是该页面的信息填写说明以及填写建议";
  }
};

const baseInfoPeriod = val => {
  periodName.value = val;
};

onMounted(() => {});
</script>

<template>
  <div class="course-add">
    <!-- tab切换 -->
    <el-tabs
      v-model="activeName"
      class="demo-tabs"
      tab-position="left"
      @tab-click="handleClick"
    >
      <el-tab-pane
        v-for="(item, index) in tabTitle"
        :key="index"
        :label="item.name"
        :name="index"
      />
      <InstructionText :text="text" />
      <!-- 切换信息 -->
      <div class="course-container">
        <BaseInfo
          v-if="infoShow === '基础信息'"
          :infoShowEnName="infoShowEnName"
          @base-info-period="baseInfoPeriod"
        />
        <CoursePeriodTrip
          v-else-if="infoShow === '行程安排'"
          :isNewEdit="'edit'"
          :periodName="periodName"
          :infoShowEnName="infoShowEnName"
        />
        <PriceSetting
          v-else-if="infoShow === '价格设置'"
          :periodName="periodName"
          :infoShowEnName="infoShowEnName"
        />
        <courseIntroduction
          v-else-if="
            infoShow === '课期介绍' ||
            infoShow === '材料说明' ||
            infoShow === '注意事项' ||
            infoShow === '用户协议'
          "
          :infoShow="infoShow"
          :btnText="btnText"
          :showContent="showContent"
          :periodName="periodName"
          :infoShowEnName="infoShowEnName"
        />
        <KnowledgePoints
          v-else-if="infoShow === '课期知识点'"
          :periodName="periodName"
          :infoShow="infoShow"
          :infoShowEnName="infoShowEnName"
        />
        <JobDesign
          v-else-if="infoShow === '实践感悟'"
          :periodName="periodName"
          :infoShowEnName="infoShowEnName"
        />
        <Complete v-else-if="infoShow === '完成'" :periodName="periodName" />
      </div>
    </el-tabs>

    <!-- <TabTitle :tabTitle="tabTitle" @tab-data="tabInfoEvt" /> -->
  </div>
</template>

<style lang="scss" scoped>
.course-add {
  box-sizing: border-box;
  // padding: 22px 30px;
  font-size: 14px;
  //   font-family: PingFangSC-regular;
  color: #101010;
  padding: 20px 20px 20px 0;
  background-color: #fff;
  position: relative;
  .info-tab {
    box-sizing: border-box;
    height: 100px;
    width: 100%;
    border-bottom: 1px solid #bbbbbb;
    display: flex;
    justify-content: center;
    .item {
      display: flex;
      .item-box {
        display: flex;
        flex-direction: column;
        justify-content: center;
        .icon-box {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          .icon {
            width: 44px;
            height: 44px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            border: 1px solid #bbbbbb;
          }
          .item-line {
            width: 80px;
            // width: 100%;
            height: 1px;
            background-color: #bbbbbb;
          }
        }
        .text {
          white-space: nowrap;
        }
      }
      .item-box:last-child {
        .item-line {
          display: none;
        }
      }
    }
  }

  .course-container {
    box-sizing: border-box;
    width: 100%;
    height: calc(100vh - 240px);
    overflow-y: auto;
    padding: 10px 0 0 0;
    .upload {
      margin: 15px 0;
    }

    .star {
      margin-right: 3px;
      color: red;
    }
  }
}
:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  width: 240px;
  background: #e1f5ff;
}
:deep(.el-tabs--left .el-tabs__nav-scroll) {
  height: calc(100vh - 150px);
}
:deep(.el-tabs__content) {
  margin-left: 10px;
}
</style>
