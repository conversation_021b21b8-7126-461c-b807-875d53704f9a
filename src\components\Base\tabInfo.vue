<script setup>
import { ref, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
const props = defineProps({
  tabTitle: {
    type: Array,
    default: () => []
  }
});
const emits = defineEmits(["tabData"]);
const router = useRouter();
const route = useRoute();
const activeName = ref(0);
// 切换tab
const handleClick = (item, index) => {
  activeName.value = item.props.name;
  // console.log(
  //   "🌵 activeName.value------------------------------>",
  //   activeName.value
  // );
  let obj = {
    index: item.props.name,
    name: item.props.label
  };
  emits("tabData", obj);
};
onMounted(() => {
  if (route.query.infoShow) {
    const findItem = props.tabTitle.find((item, index) => {
      return item.name === route.query.infoShow;
    });
    activeName.value = findItem.id;
    // console.log('🌈findItem------------------------------>',findItem);
  }
  // infoShow.value = route.query.infoShow;
});
</script>

<template>
  <!-- <div class="tab-btn">
    <div
      v-for="(item, index) in tabTitle"
      :key="index"
      :class="tabTarget === index ? 'tab-box-active' : 'tab-box'"
      @click="tabEvt(item, index)"
    >
      {{ item.name }}
    </div>
  </div> -->
  <el-tabs
    v-model="activeName"
    type="card"
    class="demo-tabs"
    @tab-click="handleClick"
  >
    <el-tab-pane
      v-for="(item, index) in tabTitle"
      :key="index"
      :label="item.name"
      :name="index"
    />
    <!-- <el-tab-pane label="Config" name="second">Config</el-tab-pane>
    <el-tab-pane label="Role" name="third">Role</el-tab-pane>
    <el-tab-pane label="Task" name="fourth">Task</el-tab-pane> -->
  </el-tabs>
</template>

<style lang="scss" scoped>
.tab-btn {
  display: flex;
  width: 100%;
  height: 36px;

  .tab-box {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 36px;
    font-size: 14px;
    color: rgb(16 16 16 / 100%);
    background-color: rgb(255 255 255 / 100%);
    border: 1px solid rgb(187 187 187 / 100%);
  }

  .tab-box-active {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 36px;
    font-size: 14px;
    color: rgb(255 255 255 / 100%);
    background-color: rgb(64 149 229 / 100%);
  }

  .demo-tabs > .el-tabs__content {
    padding: 32px;
    font-size: 32px;
    font-weight: 600;
    color: #6b778c;
  }
}

:deep(.el-tabs__nav) {
  background-color: #fff !important;
}

:deep(.el-tabs__item.is-active) {
  color: #fff;
  background-color: rgb(64 149 229 / 100%);
}

:deep(.el-tabs__header) {
  margin: 0 0 2px;
}

:deep(.el-tabs--card > .el-tabs__header) {
  border-bottom: transparent !important;
}
</style>
