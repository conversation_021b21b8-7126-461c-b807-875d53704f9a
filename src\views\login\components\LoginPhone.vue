<script setup lang="ts">
import type { FormInstance } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { $t, transformI18n } from "@/plugins/i18n";
import { useUserStoreHook } from "@/store/modules/user";
import { message } from "@/utils/message";
import Iphone from "@iconify-icons/ep/iphone";
import { reactive, ref, toRaw } from "vue";
import { useI18n } from "vue-i18n";
import Motion from "../utils/motion";
import { phoneRules } from "../utils/rule";
import { useVerifyCode } from "../utils/verifyCode";
import { getLoginPhone, getLoginById } from "@/api/user";
import { encryption } from "@/utils/SM4.js";
import { getTopMenu, initRouter } from "@/router/utils";
import { useRouter } from "vue-router";
import {
  User,
  InfoFilled,
  Avatar,
  UserFilled,
  OfficeBuilding,
  Check,
  Right,
  ArrowLeft
} from "@element-plus/icons-vue";

const { t } = useI18n();
const loading = ref(false);
const ruleForm = reactive({
  phone: "",
  codeType: "LOGIN",
  code: ""
});
const ruleFormRef = ref<FormInstance>();
const { isDisabled, text } = useVerifyCode();

// 多账号选择相关状态
const showAccountDialog = ref(false);
const accountList = ref([]);
const selectedAccountId = ref("");
const showAccountSelection = ref(false); // 显示账号选择页面

const router = useRouter();

const onLogin = async (formEl: FormInstance | undefined) => {
  loading.value = true;
  if (!formEl) return;
  await formEl.validate(async valid => {
    if (valid) {
      const params = {
        phone: encryption(ruleForm.phone),
        code: ruleForm.code
      };

      try {
        const res = await getLoginPhone(params);

        // 检查返回的账号数量
        if (res.data && res.data.length > 1) {
          // 多个账号，显示选择页面
          accountList.value = res.data;
          showAccountSelection.value = true;
          loading.value = false;
        } else if (res.data && res.data.length === 1) {
          // 单个账号，直接登录
          await loginWithAccount(res.data[0].id);
        } else {
          message("当前手机号尚未绑定平台账号，请先完成绑定后再次尝试", {
            type: "error"
          });
          loading.value = false;
        }
      } catch (err) {
        console.log(err);
        message("登录失败，请重试", { type: "error" });
        loading.value = false;
      }
    } else {
      loading.value = false;
    }
  });
};

// 使用指定账号登录
const loginWithAccount = async (accountId: string) => {
  loading.value = true;
  try {
    await useUserStoreHook()
      .loginById({ id: accountId }, "手机号")
      .then(res => {
        return initRouter().then(() => {
          router.push(getTopMenu(true).path).then(() => {
            message(t("login.pureLoginSuccess"), { type: "success" });
          });
        });
      })
      .catch(err => {
        if (err[0] === 10015) {
          message(t("login.pureLoglock"), { type: "error" });
        } else {
          message(t("login.pureLoginagain"), { type: "error" });
        }
        // 登录失败后，重新显示账号选择页面
        showAccountSelection.value = true;
        throw err; // 重新抛出错误，让外层catch处理
      });
  } catch (err) {
    console.log(err);
    if (!showAccountSelection.value) {
      // 如果还没有显示账号选择页面，则显示通用错误信息并显示选择页面
      message("登录失败，请重试", { type: "error" });
      showAccountSelection.value = true;
    }
  } finally {
    // 确保loading状态总是被重置
    loading.value = false;
    // 隐藏账号选择页面
    showAccountSelection.value = false;
  }
};

// 确认选择的账号
const confirmAccountSelection = async () => {
  if (!selectedAccountId.value) {
    message("请选择要登录的账号", { type: "warning" });
    return;
  }

  // 开始登录流程，保持在账号选择页面显示loading
  await loginWithAccount(selectedAccountId.value);
};

// 取消账号选择，返回登录页面
const cancelAccountSelection = () => {
  showAccountSelection.value = false;
  accountList.value = [];
  selectedAccountId.value = "";
  loading.value = false;
};

function onBack() {
  useVerifyCode().end();
  useUserStoreHook().SET_CURRENTPAGE(0);
}

// 切换到二维码登录
function switchToQrLogin() {
  useVerifyCode().end();
  useUserStoreHook().SET_CURRENTPAGE(2);
}
</script>

<template>
  <!-- 手机号登录表单 -->
  <div v-if="!showAccountSelection">
    <el-form
      ref="ruleFormRef"
      :model="ruleForm"
      :rules="phoneRules"
      size="large"
    >
      <Motion>
        <el-form-item prop="phone">
          <el-input
            v-model="ruleForm.phone"
            clearable
            :placeholder="t('login.purePhone')"
            :prefix-icon="useRenderIcon(Iphone)"
          />
        </el-form-item>
      </Motion>

      <Motion :delay="100">
        <el-form-item prop="code">
          <div class="w-full flex justify-between">
            <el-input
              v-model="ruleForm.code"
              clearable
              :placeholder="t('login.pureSmsVerifyCode')"
              :prefix-icon="useRenderIcon('ri:shield-keyhole-line')"
            />
            <el-button
              :disabled="isDisabled"
              class="ml-2"
              @click="
                useVerifyCode().start(ruleFormRef, 'phone', toRaw(ruleForm))
              "
            >
              {{
                text.length > 0
                  ? text + t("login.pureInfo")
                  : t("login.pureGetVerifyCode")
              }}
            </el-button>
          </div>
        </el-form-item>
      </Motion>

      <Motion :delay="150">
        <el-form-item>
          <el-button
            class="w-full"
            size="default"
            type="primary"
            :loading="loading"
            @click="onLogin(ruleFormRef)"
          >
            {{ t("login.pureLogin") }}
          </el-button>
        </el-form-item>
      </Motion>

      <Motion :delay="300">
        <el-form-item>
          <div class="w-full h-[20px] flex justify-between items-center">
            <el-button class="w-full" size="default" @click="onBack">
              <!-- {{ t("login.pureBack") }} -->
              {{ "账号登录" }}
            </el-button>
            <el-button class="w-full" size="default" @click="switchToQrLogin">
              <!-- {{ t("login.pureBack") }} -->
              {{ "二维码登录" }}
            </el-button>
          </div>
        </el-form-item>
      </Motion>
    </el-form>
  </div>

  <!-- 账号选择页面 -->
  <div v-if="showAccountSelection" class="account-selection-page">
    <!-- 页面头部 -->
    <Motion>
      <div class="selection-header">
        <div class="header-top">
          <el-button
            link
            type="primary"
            class="back-button"
            @click="cancelAccountSelection"
          >
            <el-icon class="mr-1">
              <ArrowLeft />
            </el-icon>
            切换登录方式
          </el-button>
        </div>
        <div class="header-content">
          <h3 class="selection-title">选择登录账号</h3>
          <p class="selection-subtitle">
            当前手机号关联了 {{ accountList.length }} 个账号，请选择要登录的账号
          </p>
        </div>
      </div>
    </Motion>

    <!-- 账号列表 -->
    <Motion :delay="100">
      <div class="accounts-section">
        <div
          v-for="account in accountList"
          :key="account.id"
          class="account-card"
          :class="{ selected: selectedAccountId === account.id }"
          @click="selectedAccountId = account.id"
        >
          <div class="account-content">
            <div class="account-info flex items-center">
              <div class="account-name mr-2">
                {{ account.name || account.username || "未知用户" }}
              </div>
              <div class="account-organization">
                {{ account.organizationName || "未知机构" }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Motion>

    <!-- 底部按钮 -->
    <Motion :delay="200">
      <div class="selection-footer">
        <el-button
          type="primary"
          size="large"
          class="continue-button"
          :loading="loading"
          :disabled="!selectedAccountId"
          @click="confirmAccountSelection"
        >
          继续
          <el-icon class="ml-1">
            <Right />
          </el-icon>
        </el-button>
      </div>
    </Motion>
  </div>

  <!-- 账号选择对话框 -->
  <el-dialog
    v-model="showAccountDialog"
    title=""
    width="440px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="account-dialog"
    align-center
  >
    <!-- 自定义标题 -->
    <template #header>
      <div class="dialog-header">
        <div class="header-icon">
          <el-icon size="24" color="#3b82f6">
            <User />
          </el-icon>
        </div>
        <div class="header-content">
          <h3 class="header-title">选择登录账号</h3>
          <p class="header-subtitle">
            当前手机号关联了多个账号，请选择要登录的账号
          </p>
        </div>
      </div>
    </template>

    <div class="account-selection">
      <div class="account-list">
        <div
          v-for="account in accountList"
          :key="account.id"
          class="account-item"
          :class="{ 'account-item-selected': selectedAccountId === account.id }"
          @click="selectedAccountId = account.id"
        >
          <div class="account-content">
            <div class="account-avatar">
              <span class="avatar-text">
                {{ (account.name || account.username || "未知用户").charAt(0) }}
              </span>
            </div>
            <div class="account-info">
              <div class="account-name-row">
                <span class="account-name">
                  {{ account.name || account.username || "未知用户" }}
                </span>
                <el-icon
                  v-if="selectedAccountId === account.id"
                  size="16"
                  color="#3b82f6"
                  class="check-icon"
                >
                  <Check />
                </el-icon>
              </div>
              <div class="account-organization">
                {{ account.organizationName || "未知机构" }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button
          size="large"
          class="cancel-btn"
          @click="cancelAccountSelection"
        >
          取消
        </el-button>
        <el-button
          type="primary"
          size="large"
          :loading="loading"
          :disabled="!selectedAccountId"
          class="confirm-btn"
          @click="confirmAccountSelection"
        >
          确认登录
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
/* 账号选择对话框样式 */
:deep(.account-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.account-dialog .el-dialog__header) {
  padding: 0;
  margin: 0;
  border-bottom: none;
}

:deep(.account-dialog .el-dialog__body) {
  padding: 0 24px 24px;
}

:deep(.account-dialog .el-dialog__footer) {
  padding: 16px 24px 24px;
  border-top: none;
}

/* 对话框头部 */
.dialog-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 24px;
  gap: 12px;
}

.header-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #dbeafe;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.header-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  line-height: 1.2;
}

.header-subtitle {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  max-width: 320px;
}

/* 账号列表 */
.account-selection {
  padding: 16px 0;
}

.account-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.account-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
  background: #fff;
  cursor: pointer;
}

.account-item:hover {
  border-color: #d1d5db;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.account-item-selected {
  border-color: #3b82f6 !important;
  background: #eff6ff;
  box-shadow: 0 0 0 2px #3b82f6;
}

.account-content {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 12px;
}

.account-avatar {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar-text {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
}

.account-info {
  flex: 1;
  min-width: 0;
}

.account-name-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.account-name {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.check-icon {
  flex-shrink: 0;
}

.account-organization {
  font-size: 14px;
  color: #6b7280;
  margin-top: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 底部按钮 */
.dialog-footer {
  display: flex;
  gap: 12px;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  border-radius: 6px;
  font-weight: 500;
}

:deep(.cancel-btn) {
  border-color: #d1d5db;
  color: #374151;
}

:deep(.cancel-btn:hover) {
  border-color: #9ca3af;
  background: #f9fafb;
}

:deep(.confirm-btn) {
  background: #3b82f6;
  border-color: #3b82f6;
}

:deep(.confirm-btn:hover) {
  background: #2563eb;
  border-color: #2563eb;
}

:deep(.confirm-btn:disabled) {
  background: #d1d5db;
  border-color: #d1d5db;
  color: #9ca3af;
}

/* 账号选择页面样式 */
.account-selection-page {
  width: 100%;
  max-width: 400px;
}

.selection-header {
  text-align: left;
  margin-bottom: 24px;
}

.header-top {
  margin-bottom: 16px;
}

.back-button {
  padding: 0;
  font-size: 14px;
}

.header-content {
  margin-top: 8px;
}

.selection-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  text-align: left;
}

.selection-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  text-align: left;
}

.accounts-section {
  margin-bottom: 24px;
}

.account-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #fff;
}

.account-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.account-card.selected {
  border-color: #1677ff;
  background: #fff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.2);
}

.account-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.account-avatar {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar-text {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
}

.account-info {
  flex: 1;
  min-width: 0;
}

.account-name {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.account-organization {
  font-size: 14px;
  color: #6b7280;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.selection-footer {
  position: sticky;
  bottom: 0;
  background: #fff;
  padding: 16px 0;
  border-top: 1px solid #f3f4f6;
  margin-top: 24px;
}

.continue-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 640px) {
  :deep(.account-dialog) {
    width: 90vw !important;
    margin: 0 5vw;
  }

  .dialog-header {
    padding: 20px 16px;
  }

  .header-icon {
    width: 40px;
    height: 40px;
  }

  .header-title {
    font-size: 18px;
  }

  .account-content {
    padding: 12px;
  }

  .account-avatar {
    width: 36px;
    height: 36px;
  }

  .avatar-text {
    font-size: 14px;
  }

  .account-name {
    font-size: 15px;
  }

  .selection-title {
    font-size: 20px;
  }

  .account-card {
    padding: 12px;
  }
}
</style>
