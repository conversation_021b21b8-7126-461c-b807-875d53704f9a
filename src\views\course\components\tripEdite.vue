<script setup>
import { onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { findByCoursePeriodId, itineraryDelete } from "@/api/period.js";
import { requestTo } from "@/utils/http/tool";
import { formatTime } from "@/utils/index";
import DescriptionList from "./descriptionList.vue";
import dayjs from "dayjs";
const router = useRouter();
const route = useRoute();
// 表格
const tableData = ref([]);
// 课期行程列表查询
const findCoursePeriodList = async type => {
  const params = {
    coursePeriodId: route.query.periodId
  };
  let [err, res] = await requestTo(findByCoursePeriodId(params));
  if (res) {
    console.log("🌳-----res-----", res);
    tableData.value = res;
  } else {
    console.log("🌵-----err-----", err);
  }
};

// 新增行程点
const addItinerary = () => {
  router.push({
    path: "/course/currentDetails/itineraryAdd",
    query: {
      type: "create",
      periodId: route.query.periodId
    }
  });
};
// 编辑
const editeEvt = val => {
  console.log("💗editeEvt---------->");
  router.push({
    path: "/course/currentDetails/itineraryAdd",
    query: {
      type: "edite",
      editId: val,
      periodId: route.query.periodId
    }
  });
};
const periodName = ref("");
const periodNameEvt = val => {
  periodName.value = val;
};
// 删除
const deteleEvt = val => {
  const operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `将“${periodName.value}”课期中的“${val.title}”行程点删除了`
    // operatorTarget: form.value.name,
  };
  ElMessageBox.confirm(`确定要删除该条行程吗`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      let res = await itineraryDelete({ id: val.id }, operateLog);
      if (res.code == 200) {
        ElMessage({
          type: "success",
          message: "删除成功"
        });
        findCoursePeriodList();
      } else {
        ElMessage({
          type: "error",
          message: "删除失败"
        });
      }
    })
    .catch(() => {});
};
onMounted(() => {
  findCoursePeriodList();
});
</script>

<template>
  <div>
    <DescriptionList
      :periodId="Number(route.query.periodId)"
      @name="periodNameEvt"
    />
    <div class="trip-add">
      <div class="btn-top">
        <el-button
          @click="
            router.replace({
              path: '/course/courseDetails/currentDetails',
              query: { periodId: Number(route.query.periodId) }
            })
          "
        >
          返回
        </el-button>
        <el-button type="primary" class="editeBtn" @click="addItinerary">
          新增行程点
        </el-button>

        <!-- <el-button type="warning" class="editeBtn" @click="router.back()">
        取消
      </el-button> -->
      </div>
      <el-table
        :data="tableData"
        table-layout="fixed"
        :header-cell-style="{ backgroundColor: '#fafafa', color: '#565353' }"
        highlight-current-row
        height="calc(100vh - 46vh)"
      >
        <el-table-column prop="startTime" label="日期" min-width="120">
          <template #default="scope">
            <div>
              {{ formatTime(scope.row.startTime, "YYYY-MM-DD") || "--" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="时间点" align="left">
          <template #default="scope">
            <div>
              {{ formatTime(scope.row.startTime, "HH:mm:ss") || "--" }}
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="title" label="行程标题" align="left">
          <template #default="scope">
            <div>
              {{ scope.row.title || "--" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="address"
          fixed="right"
          label="操作"
          width="200px"
        >
          <template #default="{ row }">
            <div class="option">
              <div class="btnse" @click="editeEvt(row.id)">编辑</div>
              <div class="detele" @click="deteleEvt(row)">删除</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.trip-add {
  box-sizing: border-box;
  width: 100%;
  height: 650px;
  padding: 20px 20px;
  background-color: #fff;
  .btn-top {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
    margin-bottom: 20px;
  }
  .cancel {
    background: #e6983a;
  }
  .option {
    display: flex;
    .btnse {
      cursor: pointer;
      color: #4095e5;
      margin-right: 20px;
    }
    .detele {
      cursor: pointer;
      color: #f56c6c;
    }
  }
}
</style>
