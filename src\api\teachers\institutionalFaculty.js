import { http } from "@/utils/http";

/*  机构师资相关接口  */
// 分页查询
export const findByOrganization = params => {
  return http.request(
    "get",
    "/organization/teacherDatabase/finByOrganization",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
// 删除
export const deleteOrganizationTeacher = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/teacherDatabase/deleteOrganizationTeacherDatabase",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "TEACHER_DATABASE",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "删除了师资"
      }
    }
  );
};
// 查询参与课程
export const institutionCoursePeriod = params => {
  return http.request(
    "get",
    "/organization/teacherDatabaseCoursePeriod/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};
