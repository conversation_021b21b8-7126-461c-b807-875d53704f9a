<script setup>
import { onMounted, ref, nextTick, onActivated } from "vue";
import { formatTime, downloadFileBySaveAs } from "@/utils/index";
import {
  findOrganizationFinancial,
  financialRecordFindAll,
  financialRecordExport
} from "@/api/institution";
import { ElLoading, ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { getAsyncTask } from "@/utils/common";
defineOptions({
  name: "FinanceManages"
});
onMounted(() => {
  // Id.value = route.query.id;
  // organizationName.value = route.query.name;
  // console.log("🌳-----Id.value-----", Id.value);
  getFinancial();
  getTableList();
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
  // courseTypeFindApi();
});
onActivated(() => {
  getFinancial();
  getTableList();
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});
// 计算表格高度
const searchFormHeight = ref(0);
const tableHeight = ref("calc(100vh - 400px)");

const calculateTableHeight = async () => {
  await nextTick();
  const searchForm = document.querySelector(".con_search");
  if (searchForm) {
    searchFormHeight.value = searchForm.offsetHeight;
    // 动态计算表格高度，减去搜索框高度、表头按钮高度、分页器高度和其他间距
    tableHeight.value = `calc(100vh - 260px - ${searchFormHeight.value}px - 46px)`;
  }
};

const Id = ref(0);
const organizationName = ref("");
const router = useRouter();
const route = useRoute();
// 搜索表单
const form = ref({
  startTime: "",
  endTime: "",
  recordType: "",
  channelType: "",
  feeType: ""
});
// 表格数据
const tableData = ref([]);
const params = ref({
  page: 1,
  size: 15,
  sort: "transactionTime,desc",
  totalElements: 0
});
const courseTypeoptions = ref([
  {
    value: "",
    label: "全部"
  },
  {
    value: "INCOME",
    label: "收入"
    // type: "INCOME"
  },
  {
    value: "EXPENDITURE",
    label: "支出"
    // type: "EXPENDITURE"
  }
]);
const channelTypeoptions = ref([
  {
    value: "",
    label: "全部"
  },
  {
    value: "WECHAT",
    label: "微信支付"
    // type: "WECHAT"
  },
  {
    value: "ALPAY",
    label: "支付宝支付"
    // type: "ALPAY"
  },
  {
    value: "PUBLIC_ACCOUNT",
    label: "公账"
    // type: "PUBLIC_ACCOUNT"
  },
  {
    value: "PLATFORM",
    label: "平台"
    // type: "PLATFORM"
  }
]);
const feeTypeoptions = ref([
  {
    value: "",
    label: "全部"
  },
  {
    value: "CLASS_HOUR",
    label: "课时费"
  },
  {
    value: "INSURANCE",
    label: "保险费"
  },
  {
    value: "MATERIAL",
    label: "材料费"
  },
  {
    value: "SERVICE",
    label: "服务费"
  }
]);
const financialData = ref({});
const getFinancial = async () => {
  const params = {
    // organizationId: Id.value
  };
  console.log("🐳-----params-----", params);
  try {
    const { code, data, msg } = await findOrganizationFinancial(params);
    financialData.value = data;
    console.log("🎉-----code, data, msg-----", code, data, msg);
  } catch (error) {
    console.log("🚨-----error-----", error);
  }
};
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async item => {
  if (getListLoading.value) return;
  // return;
  getListLoading.value = true;
  let paramsData = {
    // organizationId: Id.value,
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort
  };
  // Only include supported API parameters
  const supportedParams = [
    "startTime",
    "endTime",
    "recordType",
    "channelType",
    "feeType"
  ];
  for (const key of supportedParams) {
    if (form.value[key]) {
      paramsData[key] = form.value[key];
    }
  }
  console.log("🍧-----paramsData-----", paramsData);
  const { code, data, msg } = await financialRecordFindAll(paramsData);
  console.log("🎁-----data-----", data);
  if (data) {
    tableData.value = data?.content;

    params.value.totalElements = data.totalElements;
  }
  getListLoading.value = false;
};
const handleSizeChange = val => {
  params.value.size = val;
  getTableList();
};
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTableList();
};
// 编辑
const edit = item => {
  sessionStorage.setItem("orderDetailsDataV2", JSON.stringify(item));
  router.push({
    path: "/course/financeManage/orderDetail",
    // query: { data: JSON.stringify(item) }
    query: { ordersId: item.ordersId }
  });

  console.log(item, "item");
};
// 账务
// const getId = id => {
//   router.push({ path: "/course/courseDetails", query: { id: 1 } });
// };
const goSet = () => {
  router.push({ path: "/course/financeManage/bankAccount", query: { id: 1 } });
};
//搜索
const searchData = () => {
  params.value.page = 1;
  getTableList();
};
// 重置
const setData = () => {
  form.value = {
    startTime: "",
    endTime: "",
    recordType: "",
    channelType: "",
    feeType: ""
  };
  params.value.page = 1;
  value1.value = [];
};
// 选择时间
const timeChange = value => {
  // console.log("🐬-----value-----", value);
  if (!value || value.length !== 2) return;

  // 开始时间（默认 00:00:00）
  form.value.startTime = new Date(value[0]).getTime();

  // 结束时间（设置为 23:59:59.999）
  const endDate = new Date(value[1]);
  endDate.setHours(23, 59, 59, 999); // 关键修改
  form.value.endTime = endDate.getTime();
  // console.log("🐳-----form.value-----", form.value);
};

const value1 = ref([]);
const exportLoading = ref(false);
// 导出确认
const openProjectSubmission = () => {
  ElMessageBox.confirm("确认导出当前账单?", "", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    draggable: true
  })
    .then(() => {
      ProjectSubmission();
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "取消导出"
      });
    });
};
// 导出
const ProjectSubmission = async () => {
  if (exportLoading.value) return;
  exportLoading.value = true;
  // const loadingInstance = ElLoading.service({
  //   lock: true,
  //   text: "导出中...",
  //   background: "rgba(0, 0, 0, 0.7)"
  // });
  let paramsData = {};
  // Only include supported API parameters
  const supportedParams = [
    "startTime",
    "endTime",
    "recordType",
    "channelType",
    "feeType"
  ];
  for (const key of supportedParams) {
    if (form.value[key]) {
      paramsData[key] = form.value[key];
    }
  }
  // paramsData.organizationId = Id.value;
  console.log("🦄-----paramsData-----", paramsData);
  let { code, data } = await financialRecordExport(paramsData);
  console.log(data, "导出文件");
  // return;
  if (code == 200 && data?.id) {
    const task = await getAsyncTask(data?.id);
    console.log("🐬-----code, data-----", task.code, task.data);
    if (task.data.complete && task.data.success) {
      let resData = {};
      if (task.data.res) {
        resData = JSON.parse(task.data.res);
        console.log("🍪-----data-----", resData.url);
        downloadFileBySaveAs(resData.url, resData.fileName);
      }
      ElMessage.success("导出成功");
    } else {
      ElMessage.error(task.data.errMsg);
    }
    // const { id, complete, success } = data;
    // console.log("data: ", data);
    // console.log("导出结果", id, complete, success);
    // const { pause, resume, isActive } = useIntervalFn(() => {
    //   let params = {
    //     id
    //   };
    //   console.log("params: ", params);
    //   asyncTask(params).then(res => {
    //     console.log(res);
    //     const { code, data } = res;
    //     console.log("getAsyncTask- 异步任务", code, data);
    //     const { complete, success, errMsg } = data;
    //     if (code === 0 && complete) {
    //       pause();
    //       if (success) {
    //         ElMessage.success("导出成功");
    //         // loadingExport1.value = false;
    //         const { fileUrl, fileName } = data;
    //         downloadFileBySaveAs(fileUrl, fileName);
    //         exportLoading.value = false;
    //         loadingInstance.close();
    //       } else {
    //         ElMessage.error(errMsg);
    //         exportLoading.value = false;
    //         loadingInstance.close();
    //         // loadingExport1.value = false;
    //       }
    //     }
    //   });
    // }, 1000);
  } else {
    ElMessage.error("导出失败");
    // loadingInstance.close();
    // loadingExport1.value = false;
  }
  exportLoading.value = false;
};

const orderType = row => {
  let type = {
    WECHAT: "微信支付",
    ALPAY: "支付宝支付",
    PLATFORM: "平台",
    PUBLIC_ACCOUNT: "公账"
  };
  return type[row];
};
const feeTypeMap = row => {
  let type = {
    CLASS_HOUR: "课时费",
    INSURANCE: "保险费",
    MATERIAL: "材料费",
    SERVICE: "服务费"
  };
  return type[row];
};
// 清除数据
const clearEvt = () => {
  form.value.startTime = "";
  form.value.endTime = "";
};
const formatStatisticValue = value => {
  if (!value || isNaN(Number(value))) return value;
  const num = Number(value);
  if (num % 1 === 0) {
    return num.toString();
  }
  return num.toFixed(2);
};
</script>

<template>
  <div class="containers">
    <div class="banner">
      <div class="banner_top">
        <div class="details_container">
          <!-- <p>总收入 {{ financialData?.totalIncome || "--" }}</p>
          <p>
            余额
            {{
              financialData?.totalIncome - financialData?.totalExpenditure ||
              "--"
            }}
          </p> -->
          <!-- <p>可提现金额 {{ financialData?.availableBalance || "--" }}</p> -->
          <el-statistic
            title="总收入"
            :value="`￥${financialData?.totalIncome || '0'}`"
            :formatter="formatStatisticValue"
          />
          <el-statistic
            title="余额"
            :value="`￥${financialData?.balance || '0'}`"
            :formatter="formatStatisticValue"
          />
          <!-- <el-statistic
            title="可提现金额"
            :value="financialData?.availableBalance || '0'"
            :formatter="formatStatisticValue"
          /> -->
        </div>
        <div class="function_container">
          <el-button type="primary" @click="goSet">银行账户信息</el-button>
          <el-button
            type="primary"
            style="margin-right: auto"
            :loading="exportLoading"
            @click="openProjectSubmission"
          >
            导出账单
          </el-button>
        </div>
      </div>
      <div class="con_search">
        <el-form
          :model="form"
          label-width="70px"
          :inline="true"
          style="width: 100%"
        >
          <el-form-item label="支付时间">
            <el-date-picker
              v-model="value1"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              @change="timeChange"
              @clear="clearEvt()"
            />
          </el-form-item>

          <el-form-item label="记录类型">
            <el-select
              v-model="form.recordType"
              style="width: 120px"
              placeholder="请选择"
              :empty-values="[null, undefined]"
              :value-on-clear="null"
            >
              <el-option
                v-for="item in courseTypeoptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="渠道类型">
            <el-select
              v-model="form.channelType"
              style="width: 120px"
              placeholder="请选择"
              :empty-values="[null, undefined]"
              :value-on-clear="null"
            >
              <el-option
                v-for="item in channelTypeoptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="费用类型">
            <el-select
              v-model="form.feeType"
              style="width: 120px"
              placeholder="请选择"
              :empty-values="[null, undefined]"
              :value-on-clear="null"
            >
              <el-option
                v-for="item in feeTypeoptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item> -->
          <el-form-item label=" " style="float: right; margin-right: 0">
            <div class="flex">
              <el-button type="primary" @click="searchData"> 搜索 </el-button>
              <el-button @click="setData"> 重置 </el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="" style="height: 20px; width: 100%; background: #f5f5f5" />

    <div class="main">
      <el-scrollbar class="scrollbar" :style="{ height: tableHeight }">
        <div class="con_table">
          <el-table
            :data="tableData"
            :header-cell-style="{
              backgroundColor: '#fafafa',
              color: '#565353'
            }"
            :max-height="tableHeight"
          >
            <el-table-column prop="id" label="编号" min-width="120">
              <template #default="scope">
                <el-text class="w-300px mb-2" truncated>
                  {{ scope.row.id || "--" }}
                </el-text>
              </template>
            </el-table-column>
            <el-table-column
              width="200px"
              prop="transactionTime"
              label="交易时间"
            >
              <template #default="scope">
                <div>
                  {{
                    formatTime(
                      scope.row?.transactionTime,
                      "YYYY-MM-DD HH:mm"
                    ) || "--"
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="recordType" label="类型" align="left">
              <template #default="scope">
                <div>
                  {{
                    scope.row.recordType === "INCOME" ? "收入" : "支出" || "--"
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="channelType" label="支付渠道" align="left">
              <template #default="scope">
                <div>
                  {{ orderType(scope.row.channelType) || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="feeType" label="费用类型" align="left">
              <template #default="scope">
                <div>
                  {{ feeTypeMap(scope.row.feeType) || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="price" label="金额" align="left">
              <template #default="scope">
                <div>
                  {{
                    scope.row.price !== null && scope.row.price !== undefined
                      ? `￥${scope.row.price}`
                      : "--"
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="subOrdersId" label="关联订单" align="left">
              <template #default="scope">
                <div>
                  {{ scope.row.subOrdersId || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="remarks" label="备注" align="left">
              <template #default="scope">
                <div>
                  {{ scope.row.remarks || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="left">
              <template #default="scope">
                <div class="button">
                  <div class="btnse" @click="edit(scope.row)">关联订单</div>
                  <!-- <div class="btnse" @click="getId(scope.row.id)">账务</div> -->
                  <!-- <el-button
                  type="primary"
                  link
                  :style="{
                    color: scope.row.freeze === true ? 'red' : '#409EFF'
                  }"
                  @click="Freeze(scope.row)"
                >
                  {{ getButtonText(scope.row.freeze) }}
                </el-button> -->
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-scrollbar>
      <div class="con_pagination">
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="params.page"
          v-model:page-size="params.size"
          class="pagination"
          background
          :page-sizes="[15, 30, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="params.totalElements"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.scrollbar {
  background-color: #fff;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
}
.containers {
  box-sizing: border-box;
  width: calc(100% - 48px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  // padding: 24px;
  // background: #fff;

  .con_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: fit-content;
    margin-bottom: 24px;

    .titles {
      font-size: 20px;
      font-weight: bold;
      color: #606266;
    }
  }

  .con_search {
    display: flex;
    align-items: center;
    width: 100%;
    height: fit-content;
    padding: 24px 20px 0 20px;
    background: #fff;
    overflow: auto;
    .flex {
      float: right;
      gap: 1.5vw;
    }
  }

  .main {
    background: #fff;
    padding: 20px;
    box-sizing: border-box;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: hidden;
  }

  .con_table {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .button {
      // display: flex;
      // justify-content: space-around;
      .btnse {
        color: #409eff;
        cursor: pointer;
        // padding: 0 5px;
      }
    }
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    margin-top: 20px;

    :deep(.el-pagination) {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-end;
      row-gap: 10px;

      .el-pagination__sizes,
      .el-pagination__jump {
        margin-bottom: 0;
      }
    }
  }
}
.banner {
  .banner_top {
    width: 100%;
    background: #ffff;
    display: flex;
    justify-content: flex-start;
    padding: 20px;
    margin-bottom: 20px;

    .details_container {
      display: flex;
      min-width: 50vw;
      align-items: center;
      gap: 8vw;
    }
    .function_container {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5vw;
      margin-left: auto;
    }
  }
}
</style>
