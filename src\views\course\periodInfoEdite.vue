<script setup>
import { ref, reactive, onMounted, onActivated } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  complexId,
  courseTypeFind,
  coursePeriodEdite,
  leaderLecturerFind,
  coursePeriodFind
} from "@/api/course.js";
import { findAllCourseType } from "@/api/period.js";
import { requestTo } from "@/utils/http/tool";
import { dayjs, ElIcon, ElMessage, ElMessageBox } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import { uploadFile, isOverSizeLimit } from "@/utils/upload/upload.js";
import { courseStore } from "@/store/modules/course.js";
import { deepClone, isEmpty } from "@iceywu/utils";
const useCourseStore = courseStore();
const { courseInfo } = useCourseStore;
const router = useRouter();
const route = useRoute();
const ruleFormRef = ref();
const ruleForm = reactive({
  name: "",
  courseTypeId: "",
  complexId: "",
  minPeopleNumber: "",
  leaderIds: "",
  maxPeopleNumber: "",
  lecturerIds: "",
  cover: [],
  openTime: "",
  colseTime: "",
  openDate: "",
  signUpDeadline: ""
});
// 人数限制校验
const minPeoplePass = (rule, value, callback) => {
  if (value <= 0) {
    callback(new Error("请输入正确的人数限制"));
  } else {
    callback();
  }
};
const maxPeoplePass = (rule, value, callback) => {
  console.log("🎉-----value-----", value);

  console.log("🐬-----ruleForm.minPeopleNumber-----", ruleForm.minPeopleNumber);

  if (value <= 0) {
    callback(new Error("请输入正确的人数限制"));
  } else if (value < Number(ruleForm.minPeopleNumber)) {
    callback(new Error("人数上限不能低于人数下限"));
  } else {
    callback();
  }
};
const rules = reactive({
  name: [{ required: true, message: "请输入课程名称", trigger: "blur" }],
  courseTypeId: [
    { required: true, message: "请选择课程类型", trigger: "blur" }
  ],
  complexId: [{ required: true, message: "请选择实践点", trigger: "blur" }],
  leaderIds: [{ required: true, message: "请选择领队", trigger: "blur" }],
  lecturerIds: [{ required: true, message: "请选择讲师", trigger: "blur" }]
  // minPeopleNumber: [{ validator: minPeoplePass, trigger: "blur" }],
  // maxPeopleNumber: [{ validator: maxPeoplePass, trigger: "blur" }]
});

const formData = ref([
  {
    label: "课期名",
    type: "input",
    typeInput: "text",
    prop: "name",
    check: true,
    placeholder: "请输入课程名",
    width: "240px"
  },
  {
    label: "课程类型",
    type: "cascader",
    prop: "courseTypeId",
    check: true,
    placeholder: "请选择课程类型",
    width: "200px",
    options: []
  },
  {
    label: "实践点",
    type: "select",
    prop: "complexId",
    check: true,
    placeholder: "请选择实践点",
    width: "200px",
    options: [],
    text: "新建实践点"
  },
  // {
  //   label: "人数下限",
  //   type: "input",
  //   typeInput: "number",
  //   prop: "minPeopleNumber",
  //   check: false,
  //   placeholder: "请输入人数下限",
  //   width: "240px",
  //   min: 0
  // },
  {
    label: "人数上限",
    type: "input",
    typeInput: "number",
    prop: "maxPeopleNumber",
    check: false,
    placeholder: "请输入人数上限",
    width: "240px",
    min: 0
  },
  {
    label: "领队",
    type: "selectMultiple",
    prop: "leaderIds",
    check: true,
    placeholder: "请选择领队",
    width: "200px",
    options: [],
    isViewMore: false,
    text: "新建领队"
  },
  {
    label: "讲师",
    type: "selectMultiple",
    prop: "lecturerIds",
    check: true,
    placeholder: "请选择讲师",
    width: "200px",
    options: [],
    isViewMore: false,
    text: "新建讲师"
  },
  {
    label: "开课日期",
    type: "pickerDate",
    prop: "openDate",
    timeType: "date",
    check: true,
    placeholder: "请选择日期",
    width: "400px",
    options: []
  },
  {
    label: "停止报名日期",
    type: "pickerDate",
    prop: "signUpDeadline",
    timeType: "date",
    check: true,
    placeholder: "请选择日期",
    width: "400px",
    options: []
  },
  {
    label: "开课时间",
    type: "pickerTime",
    timeType: "date",
    prop: "openTime",
    check: true,
    placeholder: "请选择开课时间",
    width: "400px",
    options: []
  },
  {
    label: "停止报名时间",
    type: "pickerTime",
    prop: "colseTime",
    timeType: "date",
    check: true,
    placeholder: "请选择时间",
    width: "400px",
    options: []
  }
]);

const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const handlePictureCardPreview = uploadFile => {
  dialogImageUrl.value = uploadFile.url;
  dialogVisible.value = true;
};
const fileList = ref([]);
// 图片上传
const beforeUpload = async file => {
  const fileType = file.type?.split("/")[0];
  if (fileType == "image") {
    let isSize = isOverSizeLimit(file, 10);
    if (isSize.valid) {
      try {
        await uploadFile(
          file,
          progress => {
            // 构造用于 el-upload 展示的文件对象
            const currentFile = {
              name: file.name,
              uid: file.uid,
              status: progress.status || "uploading",
              percentage: progress.percent || 0
            };

            // 上传成功，补充 url 字段
            if (progress.status === "success" && progress.data?.url) {
              currentFile.url = progress.data.url;

              // 更新 ruleForm.cover，确保无 Proxy
              if (progress.data.fileIdentifier) {
                ruleForm.cover = ruleForm.cover.filter(
                  item => item.uid !== file.uid
                );
                ruleForm.cover.push({
                  fileIdentifier: progress.data.fileIdentifier,
                  fileType: "PHOTO",
                  uid: file.uid
                });
              }
            }

            // 失败时移除，成功/上传中则更新
            fileList.value = fileList.value.filter(f => f.uid !== file.uid);
            if (progress.status === "fail") {
              ElMessage.error(progress.errMessage || "上传失败，请重试");
            } else {
              // 用浅拷贝，避免 Proxy
              fileList.value = [...fileList.value, { ...currentFile }];
            }
          },
          ["image"]
        );
      } catch (error) {
        ElMessage.error(error.message || "上传过程中发生错误");
        fileList.value = fileList.value.filter(f => f.uid !== file.uid);
      }
      // let { data, code } = await uploadFile(file);
      // console.log("🌈-----data-----", data);
      // if (code === 200) {
      //   ruleForm.cover.push({
      //     fileIdentifier: data.fileIdentifier,
      //     fileType: "PHOTO",
      //     uid: file.uid,
      //     url: data.url,
      //     fileName: data.fileName
      //   });
      // }
    } else {
      ElMessage.error(isSize.message);
    }
    console.log("🍪----- ruleForm.2-----", ruleForm.cover);
  } else {
    ElMessage.error("请上传图片");
    return false;
  }
};
//删除图片视频
const handleRemove = (uploadFile, uploadFiles) => {
  // console.log("🌈-----uploadFile-----", uploadFile);
  // console.log("🐠-----ruleForm.cover -----", ruleForm.cover);
  ruleForm.cover = ruleForm.cover.filter(it => it.uid !== uploadFile.uid);
};
const courseTypeChange = val => {
  ruleForm.courseTypeId = val[val.length - 1];
};
const submitLoading = ref(false);
const submitForm = async formEl => {
  if (!formEl) return;
  if (submitLoading.value) return;
  submitLoading.value = true;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      const times =
        dayjs(ruleForm.openDate).format("YYYY-MM-DD") +
        "" +
        dayjs(ruleForm.openTime).format("HH:mm:ss");
      ruleForm.openTime = dayjs(times).valueOf();
      if (dayjs(ruleForm.openTime) < dayjs().valueOf()) {
        ElMessage.error("开课时间不能早于当前时间");
        submitLoading.value = false;
        return;
      }
      const times1 =
        dayjs(ruleForm.signUpDeadline).format("YYYY-MM-DD") +
        "" +
        dayjs(ruleForm.colseTime).format("HH:mm:ss");
      ruleForm.signUpDeadline = dayjs(times1).valueOf();

      ruleForm.lecturerIds = ruleForm.lecturerIds.map(it => {
        if (it.value && it.value !== it) {
          return it.value;
        } else {
          return it;
        }
      });

      ruleForm.leaderIds = ruleForm.leaderIds.map(it => {
        if (it.value && it.value !== it) {
          return it.value;
        } else {
          return it;
        }
      });
      const params = { ...ruleForm };
      params.id = Number(route.query.periodId);
      delete params.colseTime;
      delete params.openDate;
      if (!params?.cover?.length) {
        delete params?.cover;
      } else {
        params.cover = ruleForm.cover.map((it, index) => {
          return {
            sortOrder: index + 1,
            fileIdentifier: it.fileIdentifier,
            fileType: it.fileType
          };
        });
      }
      const operateLog = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `编辑了"${ruleForm.name}"课期`
        // operatorTarget: form.value.name,
      };
      // console.log("🍭params------------------------------>", params);
      let { code, data, msg } = await coursePeriodEdite(params, operateLog);
      if (code === 200) {
        router.replace({
          path: "/course/courseDetails/currentDetails",
          query: {
            periodId: route.query.periodId,
            courseId: route.query.courseId
          }
        });
        ElMessage.success("编辑成功");
        useCourseStore.saveLeaderInfo([]);
        useCourseStore.saveLecturerInfo([]);
        useCourseStore.saveBaseInfo({});
        clearPeriodEditSpecificStoreData(); // 成功提交后清除特定编辑页数据
      } else {
        // let text = "编辑失败";
        if (code === 30040) {
          ElMessage.error(`编辑失败，${msg},请去调整行程`);
        } else {
          ElMessage.error(`编辑失败，${msg}`);
        }
      }
    } else {
      console.log("error submit!", fields);
    }
    submitLoading.value = false;
  });
};
// 取消
const cancelEvt = () => {
  // 清空store中的数据
  useCourseStore.saveLeaderInfo([]);
  useCourseStore.saveLecturerInfo([]);
  useCourseStore.saveBaseInfo({});

  router.replace({
    path: "/course/courseDetails/currentDetails",
    query: { periodId: route.query.periodId, courseId: route.query.courseId }
  });
};
// 实践点查询不分页
const complexIdApi = async () => {
  let [err, res] = await requestTo(complexId());
  if (res) {
    formData.value[2].options = res.map(it => {
      return {
        ...it,
        label: it.name,
        value: it.id
      };
    });
  }
};
//课程分类查询不分页
const courseTypeFindApi = async () => {
  let [err, res] = await requestTo(findAllCourseType());
  if (res) {
    formData.value[1].options = transformArray(res);
  }
};
function transformArray(inputArray) {
  return inputArray.map(item => {
    const newItem = {
      value: item.id,
      label: item.name
    };

    // 如果有 children，则递归转换
    if (item.children && item.children.length > 0) {
      newItem.children = transformArray(item.children);
    }

    return newItem;
  });
}
// 领队讲师查询
const leaderFindApi = async type => {
  const params = {
    roleId: type
  };
  let [err, res] = await requestTo(leaderLecturerFind(params));
  if (res) {
    if (type == 2) {
      formData.value[5].options = res.map(it => {
        return {
          label: it.name,
          value: it.id
        };
      });
    } else {
      formData.value[4].options = res.map(it => {
        return {
          label: it.name,
          value: it.id
        };
      });
    }
  } else {
    console.log("🌵-----err-----", err);
  }
};
// 从store中获取并更新表单数据
const updateFormFromStore = () => {
  if (route.query.create === "create") {
    // 课期名
    if (useCourseStore.periodName) {
      ruleForm.name = useCourseStore.periodName;
    }
    // 课程类型ID
    if (useCourseStore.periodCourseTypeId) {
      ruleForm.courseTypeId = useCourseStore.periodCourseTypeId;
    }
    if (useCourseStore.baseInfo && !isEmpty(useCourseStore.baseInfo)) {
      ruleForm.complexId = useCourseStore.baseInfo?.id;
    }
    // 人数下限
    if (
      useCourseStore.periodMinPeopleNumber !== undefined &&
      useCourseStore.periodMinPeopleNumber !== null &&
      useCourseStore.periodMinPeopleNumber !== ""
    ) {
      ruleForm.minPeopleNumber = useCourseStore.periodMinPeopleNumber;
    }
    // 人数上限
    if (
      useCourseStore.periodMaxPeopleNumber !== undefined &&
      useCourseStore.periodMaxPeopleNumber !== null &&
      useCourseStore.periodMaxPeopleNumber !== ""
    ) {
      ruleForm.maxPeopleNumber = useCourseStore.periodMaxPeopleNumber;
    }
    // 领队
    if (useCourseStore.leaderInfo && useCourseStore.leaderInfo.length) {
      ruleForm.leaderIds = useCourseStore.leaderInfo?.map(it => it.id);
    }
    // 讲师
    if (useCourseStore.lecturerInfo && useCourseStore.lecturerInfo.length) {
      ruleForm.lecturerIds = useCourseStore.lecturerInfo?.map(it => it.id);
    }
    // 开课日期
    if (useCourseStore.periodOpenDate) {
      ruleForm.openDate = useCourseStore.periodOpenDate;
    }
    // 开课时间
    if (useCourseStore.periodOpenTimeValue) {
      ruleForm.openTime = useCourseStore.periodOpenTimeValue;
    }
    // 停止报名日期
    if (useCourseStore.periodSignUpDeadlineDate) {
      ruleForm.signUpDeadline = useCourseStore.periodSignUpDeadlineDate;
    }
    // 停止报名时间
    if (useCourseStore.periodColseTimeValue) {
      ruleForm.colseTime = useCourseStore.periodColseTimeValue;
    }

    // 封面图
    if (useCourseStore.coverInfo && useCourseStore.coverInfo.length > 0) {
      ruleForm.cover = useCourseStore.coverInfo;
      fileList.value = useCourseStore.coverInfo;
    } else {
      ruleForm.cover = [];
      fileList.value = [];
    }
  }
};

// 根据ID更新选择框的选项标签
const updateSelectOptions = () => {
  // 领队和讲师选项更新
  if (
    ruleForm.leaderIds &&
    formData.value[4] &&
    formData.value[4].options.length > 0
  ) {
    const leaderIdMap = {};
    formData.value[4].options.forEach(opt => {
      leaderIdMap[opt.value] = opt;
    });

    // 处理可能是对象的情况
    const processedLeaderIds = ruleForm.leaderIds.map(item => {
      if (
        typeof item === "object" &&
        item !== null &&
        item.value !== undefined
      ) {
        return item.value;
      } else if (
        typeof item === "object" &&
        item !== null &&
        item.id !== undefined
      ) {
        return item.id;
      }
      return item;
    });

    // 保留有效的ID
    ruleForm.leaderIds = processedLeaderIds.filter(id => leaderIdMap[id]);
  }

  if (
    ruleForm.lecturerIds &&
    formData.value[5] &&
    formData.value[5].options.length > 0
  ) {
    const lecturerIdMap = {};
    formData.value[5].options.forEach(opt => {
      lecturerIdMap[opt.value] = opt;
    });

    // 处理可能是对象的情况
    const processedLecturerIds = ruleForm.lecturerIds.map(item => {
      if (
        typeof item === "object" &&
        item !== null &&
        item.value !== undefined
      ) {
        return item.value;
      } else if (
        typeof item === "object" &&
        item !== null &&
        item.id !== undefined
      ) {
        return item.id;
      }
      return item;
    });

    // 保留有效的ID
    ruleForm.lecturerIds = processedLecturerIds.filter(id => lecturerIdMap[id]);
  }
};

// 复制创建查询课期详情
const getCoursePeriodFind = async () => {
  let [err, res] = await requestTo(
    coursePeriodFind({ id: route.query.periodId })
  );
  if (res) {
    // console.log("🐬-----res1111--33---", res);

    ruleForm.name = res?.name;
    ruleForm.courseTypeId = res?.courseType?.id;
    if (route.query.create === "create" && !isEmpty(useCourseStore.baseInfo)) {
      ruleForm.complexId = useCourseStore.baseInfo?.id;
    } else {
      ruleForm.complexId = res?.complex?.id;
    }
    if (
      route.query.create === "create" &&
      !isEmpty(useCourseStore.lecturerInfo)
    ) {
      ruleForm.lecturerIds = useCourseStore.lecturerInfo?.map(it => it.id);
    } else {
      ruleForm.lecturerIds = res?.lecturers?.map(item => {
        return { label: item.name, value: item.id };
      });
    }
    if (
      route.query.create === "create" &&
      !isEmpty(useCourseStore.leaderInfo)
    ) {
      ruleForm.leaderIds = useCourseStore.leaderInfo?.map(it => it.id);
    } else {
      ruleForm.leaderIds = res?.leaders?.map(item => {
        return { label: item.name, value: item.id };
      });
    }
    ruleForm.minPeopleNumber = res?.minPeopleNumber;
    ruleForm.maxPeopleNumber = res?.maxPeopleNumber;

    ruleForm.openDate = res?.openTime;
    ruleForm.openTime = res?.openTime;
    ruleForm.signUpDeadline = res?.signUpDeadline;
    ruleForm.colseTime = res?.signUpDeadline;

    if (res.cover?.length) {
      res.cover.map(item => {
        ruleForm.cover.push(item.uploadFile);
        fileList.value.push(item.uploadFile);

        // fileList.value.push(deepClone(item.uploadFile));
        // fileList.value.push(item.uploadFile);
      });
    }
  } else {
    console.log("🐳-----err-----", err);
  }
};
const isVisible = ref(false);
// 保存当前表单数据到store
const saveCurrentFormToStore = () => {
  // 保存课期名
  useCourseStore.savePeriodName(ruleForm.name);
  // 保存课程类型ID
  useCourseStore.savePeriodCourseTypeId(ruleForm.courseTypeId);
  // 保存实践点信息
  if (ruleForm.complexId) {
    useCourseStore.saveBaseInfo({ id: ruleForm.complexId });
  }

  // 保存领队信息
  if (ruleForm.leaderIds && ruleForm.leaderIds.length) {
    let ids = ruleForm.leaderIds.map(it => {
      if (typeof it === "object" && it !== null) {
        return { id: it.value || it.id };
      }
      return { id: it };
    });
    useCourseStore.saveLeaderInfo(ids);
  }

  // 保存讲师信息
  if (ruleForm.lecturerIds && ruleForm.lecturerIds.length) {
    let ids = ruleForm.lecturerIds.map(it => {
      if (typeof it === "object" && it !== null) {
        return { id: it.value || it.id };
      }
      return { id: it };
    });
    useCourseStore.saveLecturerInfo(ids);
  } else {
    useCourseStore.saveLecturerInfo([]); // 如果为空则清空store中的信息
  }

  // 保存人数下限和上限
  useCourseStore.savePeriodMinPeopleNumber(ruleForm.minPeopleNumber);
  useCourseStore.savePeriodMaxPeopleNumber(ruleForm.maxPeopleNumber);

  // 保存开课日期、开课时间、停止报名日期、停止报名时间
  useCourseStore.savePeriodOpenDate(ruleForm.openDate);
  useCourseStore.savePeriodOpenTimeValue(ruleForm.openTime);
  useCourseStore.savePeriodSignUpDeadlineDate(ruleForm.signUpDeadline);
  useCourseStore.savePeriodColseTimeValue(ruleForm.colseTime);

  // 保存封面图信息
  useCourseStore.saveCoverInfo(ruleForm.cover);
};

// 清除为编辑页暂存的特定数据
const clearPeriodEditSpecificStoreData = () => {
  useCourseStore.savePeriodName("");
  useCourseStore.savePeriodCourseTypeId("");
  useCourseStore.savePeriodMinPeopleNumber("");
  useCourseStore.savePeriodMaxPeopleNumber("");
  useCourseStore.savePeriodOpenDate(null);
  useCourseStore.savePeriodOpenTimeValue(null);
  useCourseStore.savePeriodSignUpDeadlineDate(null);
  useCourseStore.savePeriodColseTimeValue(null);
  // baseInfo, leaderInfo, lecturerInfo 会在afterEach或cancelEvt中被统一处理，这里不再重复

  // 清除封面图信息
  useCourseStore.saveCoverInfo([]);
};

// 点击新建
const clickEvt = item => {
  // 无论点击哪个类型的新建按钮，都保存当前表单数据
  saveCurrentFormToStore();

  if (item.text === "新建实践点") {
    router.push({
      path: "/course/periodEdite/baseAdd",
      query: {
        periodId: route.query.periodId,
        courseId: route.query.courseId,
        create: "create" // 添加create参数
      }
    });
  } else if (item.text === "新建领队") {
    router.push({
      path: "/course/periodEdite/leaderCreate",
      query: {
        text: "leader",
        roleId: 3,
        periodId: route.query.periodId,
        courseId: route.query.courseId,
        create: "create" // 添加create参数
      }
    });
  } else if (item.text === "新建讲师") {
    router.push({
      path: "/course/periodEdite/lecturerCreate",
      query: {
        text: "teacher",
        roleId: 2,
        periodId: route.query.periodId,
        courseId: route.query.courseId,
        create: "create" // 添加create参数
      }
    });
  }
};
router.afterEach((to, from) => {
  if (to.path === "/course/courseDetails/currentDetails") {
    useCourseStore.saveLeaderInfo([]);
    useCourseStore.saveLecturerInfo([]);
    useCourseStore.saveBaseInfo({});
    clearPeriodEditSpecificStoreData(); // 导航离开时清除特定编辑页数据
  }
});
onMounted(async () => {
  // getCoursePeriodFind(); // 移动到 onActivated 或根据逻辑调整，避免重复加载
  await complexIdApi();
  await courseTypeFindApi();
  await leaderFindApi(3);
  await leaderFindApi(2);
  // 从store获取表单数据
  updateFormFromStore();

  // 当选项加载完成后，再从store中更新表单
  updateSelectOptions();

  // 如果不是从新建子页面返回，则加载详情
  if (route.query.create !== "create") {
    await getCoursePeriodFind();
  }
});

// 当组件从缓存中激活时更新数据
onActivated(async () => {
  // 先重新加载领队和讲师数据，确保显示姓名而不是ID
  await Promise.all([
    leaderFindApi(3), // 领队
    leaderFindApi(2) // 讲师
  ]);

  // 当选项加载完成后，再从store中更新表单
  updateFormFromStore();

  // 更新选项匹配，确保选中项存在于选项中
  updateSelectOptions();

  // 如果不是从新建子页面返回，则加载详情
  if (route.query.create !== "create") {
    getCoursePeriodFind();
  }
});
</script>

<template>
  <div class="localendAccount-add">
    <div class="localendAccount-container">
      <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules">
        <el-descriptions title="" :column="2" border>
          <el-descriptions-item
            v-for="(item, index) in formData"
            :key="index"
            label-align="center"
            :span="item.label === '课程名' ? 2 : ''"
          >
            <template #label>
              <span v-if="item.check" class="star">*</span>{{ item.label }}
            </template>
            <el-form-item
              :prop="item.prop"
              :inline-message="item.check"
              style="margin-bottom: 0"
              :show-message="true"
              error-placement="right"
            >
              <!-- input输入 -->
              <template v-if="item.type === 'input'">
                <el-input
                  v-model="ruleForm[item.prop]"
                  :placeholder="item.placeholder"
                  :style="{ width: item.width }"
                  :type="item.typeInput"
                  :min="item.min"
                />
              </template>
              <!-- 多选 -->
              <template v-if="item.type === 'selectMultiple'">
                <el-select
                  v-model="ruleForm[item.prop]"
                  multiple
                  :placeholder="item.placeholder"
                  style="width: 240px"
                >
                  <el-option
                    v-for="it in item.options"
                    :key="it.value"
                    :label="it.label"
                    :value="it.value"
                  />
                </el-select>
                <el-button
                  type="primary"
                  style="margin-left: 10px"
                  @click="clickEvt(item)"
                >
                  {{ item.text }}
                </el-button>
              </template>
              <!-- 时间日期选择 -->
              <template v-if="item.type === 'pickerDate'">
                <el-date-picker
                  v-model="ruleForm[item.prop]"
                  :type="item.timeType"
                  :placeholder="item.placeholder"
                  style="width: 240px"
                  value-format="x"
                />
              </template>
              <template v-if="item.type === 'pickerTime'">
                <el-time-picker
                  v-model="ruleForm[item.prop]"
                  style="width: 240px"
                  :placeholder="item.placeholder"
                  value-format="x"
                />
              </template>
              <!-- 单选 -->
              <template v-if="item.type === 'select'">
                <el-select
                  v-model="ruleForm[item.prop]"
                  :placeholder="item.placeholder"
                  style="width: 240px"
                >
                  <el-option
                    v-for="it in item.options"
                    :key="it.value"
                    :label="it.label"
                    :value="it.value"
                  />
                </el-select>
                <el-button
                  type="primary"
                  style="margin-left: 10px"
                  @click="clickEvt(item)"
                >
                  {{ item.text }}
                </el-button>
              </template>
              <!-- 级联选择 -->
              <template v-if="item.type === 'cascader'">
                <el-cascader
                  v-model="ruleForm[item.prop]"
                  :options="item.options"
                  :show-all-levels="false"
                  style="width: 240px"
                  @change="courseTypeChange"
                />
              </template>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>

        <div class="upload">封面图</div>
        <el-upload
          v-model:file-list="fileList"
          action="#"
          :http-request="() => {}"
          list-type="picture-card"
          :before-upload="beforeUpload"
          :on-remove="handleRemove"
          :on-preview="handlePictureCardPreview"
        >
          <el-icon><Plus /></el-icon>
        </el-upload>
        <el-dialog v-model="dialogVisible">
          <img class="w-full" :src="dialogImageUrl" alt="Preview Image">
        </el-dialog>
        <div class="upload_text">
          支持上传jpg、jpeg、png、gif、bmp、webp格式，支持多张图片上传，图片最佳尺寸：750*750px，单张图片大小不超过10MB
        </div>
      </el-form>
      <div class="buttons">
        <el-button @click="cancelEvt">取消</el-button>
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="submitForm(ruleFormRef)"
        >
          {{ "保存" }}
        </el-button>
        <!-- <div class="create" @click="submitForm(ruleFormRef)">
          {{ "保存" }}
        </div> -->
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.localendAccount-add {
  box-sizing: border-box;
  // padding: 22px 30px;
  font-size: 14px;
  //   font-family: PingFangSC-regular;
  color: #101010;
  position: relative;
  // background-color: #fff;

  .title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .title-text {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 76px;
      height: 28px;
      margin-right: 10px;
      cursor: pointer;
      background-color: #f6e5d4;
      border: 1px solid #ff8c19;
    }

    .line {
      margin-right: 20px;
    }
  }

  .localendAccount-container {
    box-sizing: border-box;
    width: 100%;
    height: calc(100vh - 100px);
    padding: 20px 20px;
    background-color: #fff;

    .upload {
      margin: 15px 0;
    }

    .buttons {
      display: flex;
      // justify-content: space-between;
      justify-content: flex-end;
      // align-items: flex-end;
      width: 100%;
      // margin: 0 auto;
      // margin-top: 10vh;
      position: absolute;
      bottom: 30px;
      right: 20px;

      .create {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100px;
        height: 36px;
        color: rgb(255 255 255 / 100%);
        cursor: pointer;
        background-color: rgb(64 149 229 / 100%);
        border-radius: 6px;
      }
    }

    .star {
      margin-right: 3px;
      color: red;
    }
  }
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  width: 240px;
  background: #e1f5ff;
}
.upload_text {
  font-size: 12px;
  position: relative;
  top: 5px;
  color: #8c939d;
}
</style>
