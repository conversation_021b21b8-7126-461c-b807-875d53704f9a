import { $t } from "@/plugins/i18n";
import { vueflow } from "@/router/enums.js";

import { inCodesList, reCodesList } from "@/router/accidCode.js";
import InstitutionIcon from "@/assets/home/<USER>";
import InstitutionIconActive from "@/assets/home/<USER>";
export default {
  path: "/institution",
  redirect: "/institution/institutionManage",
  meta: {
    icon: "ri:information-line",
    imgIcon: InstitutionIcon,
    imgIconActive: InstitutionIconActive,
    // showLink: false,
    title: "机构",
    rank: vueflow,
    idCode: inCodesList.baseCode
  },
  children: [
    {
      path: "/institution/institution",
      name: "institution",
      redirect: "/institution/institutionManage",
      // component: () => import("@/views/institution/institutionManage.vue"),
      meta: {
        title: "机构管理"
        // idCode: inCodesList.organizationalManagement
      },
      children: [
        {
          path: "/institution/institutionManage",
          name: "institutionManage",
          component: () => import("@/views/institution/institutionManage.vue"),
          meta: {
            title: "机构管理",
            idCode: inCodesList.institutionManage
          }
        },
        {
          path: "/institution/institutionEdit",
          name: "institutionEdit",
          component: () => import("@/views/institution/institutionEdit.vue"),
          meta: {
            title: "机构编辑",
            idCode: reCodesList.baseEdit,
            showLink: false
          }
        }
      ]
    },
    {
      path: "/institution/base",
      name: "base",
      redirect: "/institution/baseManage",
      // component: () => import("@/views/institution/baseManage.vue"),
      meta: {
        title: "实践点管理",
        idCode: inCodesList.baseManage
      },
      children: [
        {
          path: "/institution/baseManage",
          name: "BaseManage",
          component: () => import("@/views/institution/baseManage.vue"),
          meta: {
            title: "实践点管理",
            idCode: inCodesList.baseManage,
            keepAlive: true
          }
        },
        {
          path: "/institution/baseAdd",
          name: "baseAdd",
          component: () => import("@/views/institution/baseAdd.vue"),
          meta: {
            title: "新建实践点",
            idCode: reCodesList.baseAdd,
            showLink: false
          }
        },
        {
          path: "/institution/baseDetails",
          name: "baseDetails",
          component: () => import("@/views/institution/baseDetails.vue"),
          meta: {
            title: "实践点详情",
            idCode: reCodesList.baseDetails,
            showLink: false
          },
          children: [
            {
              path: "/institution/baseEdit",
              name: "baseEdit",
              component: () => import("@/views/institution/baseEdit.vue"),
              meta: {
                title: "实践点编辑",
                idCode: reCodesList.baseEdit,
                showLink: false
              }
            }
          ]
        }
      ]
    },
    {
      path: "/institution/logManage",
      name: "logManage",
      component: () => import("@/views/institution/logManage.vue"),
      meta: {
        title: "日志管理",
        idCode: inCodesList.logManage
      }
    }
  ]
};
