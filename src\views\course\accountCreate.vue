<script setup>
import { ref, onBeforeMount, onUnmounted } from "vue";
import AccountCreate from "@/views/account/accountCreate.vue";
import RoleAdd from "./components/roleList.vue";
import { useRoute, useRouter } from "vue-router";
import { ElTabs, ElTabPane } from "element-plus";
import { courseStore } from "@/store/modules/course.js";
const useCourseStore = courseStore();
const route = useRoute();
const router = useRouter();
const accountShow = ref(true);
const tabTitle = ref([]);
const activeTab = ref("0");

const handleTabChange = tabName => {
  accountShow.value = tabName === "0";
};

onBeforeMount(() => {
  if (route.query.roleId === "2") {
    tabTitle.value = [
      { id: "0", name: "新建账号" },
      { id: "1", name: "现有账号赋予讲师角色" }
    ];
  } else {
    tabTitle.value = [
      { id: "0", name: "新建账号" },
      { id: "1", name: "现有账号赋予领队角色" }
    ];
  }
});
onUnmounted(() => {
  useCourseStore.saveCreateInfo("create");
});
</script>

<template>
  <div class="role-add">
    <!-- tab切换 -->
    <el-tabs
      v-model="activeTab"
      class="tabs-container"
      @tab-change="handleTabChange"
    >
      <el-tab-pane
        v-for="item in tabTitle"
        :key="item.id"
        :label="item.name"
        :name="item.id.toString()"
      />
    </el-tabs>

    <!-- 新建账号 -->
    <AccountCreate
      v-show="accountShow"
      :courseRoleId="Number(route.query.roleId)"
    />
    <!-- 赋予角色 -->
    <RoleAdd v-show="!accountShow" />
  </div>
</template>

<style lang="scss" scoped>
.role-add {
  box-sizing: border-box;
  background-color: #fff;
  padding: 20px;
  width: 100%;
  height: 100%;

  .tabs-container {
    margin-bottom: 20px;
  }
}
</style>
