<script setup>
import { onMounted, ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { courseStore } from "@/store/modules/course.js";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
const props = defineProps({
  infoShow: {
    type: String,
    default: "课期介绍"
  },
  btnText: {
    type: String,
    default: "编辑介绍"
  },
  showContent: {
    type: String,
    default: ""
  }
});
const useCourseStore = courseStore();
const router = useRouter();
const route = useRoute();

const refName = ref("行程安排");
// 编辑内容按钮
const editeEvt = () => {
  router.push({
    path: "/course/currentDetails/introductionEdite",
    query: {
      periodId: route.query.periodId,
      infoShow: props.infoShow,
      courseId: route.query.courseId
    }
  });
};
</script>

<template>
  <div class="course-introduction">
    <div v-if="showContent" class="content">
      <div v-html="showContent" />
    </div>
    <el-empty v-else description="暂无数据" />
    <el-button v-if="false" type="primary" class="editeBtn" @click="editeEvt">
      {{ btnText }}
    </el-button>
  </div>
</template>

<style lang="scss" scoped>
.course-introduction {
  height: 100%;
  position: relative;
  .editeBtn {
    display: flex;
    position: absolute;
    bottom: 0;
    left: 0;
  }
  .content {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    height: calc(100% - 52px);
    background: #f5f7fa;
    overflow-y: auto;
    // scrollbar-width: none;
    // -ms-overflow-style: none;

    :deep(img) {
      vertical-align: middle;
      display: block;
    }

    // 添加表格样式
    :deep(table) {
      border-collapse: collapse;
      // width: 100%;
      // margin: 10px 0;

      td,
      th {
        border: 1px solid #a7a6a6;
        // padding: 8px;
        min-width: 50px;
        text-align: center;
      }
    }
  }
  // .content::-webkit-scrollbar {
  //   display: none;
  // }
  .content::-webkit-scrollbar-track {
    background: #f1f1f1; /* 滚动条轨道背景 */
  }

  .content::-webkit-scrollbar-thumb {
    background: #afafaf; /* 滚动条滑块颜色 */
  }

  .content::-webkit-scrollbar-thumb:hover {
    background: #7c7c7c; /* 滚动条滑块悬停时的颜色 */
  }
}
</style>
