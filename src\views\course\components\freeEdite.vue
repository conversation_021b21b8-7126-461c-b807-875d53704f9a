<script setup>
import { ref, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { freeCreateOrUpdate } from "@/api/period.js";
import { requestTo } from "@/utils/http/tool";
import { to } from "@iceywu/utils";
import { ElMessage, ElMessageBox } from "element-plus";
import { courseStore } from "@/store/modules/course.js";
import DescriptionList from "./descriptionList.vue";
const router = useRouter();
const route = useRoute();
const refName = ref("fef");
const textarea = ref("");
const params = ref({});
const operateLog = ref({});
const periodName = ref("");
const submitLoading = ref(false);
const periodNameEvt = val => {
  periodName.value = val;
};
// 保存
const saveEvt = async () => {
  if (submitLoading.value) return;
  submitLoading.value = true;
  if (route.query.type === "free") {
    params.value = {
      coursePeriodId: Number(route.query.periodId),
      feeDescription: textarea.value
    };
    operateLog.value = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: `编辑了“${periodName.value}”课期中的费用说明`
    };
  } else {
    params.value = {
      coursePeriodId: Number(route.query.periodId),
      refundPolicy: textarea.value
    };
    operateLog.value = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: `编辑了“${periodName.value}”课期中的退款政策`
    };
  }

  const [err, result] = await to(
    freeCreateOrUpdate(params.value, operateLog.value)
  );
  //   console.log("🦄result------------------------------>", result);
  if (result?.code === 200) {
    ElMessage.success("编辑成功");
    router.replace({
      path: "/course/courseDetails/currentDetails",
      query: { infoShow: "价格设置", periodId: route.query.periodId }
    });
  } else {
    ElMessage.error("编辑失败");
  }
  submitLoading.value = false;
};
onMounted(() => {
  if (route.query.type === "free" && courseStore().freeInfo) {
    textarea.value = courseStore().freeInfo;
  }
  if (route.query.type === "refund" && courseStore().refundInfo) {
    textarea.value = courseStore().refundInfo;
  }
});
</script>

<template>
  <div>
    <DescriptionList
      :periodId="Number(route.query.periodId)"
      @name="periodNameEvt"
    />
    <div class="free-edite">
      <div class="title">{{ route.query.text }}</div>
      <div class="text-area">
        <el-input
          v-model="textarea"
          type="textarea"
          placeholder="请输入"
          resize="none"
        />
      </div>
      <div class="buttons">
        <!-- <div class="create" @click="saveEvt">
        {{ "保存" }}
      </div>
      <div
        class="cancel"
        @click="
          router.replace({
            path: '/course/courseDetails/currentDetails',
            query: { infoShow: '价格设置', periodId: route.query.periodId }
          })
        "
      >
        取消
      </div> -->

        <el-button
          @click="
            router.replace({
              path: '/course/courseDetails/currentDetails',
              query: { infoShow: '价格设置', periodId: route.query.periodId }
            })
          "
        >
          取消
        </el-button>
        <el-button
          type="primary"
          class="editeBtn"
          :loading="submitLoading"
          @click="saveEvt"
        >
          保存
        </el-button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.free-edite {
  box-sizing: border-box;
  width: 100%;
  height: 650px;
  padding: 20px 20px;
  background-color: #fff;

  .title {
    font-size: 14px;
    margin-bottom: 20px;
  }
  .text-area {
    height: 350px;
    margin-bottom: 32px;
  }
  .buttons {
    display: flex;
    // justify-content: space-between;
    // width: 95%;
    justify-content: flex-end;
    margin: 0 auto;
    // margin-top: 28vh;

    .cancel {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100px;
      height: 36px;
      color: rgb(255 255 255 / 100%);
      cursor: pointer;
      background-color: rgb(230 152 58 / 100%);
      border-radius: 6px;
      margin-left: 58px;
    }

    .create {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100px;
      height: 36px;
      color: rgb(255 255 255 / 100%);
      cursor: pointer;
      background-color: rgb(64 149 229 / 100%);
      border-radius: 6px;
    }
  }
}
:deep(.el-textarea__inner) {
  height: 350px;
}
</style>
