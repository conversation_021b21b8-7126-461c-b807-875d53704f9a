<script setup>
import { onMounted, ref, toRaw, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  addRoleListInterFace,
  deleteRoleListInterFace,
  editRoleListInterFace,
  getRoleDetailsInterFace,
  getRoleListInterFace,
  getAuthorityInterface
} from "@/api/rightsManagement";
import { formatTime } from "@/utils/index";
import { debounce } from "@iceywu/utils";

const router = useRouter();
const route = useRoute();
// const useDateFormat = data => {
//   return data;
// };

const treeRef = ref(null); //权限节点数据
const originalRoleName = ref(""); // 用于存储原始角色名称
const originalRemark = ref(""); // 用于存储原始备注
const originalAuthorityIds = ref([]); // 用于存储原始权限ID列表
const buttonLoading = ref(false); // 按钮加载状态

//权限列表
const authorityList = ref([]);
const ruleFormRef = ref();
const rules = ref({
  name: [{ required: true, message: "请输入角色", trigger: "blur" }],
  description: [{ required: true, message: "请输入备注", trigger: "blur" }],
  authorityIds: [{ required: false, message: "", trigger: "blur" }]
});
const dialogVisible = ref(false);
const dialogOperateVisible = ref(false);
const title = ref("新增");

const isIndeterminate = ref(false);
const checkAll = ref(false);
// 全选
const handleCheckAllChange = val => {
  let checkedArr = [];
  authorityList.value.forEach(row => {
    checkedArr.push(row.id);
  });
  formLabelAlign.value.authorityIds = val ? checkedArr : [];
  isIndeterminate.value = false;
};
const handleCheckedCitiesChange = value => {
  let checkedCount = value.length;
  checkAll.value = checkedCount === authorityList.value.length;
  isIndeterminate.value =
    checkedCount > 0 && checkedCount < authorityList.value.length;
};

//弹窗表单
const formLabelAlign = ref({
  name: "", //角色
  description: "", //备注
  authorityIds: [] //角色业务类型
});

//表格数据
const tableData = ref([]);
const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});

//获取表格列表
// const pageCom = ref();
const getTableList = async () => {
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort
  };
  let res = await getRoleListInterFace(paramsData);
  if (res.code === 200) {
    tableData.value = res.data.content;
    tableData.value.forEach((item, index) => {
      item.index = index + 1;
    });
    params.value.totalElements = res.data.totalElements;
    // 翻页置顶事件
    // pageCom.value.resetScrollTop();
  }

  // console.log("paramsData", paramsData)
};
// 获取权限列表
const newauthorityList = ref("");
const getAuthorityList = async () => {
  let res = await getAuthorityInterface({ sort: "sortNumber,asc" });
  if (res.code == 200) {
    // res.data.content[0].children = [{ name: "1" }, { name: "2" }];
    authorityList.value = res.data;
    newauthorityList.value = toRaw(authorityList.value);
    newauthorityList.value = filterData(newauthorityList.value);
  }
};

function filterData(data) {
  return data
    .map(obj => {
      // 仅保留 id、name 和 children 属性
      const filteredObj = {
        id: obj.id,
        name: obj.name
      };
      // 递归处理 children 数组
      if (obj.children && obj.children.length > 0) {
        filteredObj.children = filterData(obj.children);
      }
      return filteredObj;
    })
    .filter(obj => obj.children || obj.children != []); // 删掉不含 children 属性和 children 属性为空数组的对象
}

// 删除
const deleteData = id => {
  ElMessageBox.confirm("你确定删除这一条数据吗?", "删除提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: ""
  })
    .then(async () => {
      const operateLog = {
        operateLogType: "AUTHORITY_MANAGEMENT",
        operateType: `删除了角色“${formLabelAlign.value.name}”`
      };
      let { code, msg } = await deleteRoleListInterFace({ id }, operateLog);
      if (code === 200) {
        ElMessage.success("删除成功");
        getTableList();
      } else {
        ElMessage.error(msg);
      }
    })
    .catch(() => {
      // ElMessage({
      //   type: "info",
      //   message: "取消删除"
      // });
    });
};

//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTableList();
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getTableList();
};
//打开弹窗
const addDialog = () => {
  title.value = "新增";
  formLabelAlign.value = {
    name: "", //角色
    description: "", //备注
    authorityIds: [] //角色业务类型
  };
  if (treeRef.value?.setCheckedKeys) {
    treeRef.value.setCheckedKeys([], false);
  }
  isIndeterminate.value = false;
  if (ruleFormRef.value) ruleFormRef.value.resetFields();
  dialogVisible.value = true;
};

//编辑
const operate = async id => {
  let res = await getRoleDetailsInterFace({ id });
  if (res.code === 200) {
    formLabelAlign.value = {
      ...res.data
    };
    if (res.data.authorities.length > 0) {
      formLabelAlign.value.authorityIds = res.data?.authorities;
      checkAll.value =
        formLabelAlign.value.authorityIds.length === authorityList.value.length;
      isIndeterminate.value = true;
    } else {
      formLabelAlign.value.authorityIds = [];
      isIndeterminate.value = false;
    }
    title.value = "编辑";
    // 存储原始值用于日志记录
    originalRoleName.value = res.data.name;
    originalRemark.value = res.data.description;
    originalAuthorityIds.value = res.data.authorities || [];

    dialogVisible.value = true;
  }
};

//确定事件-新增
const confirm = debounce(
  () => {
    // console.log(treeRef.value.getCheckedKeys(false),'---')
    // return
    formLabelAlign.value.authorityIds = treeRef.value.getCheckedKeys(false); //树结构数据
    if (!ruleFormRef.value) return;
    ruleFormRef.value.validate((valid, fields) => {
      if (valid) {
        buttonLoading.value = true;
        interfaceOperate();
      }
    });

    async function interfaceOperate() {
      let axios, params, msg, operateLog;

      if (formLabelAlign.value.id) {
        axios = editRoleListInterFace;
        msg = "编辑";
        params = {
          id: formLabelAlign.value.id,
          description: formLabelAlign.value.description,
          name: formLabelAlign.value.name,
          authorityIds: treeRef.value.getCheckedKeys(false)
        };
        // 比较权限ID数组是否相同（忽略顺序）
        const arraysAreEqual = (arr1, arr2) => {
          if (arr1.length !== arr2.length) return false;
          const sortedArr1 = [...arr1].sort();
          const sortedArr2 = [...arr2].sort();
          return sortedArr1.every(
            (value, index) => value === sortedArr2[index]
          );
        };

        const currentAuthorityIds = treeRef.value.getCheckedKeys(false);
        const nameChanged =
          originalRoleName.value !== formLabelAlign.value.name;
        const remarkChanged =
          originalRemark.value !== formLabelAlign.value.description;
        const authoritiesChanged = !arraysAreEqual(
          originalAuthorityIds.value,
          currentAuthorityIds
        );

        let changes = [];
        if (nameChanged) {
          changes.push(`名称修改为“${formLabelAlign.value.name}”`);
        }
        if (remarkChanged) {
          changes.push(
            `备注从“${originalRemark.value}”修改为“${formLabelAlign.value.description}”`
          );
        }
        if (authoritiesChanged) {
          changes.push("权限");
        }

        let operateTypeMessage;
        if (changes.length > 0) {
          operateTypeMessage = `编辑了角色“${nameChanged ? originalRoleName.value : formLabelAlign.value.name}”的${changes.join("，")}`;
        } else {
          operateTypeMessage = `编辑了角色“${formLabelAlign.value.name}”`;
        }

        operateLog = {
          operateLogType: "AUTHORITY_MANAGEMENT",
          operateType: operateTypeMessage
        };
      } else {
        axios = addRoleListInterFace;
        msg = "新增";
        params = formLabelAlign.value;
        params.authorityIds = treeRef.value.getCheckedKeys(false);
        params.idCode = new Date().getTime();
        operateLog = {
          operateLogType: "AUTHORITY_MANAGEMENT",
          operateType: `创建了角色“${formLabelAlign.value.name}”`
        };
      }
      console.log("🍧-----params-----", params);

      // return;
      let res = await axios(params, operateLog);
      if (res.code === 200) {
        dialogVisible.value = false;
        ElMessage.success(`${msg}成功`);
        treeRef.value.setCheckedKeys([], false);
        getTableList();
        buttonLoading.value = false;
      } else {
        ElMessage.error(res.msg);
        buttonLoading.value = false;
      }
    }
  },
  1000,
  { immediate: true }
);

const isremove = () => {
  dialogVisible.value = false;
  treeRef.value.setCheckedKeys([], false); //清空树结构数据
};

onMounted(() => {
  //获取权限列表
  getAuthorityList();
  //获取表格数据
  getTableList();
});

const open = () => {
  if (title.value == "编辑") {
    for (const key in formLabelAlign.value) {
      ruleFormRef.value.clearValidate(key, () => null);
    }
  }
};

//关闭弹窗后清空
const closed = () => {
  treeRef.value.setCheckedKeys([], false);
};
//权限树父与子的单向同步
const handleNodeCheck = (
  data,
  { checkedKeys, checkedNodes, halfCheckedKeys, halfCheckedNodes }
) => {
  const node = treeRef.value.getNode(data.id);

  // 判断当前操作的是父节点还是子节点
  if (node.isLeaf) return; // 如果是叶子节点，不需要特殊处理

  // 如果选中了父节点，则选中其所有子节点
  if (checkedKeys.includes(data.id)) {
    const getChildrenIds = children => {
      let ids = [];
      if (!children) return ids;

      children.forEach(child => {
        ids.push(child.id);
        if (child.children && child.children.length > 0) {
          ids = [...ids, ...getChildrenIds(child.children)];
        }
      });

      return ids;
    };

    const childrenIds = getChildrenIds(data.children);

    nextTick(() => {
      childrenIds.forEach(id => {
        treeRef.value.setChecked(id, true, false);
      });
    });
  }
  // 如果取消勾选父节点，则取消勾选所有子节点
  else {
    const getChildrenIds = children => {
      let ids = [];
      if (!children) return ids;

      children.forEach(child => {
        ids.push(child.id);
        if (child.children && child.children.length > 0) {
          ids = [...ids, ...getChildrenIds(child.children)];
        }
      });

      return ids;
    };

    const childrenIds = getChildrenIds(data.children);

    nextTick(() => {
      childrenIds.forEach(id => {
        treeRef.value.setChecked(id, false, false);
      });
    });
  }
};
</script>

<template>
  <div class="containers">
    <div class="con_top">
      <!-- <div class="titles">角色管理</div> -->
      <el-button type="primary" class="addDialog" @click="addDialog">
        新增
      </el-button>
    </div>

    <!-- <div class="con_search">132</div> -->
    <div class="con_table">
      <el-table
        :data="tableData"
        :header-cell-style="{ backgroundColor: '#fafafa', color: '#565353' }"
        height="670"
      >
        <el-table-column prop="id" label="序号" align="center" />
        <el-table-column prop="name" label="角色" align="center" />
        <el-table-column prop="description" label="备注" align="center" />
        <el-table-column
          prop="createdAt"
          label="创建时间"
          align="center"
          width="600"
        >
          <template #default="scope">
            <sapn>
              {{ formatTime(scope.row.createdAt, "YYYY-MM-DD hh:mm:ss") }}
            </sapn>
          </template>
        </el-table-column>
        <el-table-column
          align="left"
          label="操作"
          fixed="right"
          min-width="100"
        >
          <template #default="scope">
            <el-link
              type="primary"
              size="small"
              :underline="false"
              @click="operate(scope.row.id)"
            >
              编辑
            </el-link>
            <el-link
              v-if="![1, 2, 3].includes(scope.row.id)"
              type="danger"
              size="small"
              :underline="false"
              style="margin-left: 20px"
              @click="deleteData(scope.row.id)"
            >
              删除
            </el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="con_pagination">
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="params.page"
        v-model:page-size="params.size"
        class="pagination"
        background
        :page-sizes="[15, 30, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="params.totalElements"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <div class="dialog">
      <!--    新增 -->
      <el-dialog
        v-model="dialogVisible"
        :title="`${title}角色`"
        width="30%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @open="open"
        @closed="closed"
      >
        <el-scrollbar style="padding-right: 15px">
          <el-form
            ref="ruleFormRef"
            label-width="50px"
            :model="formLabelAlign"
            :rules="rules"
          >
            <el-form-item label="角色" prop="name">
              <el-input
                v-model="formLabelAlign.name"
                placeholder="请输入"
                maxlength="20"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="备注" prop="description">
              <el-input
                v-model="formLabelAlign.description"
                placeholder="请输入"
                maxlength="20"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="权限" prop="authorityIds">
              <!-- <el-checkbox
              :indeterminate="isIndeterminate"
              v-model="checkAll"
              @change="handleCheckAllChange"
          >全部</el-checkbox
          >
          <div style="margin: 15px 0"></div>
          <el-checkbox-group
              v-model="formLabelAlign.authorityIds"
              @change="handleCheckedCitiesChange"
          >
            <el-checkbox
                v-for="city in authorityList"
                :label="city.id"
                :key="city.id"
            >{{ city.name }}</el-checkbox
            >
          </el-checkbox-group> -->
              <el-tree
                ref="treeRef"
                style="top: 4px"
                :data="newauthorityList"
                node-key="id"
                :props="{ label: 'name', children: 'children' }"
                show-checkbox
                check-on-click-node
                highlight-current
                :render-after-expand="false"
                :check-strictly="true"
                :default-checked-keys="formLabelAlign.authorityIds"
                @check="handleNodeCheck"
              />
              <!-- :default-expanded-keys="formLabelAlign.authorityIds" -->
            </el-form-item>

            <!--        <el-form-item label="权限" prop="authorityIds"> -->
            <!--          <el-select -->
            <!--            v-model="formLabelAlign.authorityIds" -->
            <!--            placeholder="请选择" -->
            <!--            clearable -->
            <!--            multiple -->
            <!--            style="width: 100%" -->
            <!--            collapse-tags -->
            <!--            collapse-tags-tooltip -->
            <!--          > -->
            <!--            <el-option -->
            <!--              v-for="item in authorityList" -->
            <!--              :label="item.name" -->
            <!--              :value="item.id" -->
            <!--              :key="item.id" -->
            <!--            /> -->
            <!--          </el-select> -->
            <!--        </el-form-item> -->
          </el-form>
        </el-scrollbar>

        <template #footer>
          <span class="dialog-footer">
            <el-button @click="isremove">取消</el-button>
            <el-button :loading="buttonLoading" type="primary" @click="confirm">
              确定
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!--    编辑 -->
      <!--    <el-dialog -->
      <!--        v-model="dialogOperateVisible" -->
      <!--        title="权限编辑" -->
      <!--        width="30%" -->
      <!--        :close-on-click-modal="false" -->
      <!--    > -->
      <!--      <el-checkbox-group v-model="checkList"> -->
      <!--        <div><el-checkbox label="Banner管理" /></div> -->
      <!--        <div><el-checkbox label="应用管理" /></div> -->
      <!--        <div><el-checkbox label="咨询公告管理" /></div> -->
      <!--        <div><el-checkbox label="申请审批管理" /></div> -->
      <!--        <div><el-checkbox label="权限管理" /></div> -->
      <!--        <div><el-checkbox label="数据分发管理" /></div> -->
      <!--      </el-checkbox-group> -->

      <!--      <template #footer> -->
      <!--          <span class="dialog-footer"> -->
      <!--            <el-button @click="dialogVisible = false">取消</el-button> -->
      <!--            <el-button type="primary" @click="confirm('update')"> -->
      <!--              确定 -->
      <!--            </el-button> -->
      <!--          </span> -->
      <!--      </template> -->
      <!--    </el-dialog> -->
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  box-sizing: border-box;
  width: calc(100% - 48px);
  height: 100%;
  padding: 24px;
  background: #fff;

  .con_top {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    height: fit-content;
    margin-bottom: 24px;

    .titles {
      font-size: 20px;
      font-weight: bold;
      color: #606266;
    }
  }

  .con_search {
    display: flex;
    align-items: center;
    width: 100%;
    height: fit-content;
    margin-bottom: 24px;
  }

  .con_table {
    width: calc(100% - 25px);
    margin-bottom: 24px;
    // margin-left: 25px;

    .btnse {
      color: #409eff;
    }
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }
  .eye_style {
    display: flex;
    // align-items: center;
    // justify-content: center;
    .eye {
      margin-left: 20px;
      cursor: pointer;
      :hover {
        color: #409eff;
      }
    }
  }
  // .dialog {
  //   :deep(.el-dialog__body) {
  //     padding-right: 50px;
  //   }
  //   :deep(.el-dialog__footer) {
  //     // text-align: center;
  //   }
  // }
}

:deep(.el-dialog__body) {
  max-height: 40vh;
  overflow: auto;
}
</style>
