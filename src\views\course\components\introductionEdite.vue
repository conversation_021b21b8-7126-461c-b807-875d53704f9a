<script setup>
import { onMounted, ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  introductionfindById,
  nowledgefindById,
  descriptionfindById,
  precautionsfindById,
  createOrUpdate,
  nowledgeCreateOrUpdate,
  descriptionCreateOrUpdate,
  precautionsCreateOrUpdate,
  userAgreementId,
  userAgreementUpdate
} from "@/api/period.js";
import {
  draftCourseIntroductionFindByDraftId,
  saveDraftCourseIntroduction,
  saveCourseIntroductionNext,
  draftCourseKnowledgePointFindByDraftId,
  draftCourseKnowledgePoint,
  draftEquipmentDescriptionFindByDraftId,
  saveDraftEquipmentDescription,
  draftPrecautionsFindByDraftId,
  saveDraftPrecautions,
  draftUserAgreementFindByDraftId,
  saveDraftUserAgreement,
  saveDraftUserAgreementNext,
  draftDelete
} from "@/api/drafts.js";
import RichEditor from "@/components/Base/RichEditor.vue";
import { requestTo } from "@/utils/http/tool";
import { number } from "echarts";
import { ElMessage, ElMessageBox } from "element-plus";
import DescriptionList from "./descriptionList.vue";
import { to, deepClone, isEmpty } from "@iceywu/utils";
import { aiNewPage } from "@/utils/aiTool.js";
import { courseStore } from "@/store/modules/course.js";
import {
  materialValue,
  precautionsValue,
  agreementValue
} from "@/utils/defaultValue.js";
const props = defineProps({
  infoShow: {
    type: String,
    default: ""
  },
  draftId: {
    type: Number,
    default: 0
  },
  periodName: {
    type: String,
    default: "未命名"
  },
  infoShowEnName: {
    type: String,
    default: "foundation"
  }
});
const emites = defineEmits(["baseInfo"]);
const useCourseStore = courseStore();
const router = useRouter();
const route = useRoute();
const valueHtml = ref();
const operateLog = ref({});

// 判断api类型
const getApiType = (val, isEdit) => {
  let res = "";
  switch (val) {
    case "课期介绍":
      res = isEdit ? createOrUpdate : introductionfindById;
      operateLog.value = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `编辑了“${props.periodName}”课期中的课程介绍`
      };
      break;
    // case "课期知识点":
    //   res = isEdit ? nowledgeCreateOrUpdate : nowledgefindById;
    //   operateLog.value = {
    //     operateLogType: "COURSE_MANAGEMENT",
    //     operateType: `编辑了“${props.periodName}”课期中的课程知识点`
    //   };
    //   break;
    case "材料说明":
      res = isEdit ? descriptionCreateOrUpdate : descriptionfindById;
      operateLog.value = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `编辑了“${props.periodName}”课期中的材料说明`
      };
      break;
    case "注意事项":
      res = isEdit ? precautionsCreateOrUpdate : precautionsfindById;
      operateLog.value = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `编辑了“${props.periodName}”课期中的注意事项`
      };
      break;
    case "用户协议":
      res = isEdit ? userAgreementUpdate : userAgreementId;
      operateLog.value = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `编辑了“${props.periodName}”课期中的用户协议`
      };
      break;
    default:
      break;
  }
  return res;
};
const operateLogDraft = ref({});
// 判断api类型草稿箱
const getApiDraftType = (val, isEdit) => {
  let res = "";
  switch (val) {
    case "课期介绍":
      res = isEdit
        ? saveDraftCourseIntroduction
        : draftCourseIntroductionFindByDraftId;
      operateLogDraft.value = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `将“${props.periodName}”课期中的课期介绍保存在草稿箱`
      };
      break;
    // case "课期知识点":
    //   res = isEdit
    //     ? draftCourseKnowledgePoint
    //     : draftCourseKnowledgePointFindByDraftId;
    //   operateLogDraft.value = {
    //     operateLogType: "COURSE_MANAGEMENT",
    //     operateType: `将“${props.periodName}”课期中的课期知识点保存在草稿箱`
    //   };
    //   break;
    case "材料说明":
      res = isEdit
        ? saveDraftEquipmentDescription
        : draftEquipmentDescriptionFindByDraftId;
      operateLogDraft.value = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `将“${props.periodName}”课期中的材料说明保存在草稿箱`
      };
      break;
    case "注意事项":
      res = isEdit ? saveDraftPrecautions : draftPrecautionsFindByDraftId;
      operateLogDraft.value = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `将“${props.periodName}”课期中的注意事项保存在草稿箱`
      };
      break;
    case "用户协议":
      res = isEdit ? saveDraftUserAgreement : draftUserAgreementFindByDraftId;
      operateLogDraft.value = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `将“${props.periodName}”课期中的用户协议保存在草稿箱`
      };
      break;
    default:
      break;
  }
  return res;
};
// 判断日志输出
const getLogType = val => {
  let res = {};
  switch (val) {
    case "课期介绍":
      res = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `编辑了“${props.periodName}”课期中的课程介绍`
      };
      break;
    // case "课期知识点":
    //   res = {
    //     operateLogType: "COURSE_MANAGEMENT",
    //     operateType: `编辑了“${props.periodName}”课期中的课程知识点`
    //   };
    //   break;
    case "材料说明":
      res = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `编辑了“${props.periodName}”课期中的材料说明`
      };
      break;
    case "注意事项":
      res = {
        operateLogType: "COURSE_MANAGEMENT",
        operateType: `编辑了“${props.periodName}”课期中的注意事项`
      };
      break;
    default:
      break;
  }
  return res;
};

const valueHtmlClone = ref("");
// 查询详情
const getIntroductionfindById = async () => {
  const params = {
    coursePeriodId: Number(route.query.periodId)
  };
  let api = getApiType(props.infoShow, false);
  let [err, res] = await to(api(params));
  if (res.code === 200) {
    valueHtml.value = res?.data?.content || "";
    valueHtmlClone.value = deepClone(valueHtml.value);
  } else {
    console.log("🐬-----err-----", res.msg);
  }
  if (err) {
    console.log("🐬-----err-----", err);
  }
};
// 草稿箱查询详情
const getdraftfindById = async () => {
  const params = {
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId)
  };
  let api = getApiDraftType(props.infoShow, false);

  let [err, res] = await to(api(params));

  if (res.code === 200) {
    if (isEmpty(res?.data?.content)) {
      if (props.infoShow === "材料说明") {
        valueHtml.value = materialValue;
      } else if (props.infoShow === "注意事项") {
        valueHtml.value = precautionsValue;
      } else if (props.infoShow === "用户协议") {
        valueHtml.value = agreementValue;
      } else {
        valueHtml.value = "";
      }
    } else {
      valueHtml.value = res?.data?.content;
    }
    valueHtmlClone.value = deepClone(valueHtml.value);
  } else {
    console.log("🐬-----err-----", res.msg);
  }
  if (err) {
    console.log("🐬-----err-----", err);
  }
};
const submitLoading = ref(false);
// 保存
const submitForm = async val => {
  if (submitLoading.value) return;
  submitLoading.value = true;
  await saveApi(val);
  submitLoading.value = false;
};
const submitBackLoading = ref(false);
// 保存并返回
const submitFormBack = async val => {
  if (submitBackLoading.value) return;
  submitBackLoading.value = true;
  await saveApi(val);
  submitBackLoading.value = false;
};

// 保存api(编辑)
const saveApi = async val => {
  const params = {
    coursePeriodId: Number(route.query.periodId),
    content: valueHtml.value
  };
  if (valueHtml.value === "<p><br></p>") {
    ElMessage({
      type: "error",
      message: "请输入内容"
    });
    return;
  }
  operateLog.value = getLogType(props.infoShow);
  let api = getApiType(props.infoShow, true);
  let [err, res] = await requestTo(api(params, operateLog.value));
  if (res) {
    if (val) {
      if (route.query.fromPage === "courseDetail") {
        router.replace({
          path: "/course/courseDetails",
          query: { id: route.query.courseId }
        });
      } else if (route.query.fromPage === "currentDetail") {
        router.replace({
          path: "/course/courseDetails/currentDetails",
          query: {
            periodId: route.query.periodId,
            courseId: route.query.courseId
          }
        });
      }
    }
    ElMessage.success("保存成功");
  } else {
    ElMessage.error("保存失败");
    console.log("🐬-----err-----", err);
  }
};
const draftLoading = ref(false);
const saveDraftApi = async () => {
  const params = {
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
    content: valueHtml.value
  };
  let api = getApiDraftType(props.infoShow, true);
  let [err, res] = await to(api(params, operateLog.value));
  if (res.code === 200) {
    // console.log("🐠res--------保存草稿箱---------------------->", res);
    ElMessage.success("当前资料已保存到草稿箱");
  } else {
    ElMessage.error("保存失败", res.msg);
    console.log("🐬-----err-----", res.msg);

    if (err) {
      console.log("🐬-----err-----", res.msg);
    }
  }
};
// 保存草稿箱
const submitDraftForm = async () => {
  if (draftLoading.value) return;
  draftLoading.value = true;
  if (valueHtml.value === "<p><br></p>") {
    ElMessage({
      type: "error",
      message: "请输入内容"
    });
    draftLoading.value = false;
    return;
  } else {
    await saveDraftApi();
  }

  draftLoading.value = false;
};
// 上一步
const lastSubmitForm = () => {
  if (valueHtml.value !== "<p><br></p>") {
    saveDraftApi();
  }
  let info = {
    infoShow: "",
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
    periodName: props.periodName
  };
  if (props.infoShow === "课期介绍") {
    info.infoShow = "课期行程";
  }
  // else if (props.infoShow === "课期知识点") {
  //   info.infoShow = "课期介绍";
  // }
  else if (props.infoShow === "材料说明") {
    info.infoShow = "实践感悟";
  } else if (props.infoShow === "注意事项") {
    info.infoShow = "材料说明";
  } else if (props.infoShow === "用户协议") {
    info.infoShow = "注意事项";
  }
  emites("baseInfo", info);
};
const nextSubmitLoading = ref(false);
// 下一步
const nextSubmitForm = async () => {
  if (nextSubmitLoading.value) return;
  nextSubmitLoading.value = true;
  if (
    (props.infoShow === "课期介绍" && valueHtml.value === "<p><br></p>") ||
    (props.infoShow === "用户协议" && valueHtml.value === "<p><br></p>")
  ) {
    ElMessage({
      type: "error",
      message: "请输入内容"
    });
    nextSubmitLoading.value = false;
    return;
  }
  const params = {
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
    content: valueHtml.value
  };
  if (props.infoShow === "课期介绍") {
    let operateLogDraft = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: `将“${props.periodName}”课期中的课期介绍保存在草稿箱`
    };
    let [err, res] = await to(
      saveCourseIntroductionNext(params, operateLogDraft)
    );
    if (res.code === 200) {
      ElMessage.success("当前资料已保存到草稿箱");
    }
  } else if (props.infoShow === "用户协议") {
    let operateLogDraft = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: `将“${props.periodName}”课期中的用户协议保存在草稿箱`
    };
    let [err, res] = await to(
      saveDraftUserAgreementNext(params, operateLogDraft)
    );
    if (res.code === 200) {
      ElMessage.success("当前资料已保存到草稿箱");
    }
  } else {
    if (valueHtml.value !== "<p><br></p>") {
      saveDraftApi();
    }
  }

  let info = {
    infoShow: "",
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
    complete: true,
    periodName: props.periodName
  };
  if (props.infoShow === "课期介绍") {
    info.infoShow = "课期知识点";
  }
  //  else if (props.infoShow === "课期知识点") {
  //   info.infoShow = "实践感悟";
  // }
  else if (props.infoShow === "材料说明") {
    info.infoShow = "注意事项";
  } else if (props.infoShow === "注意事项") {
    info.infoShow = "用户协议";
  } else if (props.infoShow === "用户协议") {
    info.infoShow = "价格设置";
  }
  emites("baseInfo", info);
  nextSubmitLoading.value = false;
};
const btnData = ref([
  { id: 1, name: "放弃新建" },
  { id: 2, name: "保存到草稿箱" }
]);
const deleteDraft = async () => {
  let operateLogDraft = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: route.query.draftId
      ? `删除了草稿id为“${route.query.draftId}”的草稿数据`
      : `删除了草稿id为“${useCourseStore.draftId}”的草稿数据`
  };
  const [err, res] = await to(
    draftDelete(
      { id: Number(route.query.draftId) || Number(useCourseStore.draftId) },
      operateLogDraft
    )
  );
  if (res.code === 200) {
    ElMessage.success("删除成功");
  } else {
    ElMessage.error("删除失败");
  }
};
// 退出
const backEvt = (val, it) => {
  if (val === "exit") {
    let titlt =
      it.id === 1
        ? `请确认是否清除当前编辑和提交的所有资料，并删除当前编辑资料在草稿箱中对应的草稿条目`
        : `请确认是否将当前编辑和提交的所有资料保存到草稿箱，并退出编辑页面`;
    ElMessageBox.confirm(`${titlt}`, `退出并${it.name}`, {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    }).then(() => {
      if (it.id === 1) {
        deleteDraft();
        if (route.query.type === "draft") {
          router.replace("/course/drafts");
        } else if (route.query.type === "edite") {
          router.replace("/course/courseDetails");
        } else {
          router.go(-1);
        }
      } else if (it.id === 2) {
        saveDraftApi();
        if (route.query.type === "draft") {
          router.replace("/course/drafts");
        } else if (route.query.type === "edite") {
          router.replace("/course/courseDetails");
        } else {
          router.go(-1);
        }
      }
    });
  } else {
    if (route.query.fromPage === "courseDetail") {
      router.replace({
        path: "/course/courseDetails",
        query: { id: route.query.courseId }
      });
    } else if (route.query.fromPage === "currentDetail") {
      router.replace({
        path: "/course/courseDetails/currentDetails",
        query: {
          periodId: route.query.periodId,
          courseId: route.query.courseId
        }
      });
    }
  }
};

// onMounted(() => {
//   if (route.query.type === "edite") {
//     getIntroductionfindById();
//   } else {
//     getdraftfindById();
//   }
// });
watch(
  () => props.infoShow,
  () => {
    if (route.query.type === "edite") {
      getIntroductionfindById();
    } else {
      getdraftfindById();
    }
  },
  { immediate: true },
  { deep: true }
);
const submitDraftFormatt = async () => {
  const tre = ref(false);
  nextSubmitLoading.value = true;
  if (
    (props.infoShow === "课期介绍" && valueHtml.value === "<p><br></p>") ||
    (props.infoShow === "用户协议" && valueHtml.value === "<p><br></p>")
  ) {
    ElMessage({
      type: "error",
      message: "请输入内容"
    });
    tre.value = true;
    nextSubmitLoading.value = false;
    return tre.value;
  }
  // if(valueHtmlClone.value === valueHtml.value){
  //   tre.value = true;
  //   nextSubmitLoading.value = false;
  //   return tre.value;
  // }
  const params = {
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
    content: valueHtml.value
  };
  if (props.infoShow === "课期介绍") {
    let operateLogDraft = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: `将“${props.periodName}”课期中的课期介绍保存在草稿箱`
    };
    let [err, res] = await to(
      saveCourseIntroductionNext(params, operateLogDraft)
    );
    if (res.code === 200) {
      ElMessage.success("当前资料已保存到草稿箱");
      tre.value = true;
    }
  } else if (props.infoShow === "用户协议") {
    let operateLogDraft = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: `将“${props.periodName}”课期中的用户协议保存在草稿箱`
    };
    let [err, res] = await to(
      saveDraftUserAgreementNext(params, operateLogDraft)
    );
    if (res.code === 200) {
      ElMessage.success("当前资料已保存到草稿箱");
      tre.value = true;
    }
  } else {
    if (valueHtml.value !== "<p><br></p>") {
      const params = {
        draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
        content: valueHtml.value
      };
      let api = getApiDraftType(props.infoShow, true);
      let [err, res] = await to(api(params, operateLog.value));
      if (res.code === 200) {
        ElMessage.success("当前资料已保存到草稿箱");
        tre.value = true;
      } else {
        ElMessage.error("保存失败", res.msg);
        tre.value = false;
      }
    }
  }
  nextSubmitLoading.value = false;
  return tre.value;
};

// 用于草稿箱点击课期介绍校验
defineExpose({
  submitDraftFormatt
});
</script>

<template>
  <div class="introduction-edite">
    <div class="editer"><RichEditor v-if="infoShow" v-model="valueHtml" /></div>
    <div class="buttons">
      <div v-if="route.query.type === 'edite'" class="left">
        <el-button class="cancel" @click="backEvt('back')"> 返回 </el-button>
        <el-button
          type="primary"
          class="create"
          :loading="submitBackLoading"
          @click="submitFormBack(true)"
        >
          {{ "保存并返回" }}
        </el-button>
        <el-button
          type="primary"
          class="create"
          :loading="submitLoading"
          @click="submitForm(false)"
        >
          {{ "保存" }}
        </el-button>
      </div>
      <div v-else class="left">
        <!-- <el-button class="cancel" @click="backEvt('exit')"> 退出 </el-button> -->
        <el-dropdown>
          <el-button style="margin-right: 10px">退出</el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="it in btnData"
                :key="it.id"
                @click="backEvt('exit', it)"
              >
                {{ it.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button
          type="primary"
          class="create"
          :loading="draftLoading"
          @click="submitDraftForm(ruleFormRef)"
        >
          {{ "保存草稿箱" }}
        </el-button>
        <el-button
          type="primary"
          class="create"
          :loading="submitLoading"
          @click="lastSubmitForm(ruleFormRef)"
        >
          {{ "上一步" }}
        </el-button>
        <el-button
          type="primary"
          class="create"
          :loading="nextSubmitLoading"
          @click="nextSubmitForm(ruleFormRef)"
        >
          {{ "下一步" }}
        </el-button>
      </div>
      <div class="right">
        <el-button
          type="primary"
          class="create"
          @click="aiNewPage(infoShowEnName)"
        >
          {{ "AI课程设计" }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.introduction-edite {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  position: relative;
  padding: 10px 0 0 0;
  background-color: #fff;
  display: flex;
  .editer {
    width: 100%;
    height: calc(100% - 40px);
    overflow-y: auto;
  }
  .buttons {
    display: flex;
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    justify-content: space-between;
  }
}
:deep(.w-e-text-container) {
  // height: 500px;
  //   width: 50%;
  // height: 50vh;
}
:deep(.w-e-text-placeholder) {
  top: 6px;
  left: 14px;
}
:deep(.el-button:focus-visible) {
  display: none;
}
</style>
