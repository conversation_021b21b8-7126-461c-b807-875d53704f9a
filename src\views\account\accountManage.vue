<script setup>
import { ref, onMounted, onActivated, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import { requestTo } from "@/utils/http/tool";
import { formatTime } from "@/utils/index";
import { ElMessage, ElMessageBox } from "element-plus";
import { accountAll, isaccFreeze, roleList } from "@/api/leaderLecturer.js";
import { decrypt, encryption } from "@/utils/SM4.js";
import { View, Hide } from "@element-plus/icons-vue";

defineOptions({
  name: "AccountManage"
});
const router = useRouter();
const route = useRoute();
onMounted(() => {
  getTableList();
  getroleList(); //获取角色列表(不分页)
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
  form.value.freeze = "all";
  form.value.roleId = "all";
});
onActivated(() => {
  getTableList();
  getroleList(); //获取角色列表(不分页)
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});

// 计算表格高度
const searchFormHeight = ref(0);
const tableHeight = ref("calc(100vh - 350px)");

const calculateTableHeight = async () => {
  await nextTick();
  const searchForm = document.querySelector(".con_search");
  if (searchForm) {
    searchFormHeight.value = searchForm.offsetHeight;
    // 动态计算表格高度，减去搜索框高度、表头按钮高度、分页器高度和其他间距
    tableHeight.value = `calc(100vh - 260px - ${searchFormHeight.value}px)`;
  }
};

// 搜索表单
const form = ref({
  startTime: "",
  endTime: "",
  name: "",
  phone: "",
  roleId: "all",
  freeze: "all"
});
// 获取角色列表(不分页)
const roleLists = ref([]);
const getroleList = async () => {
  const [err, result] = await requestTo(roleList());
  if (result) {
    // 处理数据
    const roleData = result.map(role => ({
      id: role.id,
      list_name: role.name
    }));
    // 添加"全部"选项
    roleLists.value = [
      {
        id: "all",
        list_name: "全部"
      },
      ...roleData
    ];
    console.log("🐳-----roleList-----", roleLists.value);
  }
};

const freezeLists = ref([
  {
    status: "all",
    list_name: "全部"
  },
  {
    status: false,
    list_name: "正常"
  },
  {
    status: true,
    list_name: "冻结"
  }
]);

// 表格数据
const tableData = ref([
  // {
  //   id: 0,
  //   createdAt: 0,
  //   updatedAt: 0,
  //   name: "1223",
  //   organizationName: "dd ",
  //   courseTypeName: "fff",
  //   termNumber: 0
  // }
]);
const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (
        form.value[paramsDataKey] !== "" &&
        form.value[paramsDataKey] !== null &&
        form.value[paramsDataKey] !== undefined
      ) {
        // 跳过freeze=all和roleId=all的情况，不发送该参数
        if (
          (paramsDataKey === "freeze" && form.value[paramsDataKey] === "all") ||
          (paramsDataKey === "roleId" && form.value[paramsDataKey] === "all")
        ) {
          continue;
        }
        // 仅对 phone 进行加密
        paramsData[paramsDataKey] =
          paramsDataKey === "phone"
            ? encryption(String(form.value[paramsDataKey]))
            : form.value[paramsDataKey];
      }
    }
  }
  // console.log("🍧-----paramsData-----", paramsData);
  // return;
  const [err, result] = await requestTo(accountAll(paramsData));
  // console.log("🎁-账号管理----result-----", result);
  if (result) {
    result?.content.forEach(item => {
      item.show_phone = false;
      // item.show_card = false;
    });
    tableData.value = result?.content;
    params.value.totalElements = result.totalElements;
  } else {
    ElMessage.error(err);
  }
  getListLoading.value = false;
};
const eye_phone = id => {
  const item = tableData.value.find(item => item.id === id);
  if (item) {
    item.type_phone = !item.type_phone;
  }
};
// const eye_card = id => {
//   const item = tableData.value.find(item => item.id === id);
//   if (item) {
//     item.show_card = !item.show_card;
//   }
// };

//搜索
const searchData = () => {
  params.value.page = 1;
  getTableList();
};
// 选择时间
const timeChange = e => {
  form.value.startTime = e ? new Date(e[0])?.getTime() : "";
  form.value.endTime = e ? new Date(e[1])?.getTime() + (******** - 1) : "";
};

const value1 = ref([]);
// 重置
const setData = () => {
  form.value = {
    startTime: "",
    endTime: "",
    name: "",
    phone: "",
    roleId: "all",
    freeze: "all"
  };
  params.value.page = 1;
  value1.value = [];
  getTableList();
};
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTableList();
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getTableList();
};
// 账号详情
const getInfoid = row => {
  router.push({
    path: "/account/accountDetails",
    query: { title: "jsMang", id: row.id }
  });
};
// 创建账号
const accCreate = () => {
  router.push({
    path: "/account/accountCreate",
    query: { istitle: false }
  });
};
// 冻结/解冻
const getButtonText = isPub => {
  return isPub === true ? "解冻" : "冻结";
};

// 添加一个标记，防止重复提交
const isSubmitting = ref(false);

const Freeze = async row => {
  const isFreezing = !row.freeze;
  const confirmText = isFreezing
    ? "你确定要冻结该账户吗?"
    : "你确定要解冻该账户吗?";
  const confirmTitle = isFreezing ? "确认冻结" : "确认解冻";
  const successMessage = isFreezing ? "已冻结" : "已解冻";

  try {
    await ElMessageBox.confirm(confirmText, confirmTitle, {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: ""
    });

    // 如果已经在提交中，则不再重复发起请求
    if (isSubmitting.value) return;
    isSubmitting.value = true;

    const params = {
      id: row.id,
      freeze: isFreezing
    };
    const operateLog = {
      operateLogType: "ACCOUNT_MANAGEMENT",
      operateType: isFreezing ? "冻结了" : "解冻了",
      operatorTarget: row.name
    };

    try {
      const { code, msg } = await isaccFreeze(params, operateLog);
      if (code === 200) {
        ElMessage({
          message: successMessage,
          type: "success"
        });

        getTableList();
      } else {
        ElMessage.error(msg);
      }
    } finally {
      // 无论请求成功或失败，都重置提交状态
      isSubmitting.value = false;
    }
  } catch (error) {
    // console.log("操作取消");
  }
};
</script>

<template>
  <div class="containers">
    <div class="con_top">
      <!-- <div class="titles">账号管理</div> -->
    </div>
    <div class="con_search">
      <el-form :model="form" :inline="true" class="search-form">
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="value1"
            type="daterange"
            start-placeholder="请选择开始时间"
            end-placeholder="请选择结束时间"
            style="width: 280px"
            @change="timeChange"
          />
        </el-form-item>
        <el-form-item label="姓名">
          <el-input
            v-model.trim="form.name"
            placeholder="请输入"
            clearable
            style="width: 140px"
          />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input
            v-model="form.phone"
            placeholder="请输入"
            clearable
            style="width: 140px"
            @input="form.phone = form.phone.replace(/\D/g, '')"
          />
        </el-form-item>
        <el-form-item label="角色">
          <el-select
            v-model="form.roleId"
            placeholder="请选择"
            clearable
            style="width: 140px"
            @clear="form.roleId = 'all'"
          >
            <el-option
              v-for="role in roleLists"
              :key="role.id"
              :label="role.list_name"
              :value="role.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="账号状态">
          <el-select
            v-model="form.freeze"
            placeholder="请选择"
            clearable
            style="width: 140px"
            @clear="form.freeze = 'all'"
          >
            <el-option
              v-for="freeze in freezeLists"
              :key="freeze.status"
              :label="freeze.list_name"
              :value="freeze.status"
            />
          </el-select>
        </el-form-item>

        <el-form-item class="search-buttons">
          <el-button type="primary" @click="searchData">搜索</el-button>
          <el-button @click="setData">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="con_table">
      <div class="btns">
        <el-button type="primary" @click="accCreate">新建账号</el-button>
      </div>
      <el-table
        :data="tableData"
        table-layout="fixed"
        :header-cell-style="{ backgroundColor: '#fafafa', color: '#565353' }"
        highlight-current-row
        :max-height="tableHeight"
      >
        <el-table-column prop="id" label="账号ID">
          <template #default="scope">
            <el-text>
              {{ scope.row.id || "暂无" }}
            </el-text>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="姓名" align="left">
          <template #default="scope">
            <div>
              {{ scope.row.name || "暂无" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="phone"
          label="手机号"
          align="left"
          min-width="132"
        >
          <template #default="scope">
            <div class="eye_style">
              {{
                scope.row.phone
                  ? scope.row.type_phone
                    ? decrypt(scope.row.phoneCt)
                    : scope.row.phone
                  : "-"
              }}
              <div
                v-if="scope.row.phone"
                class="eye"
                @click="eye_phone(scope.row.id, scope.row.phoneCt)"
              >
                <el-icon v-if="!scope.row.type_phone"><Hide /></el-icon>
                <el-icon v-else><View /></el-icon>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="account" label="账号" align="left">
          <template #default="scope">
            <div>
              {{ scope.row.account || "暂无" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="roles" label="角色">
          <template #default="scope">
            <template v-if="scope.row.roles?.length">
              <div
                v-for="(item, index) in scope.row.roles"
                :key="index"
                style="display: inline-block"
              >
                {{ item.name || "--" }}
                <span v-if="index !== scope.row.roles.length - 1">、</span>
              </div>
            </template>
            <span v-else>暂无</span>
          </template>
        </el-table-column>

        <el-table-column width="200px" prop="createdAt" label="创建时间">
          <template #default="scope">
            <div>
              {{
                formatTime(scope.row?.createdAt, "YYYY-MM-DD HH:mm") || "暂无"
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="freeze" label="账号状态">
          <template #default="scope">
            <div
              :style="{
                color: scope.row.freeze === true ? 'red' : ''
              }"
            >
              {{ scope.row.freeze === true ? "冻结" : "正常" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="address"
          fixed="right"
          label="操作"
          align="left"
          min-width="104"
        >
          <template #default="scope">
            <div class="operate">
              <el-button type="primary" link @click="getInfoid(scope.row)">
                详情
              </el-button>
              <el-button
                type="primary"
                link
                :style="{
                  color: scope.row.freeze === true ? 'red' : '#409EFF'
                }"
                @click="Freeze(scope.row)"
              >
                {{ getButtonText(scope.row.freeze) }}
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="con_pagination">
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="params.page"
          v-model:page-size="params.size"
          class="pagination"
          background
          :page-sizes="[15, 30, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="params.totalElements"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  display: flex;
  flex-direction: column;
  height: 89vh;
  overflow: hidden;
}

.containers {
  box-sizing: border-box;

  // width: calc(100% - 48px);
  // height: 100%;
  // padding: 24px 24px 24px 0;
  // background: #fff;

  // .con_top {
  //   display: flex;
  //   align-items: center;
  //   justify-content: space-between;
  //   width: 100%;
  //   height: fit-content;
  //   margin: 0 0 24px 24px;

  //   .titles {
  //     font-size: 20px;
  //     font-weight: bold;
  //     color: #606266;
  //   }
  // }

  .con_search {
    // display: flex;
    // align-items: center;
    // width: 100%;
    // height: fit-content;
    // margin-bottom: 24px;
    padding: 20px 20px 2px 20px;
    background-color: #fff;
    margin-bottom: 20px;
    overflow: auto;

    .search-form {
      display: flex;
      flex-wrap: wrap;
      gap: 5px 10px;

      :deep(.el-form-item) {
        margin-right: 0;
        margin-bottom: 15px;
      }

      .search-buttons {
        margin-left: auto;
        align-self: flex-end;
        margin-bottom: 15px;
      }
    }
  }

  .con_table {
    // width: calc(100% - 25px);
    // margin-bottom: 24px;
    // margin-left: 25px;
    background: #fff;
    padding: 20px;
    box-sizing: border-box;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .btns {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      margin-bottom: 20px;
    }

    .el-table {
      flex: 1;
      overflow: auto;
    }
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    margin-top: 20px;
    flex-wrap: wrap;

    :deep(.el-pagination) {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-end;
      row-gap: 10px;

      .el-pagination__sizes,
      .el-pagination__jump {
        margin-bottom: 0;
      }
    }
  }
  .eye_style {
    width: 120px;
    // align-items: center;
    // justify-content: center;
    .eye {
      float: right;
      cursor: pointer;
      :hover {
        color: #409eff;
      }
    }
  }
}
</style>
