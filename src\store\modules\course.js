import { defineStore } from "pinia";
import { ref, computed } from "vue";
import { getObjVal } from "@iceywu/utils";
export const courseStore = defineStore(
  "course",
  () => {
    // 课程信息
    const courseInfo = ref({});
    // 费用信息
    const freeInfo = ref("");
    // 退款信息
    const refundInfo = ref("");
    // 学生整体表现评价
    const evaluate = ref("");
    // 教学成果总结
    const result = ref("");
    // 课期是否为下架状态（或未上架）
    const periodStateDown = ref(false);
    // 课期是否为完成状态
    const periodComplete = ref(false);
    // 领队信息
    const leaderInfo = ref([]);
    // 讲师信息
    const lecturerInfo = ref([]);
    //  实践点信息
    const baseInfo = ref({});
    // 课期名称
    const periodName = ref("");
    // 人数上限
    const periodMaxPeopleNumber = ref("");
    // 草稿箱课程信息
    const courseBaseDraft = ref({});
    // 课期标签
    const periodTags = ref([]);
    // 课期前缀
    const periodPrefix = ref("");
    // 课期后缀
    const periodSuffix = ref("");
    // 课期前缀规则
    const prefixRule = ref("");
    // 课期后缀规则
    const suffixRule = ref("");
    // 课期前缀选项
    const periodPrefixOptions = ref([]);
    // 课期后缀选项
    const periodSuffixOptions = ref([]);
    // 草稿iddraftId
    const draftId = ref();
    // 打开复制课程图片开关
    const openCourseImg = ref();
    // 课期师资信息
    const teachersInfo = ref([]);
    const periodEditePreInfo = ref({}); //课期编辑前后缀信息
    const courseCreate = ref(""); //点击面包屑新增课程是否从储存中获取数据
    // 点击面包屑新增课程是否从储存中获取数据
    const saveCreateInfo = data => {
      courseCreate.value = data;
    };
    // 存储师资信息
    const saveteachersInfo = data => {
      teachersInfo.value = data;
    };
    // 存储课程信息
    const saveCourseInfo = data => {
      courseInfo.value = data;
    };
    // 存储费用信息
    const saveFreeInfo = data => {
      freeInfo.value = data;
    };
    // 存储退款信息
    const saveRefundInfo = data => {
      refundInfo.value = data;
    };
    // 存储学生整体表现评价
    const saveEvaluate = data => {
      evaluate.value = data;
    };
    // 存储教学成果总结
    const saveResult = data => {
      result.value = data;
    };
    // 存储实践点信息
    const saveBaseInfo = data => {
      baseInfo.value = data;
    };
    // 存储讲师信息
    const saveLecturerInfo = data => {
      lecturerInfo.value = data;
    };
    // 存储领队信息
    const saveLeaderInfo = data => {
      leaderInfo.value = data;
    };
    // 存储课期名称
    const savePeriodName = data => {
      periodName.value = data;
    };
    // 存储人数上限
    const savePeriodMaxPeopleNumber = data => {
      periodMaxPeopleNumber.value = data;
    };
    // 存储课期前缀
    const savePeriodPrefix = data => {
      periodPrefix.value = data;
    };
    // 存储课期后缀
    const savePeriodSuffix = data => {
      periodSuffix.value = data;
    };
    // 存储课期前缀规则
    const savePeriodPrefixRule = data => {
      prefixRule.value = data;
    };
    // 存储课期后缀规则
    const savePeriodSuffixRule = data => {
      suffixRule.value = data;
    };
    // 存储课期前缀选项
    const savePeriodPrefixOptions = data => {
      periodPrefixOptions.value = data;
    };
    // 存储课期后缀选项
    const savePeriodSuffixOptions = data => {
      periodSuffixOptions.value = data;
    };
    // 存储课期标签
    const savePeriodTags = data => {
      periodTags.value = data;
    };
    // 存储草稿id
    const saveDraftId = data => {
      draftId.value = data;
    };
    // 存储打开复制课程图片开关
    const saveOpenCourseImg = data => {
      openCourseImg.value = data;
    };
    // 封面图
    const coverInfo = ref([]);
    // 开课时间设置表格数据
    const periodTableData = ref([]);

    // 新增用于 periodinfoedite.vue 的 state
    const periodCourseTypeId = ref(""); // 课程类型ID
    const periodMinPeopleNumber = ref(""); // 人数下限
    const periodOpenDate = ref(null); // 开课日期 (时间戳)
    const periodOpenTimeValue = ref(null); // 开课时间选择器原始值 (时间戳)
    const periodSignUpDeadlineDate = ref(null); // 停止报名日期 (时间戳)
    const periodColseTimeValue = ref(null); // 停止报名时间选择器原始值 (时间戳)
    const saveCourseBaseDraft = data => {
      courseBaseDraft.value = data;
    };
    const saveCoverInfo = data => {
      coverInfo.value = data;
    };

    const savePeriodTableData = data => {
      periodTableData.value = data;
    };

    // 新增用于 periodinfoedite.vue 的 actions
    const savePeriodCourseTypeId = data => {
      periodCourseTypeId.value = data;
    };
    const savePeriodMinPeopleNumber = data => {
      periodMinPeopleNumber.value = data;
    };
    const savePeriodOpenDate = data => {
      periodOpenDate.value = data;
    };
    const savePeriodOpenTimeValue = data => {
      periodOpenTimeValue.value = data;
    };
    const savePeriodSignUpDeadlineDate = data => {
      periodSignUpDeadlineDate.value = data;
    };
    const savePeriodColseTimeValue = data => {
      periodColseTimeValue.value = data;
    };
    const savePeriodEditePreInfo = data => {
      periodEditePreInfo.value = data;
    };

    // 课期上下架状态
    const savePeriodState = data => {
      if (data && (data === "OFFLINE" || data === "NOT_LISTED")) {
        periodStateDown.value = true;
      } else {
        periodStateDown.value = false;
      }
      if (data && data === "COMPLETED") {
        periodComplete.value = true;
      } else {
        periodComplete.value = false;
      }
    };
    return {
      courseInfo,
      saveCourseInfo,
      freeInfo,
      refundInfo,
      saveFreeInfo,
      saveRefundInfo,
      saveResult,
      saveEvaluate,
      result,
      evaluate,
      savePeriodState,
      periodStateDown,
      periodComplete,
      saveBaseInfo,
      saveLecturerInfo,
      saveLeaderInfo,
      savePeriodName,
      savePeriodMaxPeopleNumber,
      baseInfo,
      leaderInfo,
      lecturerInfo,
      periodName,
      periodMaxPeopleNumber,
      coverInfo,
      saveCoverInfo,
      periodTableData,
      savePeriodTableData,
      // 暴露新增的 state 和 actions
      periodCourseTypeId,
      savePeriodCourseTypeId,
      periodMinPeopleNumber,
      savePeriodMinPeopleNumber,
      periodOpenDate,
      savePeriodOpenDate,
      periodOpenTimeValue,
      savePeriodOpenTimeValue,
      periodSignUpDeadlineDate,
      savePeriodSignUpDeadlineDate,
      periodColseTimeValue,
      savePeriodColseTimeValue,
      courseBaseDraft,
      saveCourseBaseDraft,
      savePeriodTags,
      savePeriodSuffix,
      savePeriodPrefix,
      periodTags,
      periodPrefix,
      periodSuffix,
      savePeriodPrefixRule,
      savePeriodSuffixRule,
      suffixRule,
      prefixRule,
      savePeriodSuffixOptions,
      savePeriodPrefixOptions,
      periodSuffixOptions,
      periodPrefixOptions,
      draftId,
      saveDraftId,
      saveOpenCourseImg,
      openCourseImg,
      teachersInfo,
      saveteachersInfo,
      savePeriodEditePreInfo,
      periodEditePreInfo,
      courseCreate,
      saveCreateInfo
    };
  },
  {
    persist: {
      key: "courseInfo"
    }
  }
);
