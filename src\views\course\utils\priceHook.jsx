import { Edit } from "@element-plus/icons-vue";
import { fileDeleteById } from "@/api/files";
import ImgBlurHash from "@/components/ImgBlurHash";
import { message } from "@/utils/message";
import dayjs from "dayjs";
import { onMounted, reactive, ref } from "vue";
import { uploadFile } from "@/utils/upload/upload.js";
// import {
//   courseTypeSave,
//   courseTypeMove,
//   courseTypeDelete,
//   courseTypeFindAll,
//   courseTypeUpdate
// } from "@/api/courseClassify.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox, ElInputNumber } from "element-plus";
import { to, deepClone } from "@iceywu/utils";

export function usePriceSet() {
  onMounted(() => {});
  const oldData = ref({
    content: [],
    header: []
  });
  const list = ref([
    {
      name: "规格",
      id: "1",
      children: [
        {
          name: "默认",
          id: "1-1"
        }
      ]
    }
  ]);
  const getColumsData = data => {
    const targetList = data || list.value;
    const baseList = deepClone(list.value);
    const baseColumns = baseList.map(item => {
      return {
        label: item.name,
        prop: item.name,
        key: item.name,
        dataKey: item.name,
        width: 150
      };
    });

    const columns = [
      {
        label: "序号",
        type: "index",
        width: 60,
        key: "index",
        dataKey: "index",
        cellRenderer: ({ rowIndex }) => <span>{rowIndex + 1}</span>
      },
      ...baseColumns,
      {
        label: "价格（元）",
        prop: "price",
        key: "price",
        dataKey: "price",
        cellRenderer: ({ rowData }) => {
          if (rowData.price === undefined || rowData.price === null) {
            rowData.price = 0;
          }
          return (
            <ElInputNumber
              modelValue={rowData.price}
              min={0}
              max={99999}
              precision={2}
              onChange={val => {
                rowData.price = val;
              }}
            />
          );
        },
        width: 150
      },
      {
        label: "数量",
        prop: "quantity",
        key: "quantity",
        dataKey: "quantity",
        cellRenderer: ({ rowData }) => {
          if (rowData.quantity === undefined || rowData.quantity === null) {
            rowData.quantity = 1;
          }
          return (
            <ElInputNumber
              modelValue={rowData.quantity}
              min={1}
              step={1}
              max={99999}
              precision={0}
              onChange={val => {
                rowData.quantity = val;
              }}
            />
          );
        },
        width: 150
      }
    ];
    return columns;
  };
  // 数据赋值
  const setData = (data = [], price = []) => {
    const nestList = [];
    if (data.length > 0) {
      for (let i = 0; i < data.length; i++) {
        const item = data[i];
        const itemList = [];
        for (let j = 0; j < item.specifications.length; j++) {
          const item2 = item.specifications[j];
          itemList.push({
            name: item2.name,
            id: item2.id
          });
        }
        nestList.push({
          name: item.name,
          id: item.id,
          children: itemList
        });
      }
    }

    list.value = nestList;
  };
  const getValList = () => {
    const valList = deepClone(list.value);
    // 返回children数据
    const childrenList = [];
    for (let i = 0; i < valList.length; i++) {
      const children = valList[i].children || [];
      if (children.length > 0) {
        childrenList.push(...children);
      }
    }

    return childrenList;
  };

  return {
    list,
    getColumsData,
    setData,
    getValList
  };
}
