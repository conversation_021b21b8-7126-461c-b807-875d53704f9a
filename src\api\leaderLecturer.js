import { http } from "@/utils/http";
// 领队讲师/列表查询
export const ledeterAll = params => {
  return http.request(
    "get",
    "/organization/leaderLecturer/findAll",
    { params },
    { isNeedEncrypt: true }
  );
};
// 领队讲师/列表详情
export const ledeterList = params => {
  return http.request(
    "get",
    "/organization/leaderLecturer/findById",
    { params },
    { isNeedEncrypt: true }
  );
};
// 领队讲师/是否冻结
export const isFreeze = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/leaderLecturer/isFreeze",
    { data },
    {
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 领队讲师/详情课期
export const ledeterinfoList = params => {
  return http.request(
    "get",
    "/organization/leaderLecturer/findByCoursePeriod",
    { params },
    { isNeedEncrypt: true }
  );
};
// 领队讲师/角色查询
export const roleList = params => {
  return http.request(
    "get",
    "/organization/role/findAllNotPage",
    { params },
    { isNeedEncrypt: true },
    { isNeedToken: true }
  );
};
// 领队讲师/编辑信息
export const editteaInfo = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/admin/update",
    { data },
    {
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 根据机构管理员id查询领队资质文件
export const fileLeaderList = params => {
  return http.request(
    "get",
    "/organization/leaderLecturer/findLeaderByOrganizationAdminId",
    { params },
    { isNeedEncrypt: true },
    { isNeedToken: true }
  );
};
// 根据机构管理员id查询讲师资质文件
export const fileLeatureList = params => {
  return http.request(
    "get",
    "/organization/leaderLecturer/findLecturerByOrganizationAdminId",
    { params },
    { isNeedEncrypt: true },
    { isNeedToken: true }
  );
};
// 账号-账号管理/列表查询
export const accountAll = params => {
  return http.request(
    "get",
    "/organization/admin/findAll",
    { params },
    { isNeedEncrypt: true }
  );
};
// 账号管理/创建账号
export const addaccadmin = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/admin/save",
    { data },
    {
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 账号管理/列表详情
export const getaccInfo = params => {
  return http.request(
    "get",
    "/organization/admin/findById",
    { params },
    { isNeedEncrypt: true }
  );
};
// 账号管理/编辑信息
export const editaccInfo = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/admin/update",
    { data },
    {
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 账号管理/是否冻结
export const isaccFreeze = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/admin/isFreeze",
    { data },
    {
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 手机号表单验证
export const verifyPhone = data => {
  return http.request(
    "post",
    "/organization/verifyPhone",
    { data },
    { isNeedEncrypt: true }
  );
};
// 获取手机号验证码
export const getPhonecode = data => {
  return http.request(
    "post",
    "/common/verificationCode/generateCode",
    { data },
    { isNeedEncrypt: true }
  );
};
// 验证该管理员是否可以取消该角色
export const verifyCheck = data => {
  return http.request(
    "post",
    "/organization/admin/verifyRoleUpdate",
    { data },
    { isNeedEncrypt: true }
  );
};
// 查询管理员不能取消的角色
export const getNonCancell = params => {
  return http.request(
    "get",
    "/organization/admin/getNonCancellableRoleIds",
    { params },
    { isNeedEncrypt: true }
  );
};
/**批量赋予角色 */
export const batchAssignRole = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/admin/batchAssignRole",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
