import { http } from "@/utils/http";

/** 登录 */
export const getLogin = data => {
  return http.request("post", "/login", { data }, { isNeedToken: false });
};

/** 刷新`token` */
export const refreshTokenApi = data => {
  return http.request("post", "/refresh-token", { data });
};

/** 账户设置-个人信息 */
export const getMine = data => {
  return http.request("get", "/mine", { data });
};

/** 账户设置-个人安全日志 */
export const getMineLogs = data => {
  return http.request("get", "/mine-logs", { data });
};

// 手机号验证码获取机构账号
export const getLoginPhone = data => {
  return http.request(
    "post",
    "/organization/admin/findByPhoneCode",
    { data },
    {
      isNeedToken: false
    }
  );
};

// 根据id登录
export const getLoginById = data => {
  return http.request(
    "post",
    "/organization/admin/loginById",
    { data },
    {
      isNeedToken: false
    }
  );
};

// 根据code查询已绑定账号
export const getWxLogin = data => {
  return http.request(
    "post",
    "/organization/admin/findUserByCode",
    { data },
    {
      isNeedToken: false,
      isNeedEncrypt: true
    }
  );
};

// 根据code绑定微信
export const getBindCode = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/admin/bindPhone",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

// 解绑微信
export const unbindWxCode = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/admin/unbind",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
